import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Card,
  CardContent,
  CardMedia,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  Input,
  Radio,
  RadioGroup,
  Stack,
  Switch,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme
} from '@mui/material';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import AssignmentOutlinedIcon from '@mui/icons-material/AssignmentOutlined';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import LoadingButton from '@mui/lab/LoadingButton';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../redux/reducers';
import { AppDispatch, AppState } from '../../redux/app.store';
import {
  autocreate,
  // autocreatewithpdf,
  clearAppState
} from '../../redux/reducers/apps.reducer';

import '../../css/create-form-styles.scss';
import '../../css/index.scss';
import { CreateFormTypes } from '../../types';
// import { toast } from 'react-toastify';
// import SuccessDialog from './SuccessDialog';

export const CreateForm = ({ appId, appData }: CreateFormTypes) => {
  const navigate = useNavigate();
  const { userType } = useSelector((state: RootState) => state.auth);
  const { isLoading, getApp }: any = useSelector(
    (state: AppState) => state.app
  );
  const dispatch = useDispatch<AppDispatch>();

  const [openFormDialog, setOpenFormDialog] = useState(false);
  const [nameErr, setNameErr] = useState<boolean>(false);
  const [isToggled, setIsToggled] = useState<boolean>(false);
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('sm'));
  // const [openSuccess, setOpenSuccess] = useState(false);
  // const [successMessage, setSuccessMessage] = useState('');
  // const [createFormData, setCreateFormData]: any = useState();
  // Clear app state on unmount
  useEffect(() => {
    return () => {
      clearAppState();
    };
  }, []);

  const handleFormDialogOpen = () => {
    setOpenFormDialog(true);
  };

  const handleClose = () => {
    setOpenFormDialog(false);
  };

  // This is a placeholder list if needed for selected apps.
  const selectedAppsList: any = [
    // { name: 'Mandatory', url: '/icon.svg' }, etc.
  ];
  const handleToggleChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setIsToggled(event.target.checked);
  };

  // const onClose = () => {
  //   setOpenSuccess(false);
  //   const data = createFormData;

  //   if (data?.has_sub_forms) {
  //     if (userType === 'super_admin') {
  //       navigate(`sub-forms/${data?.app_id}/${data?.form_id}`);
  //     } else {
  //       navigate(`sub-forms/${data?.app_id}/${data?.form_id}`);
  //     }
  //   } else {
  //     if (userType === 'super_admin') {
  //       navigate(`/apps/form-builder/edit-form/${data?.form_id}`);
  //     }
  //     navigate(`/form-builder/edit-form/${data?.form_id}`);
  //   }
  // };

  return (
    <Box>
      <Box className="d-flex justify-content-space-between align-items-center w-full">
        <Box className="d-flex align-items-center gap-10">
          <Typography
            className="font-size-24 textTransform-capitalize"
            sx={{ color: '#595959' }}
          >
            Forms List
          </Typography>
        </Box>

        <Box sx={{ padding: '0px' }}>
          <Tooltip title="Create a new form" arrow>
            <Button
              size="medium"
              className="color-white align-items-center d-flex"
              sx={{
                p: '10px 40px',
                border: '2px solid',
                backgroundColor: '#08366B',
                color: 'white',
                '& .MuiSvgIcon-root': { color: 'white' },
                '&:hover': { backgroundColor: '#08363B' },
                borderRadius: '50px'
              }}
              onClick={handleFormDialogOpen}
            >
              <AddCircleOutlineIcon
                fontSize="small"
                sx={{ color: '#F9F9F9', margin: '0px 5px' }}
              />
              Create
            </Button>
          </Tooltip>

          <Dialog
            fullScreen={fullScreen}
            open={openFormDialog}
            onClose={handleClose}
            aria-labelledby="customized-dialog-title"
            sx={{
              '& .MuiDialog-paper': {
                borderRadius: '20px',
                padding: '20px',
                backgroundImage: 'linear-gradient(to right, #ece9e6, #ffffff)',
                textTransform: 'capitalize'
              }
            }}
            PaperProps={{
              component: 'form',
              onSubmit: async (event: React.FormEvent<HTMLFormElement>) => {
                event.preventDefault();
                const formData = new FormData(event.currentTarget);
                const formJson: any = Object.fromEntries(formData.entries());
                // Regex to allow names between 3 to 50 characters, letters/numbers & spaces (excluding special characters)
                // const nameregx = /^(?=.{3,50}$)[^\W_\/]+(?:[ \/][^\W_\/]+)*$/;
                const nameRegEx = /^(?=.{3,50}$)[^\W_]+(?:[ /][^\W_]+)*$/;
                formJson.name = formJson.name?.trim();
                if (nameRegEx.test(formJson.name)) {
                  setNameErr(false);

                  const data = {
                    app_id: appId,
                    name: formJson.name,
                    is_quiz_form: formJson?.isQuizForm === 'quizForm' || false,
                    has_sub_forms: formJson?.hasSubForm === 'subForm' || false
                  };

                  await dispatch(autocreate(data)).then(() => {
                    setOpenFormDialog(false);
                  });
                } else {
                  setNameErr(true);
                }
              }
            }}
          >
            <DialogTitle
              id="customized-dialog-title"
              sx={{
                fontSize: '24px',
                fontWeight: 'bold',
                textAlign: 'center',
                color: '#08366B'
              }}
            >
              Create New Form
            </DialogTitle>
            <DialogContent dividers>
              {/* <DialogContentText
                sx={{
                  fontSize: '18px',
                  textAlign: 'center',
                  color: '#616161',
                  marginBottom: '20px'
                }}
              >
                Fill out the form details below to create a new form.
              </DialogContentText> */}
              <FormControl
                error={nameErr}
                sx={{ width: '100%', marginTop: '20px', marginBottom: '10px' }}
              >
                <Input
                  required
                  name="name"
                  id="name"
                  sx={{ width: '100%' }}
                  autoComplete="off"
                  autoFocus
                  placeholder="Enter Form Name"
                />
                {nameErr && (
                  <FormHelperText error>
                    Blank spaces and Special characters are not allowed.
                  </FormHelperText>
                )}
              </FormControl>
              <RadioGroup
                aria-labelledby="demo-radio-buttons-group-label"
                defaultValue="standardForm"
                name="radio-buttons-group"
                className="d-flex flex-row"
                sx={{ marginTop: '6px' }}
              >
                <FormControlLabel
                  value="standardForm"
                  control={<Radio id="isStandardForm" name="isStandardForm" />}
                  label="Standard Form"
                />
                <FormControlLabel
                  value="subForm"
                  control={<Radio id="hasSubForm" name="hasSubForm" />}
                  label="Sub Form"
                />
                {/* <FormControlLabel
                  value="quizForm"
                  control={<Radio id="isQuizForm" name="isQuizForm" />}
                  label="Quiz Form"
                /> */}
              </RadioGroup>

              <RadioGroup
                aria-labelledby="demo-radio-buttons-group-label"
                defaultValue=""
                name="checkbox-buttons-group"
                className="d-flex flex-row"
                sx={{ marginTop: '6px' }}
              >
                <FormControlLabel
                  value="quizForm"
                  control={<Checkbox id="isQuizForm" name="isQuizForm" />}
                  label="Quiz Form"
                />
              </RadioGroup>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Typography>Blank Form</Typography>
                <Switch onChange={handleToggleChange} checked={isToggled} />
                <Typography>Import PDF Fillable Form</Typography>
              </Stack>
              {isToggled && (
                <FormControl sx={{ width: '100%', marginTop: '10px' }}>
                  {/* <Typography sx={{ marginBottom: '10px' }}>
                    Import PDF Fillable Form
                  </Typography> */}
                  <Input
                    required
                    type="file"
                    name="file"
                    id="file"
                    sx={{ width: '100%' }}
                    autoComplete="off"
                    autoFocus
                  />
                </FormControl>
              )}
            </DialogContent>
            <DialogActions sx={{ justifyContent: 'center' }}>
              <Button
                onClick={handleClose}
                sx={{
                  backgroundColor: 'white2.main',
                  color: 'primaryBlue.main',
                  padding: '10px 30px',
                  boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                  textTransform: 'capitalize'
                }}
              >
                CANCEL
              </Button>
              <LoadingButton
                type="submit"
                loading={isLoading}
                sx={{
                  backgroundColor: 'primaryBlue.main',
                  color: 'white2.main',
                  padding: '10px 35px',
                  boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                  '&:hover': {
                    color: 'white2.main',
                    backgroundColor: '#08366b',
                    boxShadow: '0px 8px 10px 4px rgba(0,0,0,0.2)'
                  }
                }}
              >
                SAVE
              </LoadingButton>
            </DialogActions>
          </Dialog>

          {/* <SuccessDialog
            open={openSuccess}
            onClose={onClose}
            // title="Form Submitted"
            // message={successMessage}
          /> */}
        </Box>
      </Box>
      {!isLoading && (
        <>
          <Box sx={{ marginBottom: '20px' }}>
            <Box sx={{ paddingTop: '15px' }}>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: '20px' }}>
                {selectedAppsList?.map((app: any, index: number) => {
                  const key = `${index}-${index * 2}-app-key`;
                  return (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '10px'
                      }}
                      key={key}
                    >
                      {app.url && (
                        <Box
                          className="bg-white d-flex align-items-center justify-content-center border-radius-50 overflow-hidden"
                          sx={{
                            width: '12px',
                            height: '12px',
                            color: '#616161'
                          }}
                        >
                          <img
                            src={app.url}
                            alt={app.name}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover'
                            }}
                          />
                        </Box>
                      )}
                      <Typography
                        className="font-weight-400 textTransform-capitalize font-size-18"
                        sx={{ color: '#616161' }}
                      >
                        {app.name}
                      </Typography>
                    </Box>
                  );
                })}
              </Box>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <Typography
              className="font-size-18 textTransform-capitalize"
              sx={{ color: '#595959' }}
            >
              Total Forms
            </Typography>
            <Typography
              className="d-flex justify-content-center align-items-center font-size-18 w-h-32 textTransform-capitalize font-weight-500"
              sx={{
                color: '#616161',
                background: '#EBEBEB',
                borderRadius: '50%'
              }}
            >
              {getApp?.forms?.length || 0}
            </Typography>
          </Box>
          {userType === 'super_admin' ? (
            <Box>
              {getApp?.forms?.length > 0 ? (
                <Grid
                  container
                  spacing={3}
                  sx={{
                    padding: '0 70px',
                    maxWidth: '100%',
                    margin: '0 auto'
                  }}
                >
                  {getApp?.forms?.map((form: any, index: number) => {
                    const key = `${index}-${index * 2}-form-key`;
                    return (
                      <Grid item xs={12} sm={12} md={6} lg={4} key={key}>
                        <Card
                          onClick={() => {
                            navigate(
                              form?.has_sub_forms
                                ? `/apps/app-details/sub-forms/${appId}/${form.form_id}`
                                : `/apps/form-builder/edit-form/${form.form_id}`
                            );
                            localStorage.setItem('app_id', appId);
                          }}
                          sx={{
                            borderRadius: 1,
                            boxShadow: 1,
                            p: 0,
                            width: '100%',
                            height: 80,
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            position: 'relative',
                            backgroundColor: 'background.paper',
                            border: '1px solid transparent',
                            transition: 'all 0.3s ease-in-out',
                            '&:hover': {
                              backgroundColor: 'background.default',
                              border: '1px solid',
                              borderColor: 'primary.main',
                              transform: 'scale(1.05)',
                              boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)'
                            }
                          }}
                        >
                          <CardContent
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 2,
                              pt: 2,
                              width: '100%'
                            }}
                          >
                            {form?.icon ? (
                              <CardMedia
                                component="img"
                                image={form.icon}
                                alt={form?.name}
                                sx={{
                                  width: '40px',
                                  height: '40px',
                                  backgroundColor: '#F9F9F9'
                                }}
                              />
                            ) : (
                              <AssignmentOutlinedIcon
                                sx={{ color: 'text.secondary', fontSize: 30 }}
                              />
                            )}
                            <Tooltip title={form?.name} arrow>
                              <Typography
                                sx={{
                                  fontSize: '18px',
                                  fontWeight: 600,
                                  color: 'text.secondary',
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  width: 200
                                }}
                              >
                                {form?.name}
                              </Typography>
                            </Tooltip>
                          </CardContent>
                        </Card>
                      </Grid>
                    );
                  })}
                </Grid>
              ) : (
                <Box sx={{ paddingTop: '60px' }} />
              )}
            </Box>
          ) : (
            <Box sx={{ paddingTop: '40px' }}>
              <Accordion defaultExpanded>
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  sx={{
                    background: '#E8F4F7',
                    borderRadius: '5px'
                  }}
                >
                  <Box className="d-flex align-items-center gap-10">
                    <Typography
                      className="font-weight-500"
                      sx={{ fontSize: '20px', color: '#595959' }}
                    >
                      Default Forms
                    </Typography>
                    <Typography
                      className="w-h-32 border-radius-50 font-size-18 font-weight-500 d-flex align-items-center justify-content-center"
                      sx={{
                        color: '#616161',
                        background: '#EBEBEB'
                      }}
                    >
                      {appData?.forms?.length}
                    </Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid
                    container
                    spacing={3}
                    sx={{
                      padding: '0 70px',
                      maxWidth: '100%',
                      margin: '0 auto'
                    }}
                  >
                    {appData?.forms?.map((form: any, index: number) => {
                      const key = `${index}-${index * 2}-form-key`;
                      return (
                        <Grid item xs={12} sm={12} md={6} lg={4} key={key}>
                          <Card
                            onClick={() => {
                              navigate(
                                form?.has_sub_forms
                                  ? `/form-builder/form-details/sub-forms/${appId}/${form.form_id}`
                                  : `/form-builder/edit-form/${form.form_id}`
                              );
                              localStorage.setItem('app_id', appId);
                            }}
                            sx={{
                              borderRadius: 1,
                              boxShadow: 1,
                              p: 0,
                              width: '100%',
                              height: 80,
                              cursor: 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              position: 'relative',
                              backgroundColor: 'background.paper',
                              border: '1px solid transparent',
                              transition: 'all 0.3s ease-in-out',
                              '&:hover': {
                                backgroundColor: 'background.default',
                                border: '1px solid',
                                borderColor: 'primary.main',
                                transform: 'scale(1.05)',
                                boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)'
                              }
                            }}
                          >
                            <CardContent
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 2,
                                pt: 2,
                                width: '100%'
                              }}
                            >
                              {form?.icon ? (
                                <CardMedia
                                  component="img"
                                  image={form.icon}
                                  alt={form?.name}
                                  sx={{
                                    width: '40px',
                                    height: '40px',
                                    backgroundColor: '#F9F9F9'
                                  }}
                                />
                              ) : (
                                <AssignmentOutlinedIcon
                                  sx={{ color: 'text.secondary', fontSize: 30 }}
                                />
                              )}
                              <Tooltip title={form?.name} arrow>
                                <Typography
                                  sx={{
                                    fontSize: '18px',
                                    fontWeight: 600,
                                    color: 'text.secondary',
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    width: 200
                                  }}
                                >
                                  {form?.name}
                                </Typography>
                              </Tooltip>
                            </CardContent>
                          </Card>
                        </Grid>
                      );
                    })}
                  </Grid>
                </AccordionDetails>
              </Accordion>

              <Accordion>
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  sx={{
                    background: '#E8F4F7',
                    borderRadius: '5px'
                  }}
                >
                  <Box className="d-flex align-items-center gap-10">
                    <Typography
                      className="font-weight-500"
                      sx={{ fontSize: '20px', color: '#595959' }}
                    >
                      Modified Forms
                    </Typography>
                    <Typography
                      className="w-h-32 border-radius-50 font-size-18 font-weight-500 d-flex align-items-center justify-content-center"
                      sx={{ color: '#616161' }}
                    >
                      {0}
                    </Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Box>
                    <Typography>Forms not available</Typography>
                  </Box>
                </AccordionDetails>
              </Accordion>

              <Accordion>
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  sx={{
                    background: '#E8F4F7',
                    borderRadius: '5px'
                  }}
                >
                  <Box className="d-flex align-items-center gap-10">
                    <Typography
                      className="font-weight-500"
                      sx={{ fontSize: '20px', color: '#595959' }}
                    >
                      My Forms
                    </Typography>
                    <Typography
                      className="w-h-32 border-radius-50 font-size-18 font-weight-500 d-flex align-items-center justify-content-center"
                      sx={{ color: '#616161' }}
                    >
                      {0}
                    </Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Box>
                    <Typography>Forms not available</Typography>
                  </Box>
                </AccordionDetails>
              </Accordion>
            </Box>
          )}
        </>
      )}
    </Box>
  );
};

export default CreateForm;
