import {
  useMemo
  // useState
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import { AppDispatch, AppState } from '../../redux/app.store';
import { getapp } from '../../redux/reducers/apps.reducer';
import FormDetails from '../../components/apps/FormDetails';
// import { SnackbarElement } from '../../components/reusable/SnackbarElement';
// import { SnabackBarState } from '../../types';
import LoaderUI from '../../components/reusable/loaderUI';

const FormDetailsPage: React.FC = () => {
  const { id }: any = useParams();
  const dispatch = useDispatch<AppDispatch>();
  const { getApp, isLoading } = useSelector((state: AppState) => state.app);
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: ''
  // });
  try {
    const getData = async () => {
      const res = await dispatch(getapp(id));
      if (res?.payload?.status) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     res?.payload?.message ||
        //     'Something Went Wrong, Please Try Again Later.'
        // });
        // toast.success(res?.payload?.message || 'Success.');
      } else {
        toast.error(
          res?.payload?.message ||
            ' Something Went Wrong, Please Try Again Later.'
        );
      }
    };
    useMemo(() => {
      getData();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [id]);
  } catch (err) {
    // setSnackbarOpen({
    //   status: true,
    //   message: 'Something Went Wrong, Please Try Again Later.'
    // });
    toast.error('Something Went Wrong, Please Try Again Later.');
  }
  return (
    <>
      {isLoading && <LoaderUI />}
      {/* {!isLoading &&  */}
      <FormDetails appData={getApp} id={id} />
      {/* } */}
      {/* <SnackbarElement
        message={snackbarOpen.message}
        snackbarOpen={snackbarOpen.status}
        statusType="error"
        setSnackbarOpen={undefined}
      /> */}
    </>
  );
};
export default FormDetailsPage;
