import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Button,
  Menu,
  MenuItem,
  IconButton,
  Typography,
  Chip,
  Paper,
  Divider,
  Tabs,
  Tab,
  TextField,
  List,
  ListItem,
  Checkbox,
  ListItemText
} from '@mui/material';
import TuneIcon from '@mui/icons-material/Tune';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import CloseIcon from '@mui/icons-material/Close';

type JobPostsFilterProps = {
  onFilterChange?: (filters: Record<string, any>) => void;
};

const filterCategories = ['Sort', 'Type', 'Brand'];
const brandOptions = [
  'Puma',
  'RARE RABBIT',
  'EQL',
  'Jockey',
  'Adidas',
  'Boldfit',
  'PlayR'
];

const JobPostsFilterNew: React.FC<JobPostsFilterProps> = ({
  onFilterChange
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState<Record<string, any>>(
    {}
  );
  const [menuAnchorEl, setMenuAnchorEl] = useState<HTMLElement | null>(null);
  const [currentFilterType, setCurrentFilterType] = useState<string | null>(
    null
  );
  const popupRef = useRef<HTMLDivElement>(null);

  const [activeTab, setActiveTab] = useState(2);
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [search, setSearch] = useState('');

  const toggleBrand = (brand: string) => {
    setSelectedBrands((prev) =>
      prev.includes(brand) ? prev.filter((b) => b !== brand) : [...prev, brand]
    );
  };

  const filteredBrands = brandOptions.filter((b) =>
    b.toLowerCase().includes(search.toLowerCase())
  );

  const filterOptions = {
    employment_type: ['Full-time', 'Part-time', 'Contract', 'Internship'],
    status: ['Active', 'Inactive', 'Draft', 'Closed'],
    location: [
      'Remote',
      'New York',
      'San Francisco',
      'London',
      'Berlin',
      'Toronto'
    ],
    department: [
      'Engineering',
      'Marketing',
      'Sales',
      'HR',
      'Finance',
      'Product'
    ],
    salary_range: ['$30k-50k', '$50k-80k', '$80k-120k', '$120k-150k', '$150k+'],
    stages: ['Screening', 'Interview', 'Technical', 'Final Round', 'Offer']
  };

  // Close popup when clicking outside (but not on dropdown menus)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      // If click is inside popup panel, do nothing
      if (popupRef.current && popupRef.current.contains(target)) return;

      // If click is inside any MUI Menu (which renders outside via portal), do nothing
      if (target.closest('.MuiPopover-root')) return;

      // Otherwise, close the popup
      setIsOpen(false);
      setCurrentFilterType(null);
      setMenuAnchorEl(null);
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleFilterSelect = (filterType: string, value: any) => {
    const newFilters = { ...selectedFilters, [filterType]: value };
    setSelectedFilters(newFilters);
    onFilterChange?.(newFilters);
  };

  const removeFilter = (filterType: string) => {
    const newFilters = { ...selectedFilters };
    delete newFilters[filterType];
    setSelectedFilters(newFilters);
    onFilterChange?.(newFilters);
  };

  const clearAllFilters = () => {
    setSelectedFilters({});
    onFilterChange?.({});
  };

  const toggleDropdown = (
    filterType: string,
    event: React.MouseEvent<HTMLElement>
  ) => {
    if (currentFilterType === filterType) {
      setCurrentFilterType(null);
      setMenuAnchorEl(null);
    } else {
      setCurrentFilterType(filterType);
      setMenuAnchorEl(event.currentTarget);
    }
  };

  const getFilterLabel = (key: string) => {
    return key
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const activeFiltersCount = Object.keys(selectedFilters).length;
  console.log(' Filter Options: ', Object.entries(filterOptions)[2][0]);

  return (
    <Box position="relative" ref={popupRef}>
      {/* Filter Button */}
      <Button
        variant="outlined"
        startIcon={<TuneIcon />}
        endIcon={
          <ChevronRightIcon
            sx={{ transform: isOpen ? 'rotate(90deg)' : 'none' }}
          />
        }
        onClick={() => {
          setIsOpen(!isOpen);
          setCurrentFilterType(null);
          setMenuAnchorEl(null);
        }}
        sx={{
          textTransform: 'none',
          borderRadius: '8px',
          height: '50px',
          outline: 'none',
          border: '1px solid #e2e8f0',
          color: 'rgba(0, 0, 0, 0.7)',
          '&:hover': {
            backgroundColor: 'transparent',
            borderColor: '#0483BA',
            color: '#0483BA'
          }
        }}
      >
        Filters {activeFiltersCount > 0 && `(${activeFiltersCount})`}
      </Button>

      {/* Active Filter Chips */}
      {activeFiltersCount > 0 && (
        <Box display="flex" flexWrap="wrap" gap={1} mt={1}>
          {Object.entries(selectedFilters).map(([key, value]) => (
            <Chip
              key={key}
              label={`${getFilterLabel(key)}: ${value}`}
              onDelete={() => removeFilter(key)}
              size="small"
              color="primary"
              sx={{ fontSize: 12 }}
            />
          ))}
          <Button size="small" color="secondary" onClick={clearAllFilters}>
            Clear all
          </Button>
        </Box>
      )}

      {/* Popup Panel */}
      {isOpen && (
        <Paper
          elevation={3}
          sx={{
            position: 'absolute',
            top: 'calc(100% + 8px)',
            width: 500,
            zIndex: 10,
            borderRadius: 2,
            p: 2
          }}
        >
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            mb={2}
          >
            <Typography variant="h6" fontSize={16}>
              Filter Jobs
            </Typography>
            <IconButton size="small" onClick={() => setIsOpen(false)}>
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>

          {/* Filter Fields */}
          <Box display="flex" flexDirection="column" gap={2}>
            <Box display="flex" flexDirection="row" gap={2}>
              <Tabs
                orientation="vertical"
                value={activeTab}
                onChange={(e, newValue) => setActiveTab(newValue)}
                sx={{ borderRight: 1, borderColor: 'divider', width: '30%' }}
              >
                {/* {filterCategories.map((cat, i) => (
                <Tab key={cat} label={cat} sx={{ alignItems: 'flex-start' }} />
              ))} */}
                {Object.entries(filterOptions).map(([filterType]) => {
                  return (
                    <Tab
                      key={filterType}
                      label={getFilterLabel(filterType)}
                      sx={{
                        textAlign: 'left',
                        justifyContent: 'flex-start',
                        alignItems: 'flex-start',
                        textTransform: 'none',
                        fontSize: 14,
                        fontWeight: 'semibold'
                        // fontWeight: activeTab === 2 ? 'bold' : 'normal'
                      }}
                    />
                  );
                })}
                ;
              </Tabs>

              <Box flex={1} p={2} position="relative">
                {Object.entries(filterOptions).map(([filterType, options]) => {
                  return (
                    <Box
                      key={filterType}
                      sx={{
                        display:
                          Object.entries(filterOptions)[activeTab][0] ===
                          filterType
                            ? 'block'
                            : 'none'
                      }}
                    >
                      <Typography variant="h6" mb={1}>
                        {getFilterLabel(filterType)}
                      </Typography>
                      <List dense>
                        {options.map((option) => (
                          <ListItem key={option} disablePadding>
                            <Checkbox
                              checked={
                                selectedFilters[filterType] === option ||
                                (Array.isArray(selectedFilters[filterType]) &&
                                  selectedFilters[filterType].includes(option))
                              }
                              onChange={() => {
                                handleFilterSelect(filterType, option);
                                toggleBrand(filterType);
                              }}
                            />
                            <ListItemText primary={option} />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  );
                })}

                {/* {activeTab === 2 && (
                  <>
                    <Typography variant="h6" mb={1}>
                      Brand
                    </Typography>
                    <TextField
                      placeholder="Search for Brand"
                      fullWidth
                      size="small"
                      value={search}
                      onChange={(e) => setSearch(e.target.value)}
                      sx={{ mb: 2 }}
                    />
                    <List dense>
                      {filteredBrands.map((brand) => (
                        <ListItem key={brand} disablePadding>
                          <Checkbox
                            checked={selectedBrands.includes(brand)}
                            onChange={() => toggleBrand(brand)}
                          />
                          <ListItemText primary={brand} />
                        </ListItem>
                      ))}
                    </List>
                  </>
                )} */}

                {/* Sticky Bottom Buttons */}
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    p: 2,
                    bgcolor: 'white',
                    display: 'flex',
                    justifyContent: 'space-between',
                    borderTop: '1px solid #eee'
                  }}
                >
                  <Button onClick={() => setSelectedBrands([])} variant="text">
                    Clear Filters
                  </Button>
                  <Button variant="contained">Apply</Button>
                </Box>
              </Box>
            </Box>
          </Box>

          {/* Footer Buttons */}
          {/* <Box display="flex" gap={1}>
            <Button
              fullWidth
              size="small"
              variant="outlined"
              color="secondary"
              onClick={clearAllFilters}
            >
              Clear All
            </Button>
            <Button
              fullWidth
              size="small"
              variant="contained"
              onClick={() => setIsOpen(false)}
            >
              Apply Filters
            </Button>
          </Box> */}
        </Paper>
      )}
    </Box>
  );
};

export default JobPostsFilterNew;
