/* eslint-disable import/no-named-as-default */
/* eslint-disable react/no-array-index-key */
// Global Imports
import {
  Badge,
  Box,
  Button,
  Collapse,
  Divider,
  IconButton,
  Modal,
  Pagination,
  Paper,
  Step,
  StepLabel,
  Stepper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography
} from '@mui/material';
import * as Yup from 'yup';
import { useEffect, useState } from 'react';
import {
  // Link,
  useNavigate
} from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
// import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';
import { toast } from 'react-toastify';
import moment from 'moment';
import { DataGrid, GridColDef } from '@mui/x-data-grid';

// Local Imports
import '../../../css/employee-onboarding-styles.scss';

import {
  EmployeeOnboardingStepperModalProps,
  EmployeeRowProps
  // SnabackBarState
} from '../../../types';
import { AppForm, FormInput, SubMenu, SubmitButton } from '../../form.elements';
import Shell from '../../layout/Shell';
import Sidebar from '../../reusable/Sidebar';
import { AppDispatch, AppState } from '../../../redux/app.store';
// import { SnackbarElement } from '../../reusable/SnackbarElement';
import { deleteapplicant } from '../../../redux/reducers/applicant.reducer';
import {
  getemployees,
  onboardapplicant,
  sendmailafterscheduleinterview
} from '../../../redux/reducers/user.reducer';
import LoaderUI from '../../reusable/loaderUI';
import { UserNavBarModules } from '../UsersList';
import SearchWithFilters from '../../reusable/SearchWithFilters';

const RenderStatusStepperModal: React.FC<
  EmployeeOnboardingStepperModalProps
> = ({
  emp,
  handleClose,
  currentStatus,
  stepperIntialValues,
  setLocalEmployeeList
}) => {
  const dispatch = useDispatch<AppDispatch>();

  const getYesterdayDate = () => {
    const today = new Date();
    today.setDate(today.getDate() - 1);
    return today.toISOString().split('T')[0];
  };

  // const [bgCheckInitialValues, setBgCheckInitialValues] = useState({
  //   background_check_file: ''
  // });

  const bgCheckValidationSchema = Yup.object().shape({
    background_check_file: Yup.string().required('File is required')
  });
  const [bg, setBg]: any = useState({});

  const validationSchema = Yup.object().shape({
    interview_date: Yup.date()
      .required('Interview date is required')
      .min(getYesterdayDate(), 'Interview date cannot be in the past'),

    interview_time: Yup.string()
      .required('Interview time is required')
      .matches(/^([01]\d|2[0-3]):([0-5]\d)$/, 'Please enter a valid time')
      .when('interview_date', (interview_date, schema) => {
        return schema.test(
          'interview-date-time',
          'Interview date and time cannot be in the past',
          (interview_time) => {
            if (!interview_date || !interview_time) return true;

            const dateString = moment(interview_date[0]).format('YYYY-MM-DD'); // format the date properly
            const interviewDateTime = moment(
              `${dateString} ${interview_time}`,
              'YYYY-MM-DD HH:mm'
            );

            const now = moment();
            return interviewDateTime.isAfter(now);
          }
        );
      })
  });

  const updateApplicant = async (
    data: any,
    emailData?: {
      email: string;
      subject: string;
      content: string;
    }
  ) => {
    const response = await dispatch(onboardapplicant({ id: emp.id, data }));

    if (response.payload.status) {
      setLocalEmployeeList((prevList: any) =>
        prevList.map((employee: any) => {
          if (employee.id !== emp.id) {
            return employee;
          }
          return {
            ...employee,
            ...data
          };
        })
      );

      if (emailData) {
        await dispatch(sendmailafterscheduleinterview(emailData));
      }
      handleClose();
    }
  };
  const handleSubmit = async (e: any) => {
    let data = {};
    let emailData = { email: '', subject: '', content: '' };
    if (
      currentStatus === 'Application Completed' ||
      currentStatus === 'Interview Scheduled'
    ) {
      data = {
        ...emp,
        scheduled_interview_date: e.interview_date,
        scheduled_interview_time: e.interview_time,
        status: 'Interview Scheduled'
      };
      emailData = {
        email: emp.email,
        subject: 'Interview Scheduled',
        content: `<html><head></head><body><p style="text-transform: capitalize;">Dear ${emp.name},</p><p>Interview is Scheduled on ${e.interview_date} at ${e.interview_time} </p><p>All the best. If you wish to reschedule, Please reply to this mail.</p><p>Regards,</p><p>HR Manager,</p><p>${localStorage.getItem('org_name')}.</p></body></html>`
      };
      updateApplicant(data, emailData);
    }

    if (emp.status === 'In Person Orientation Completed') {
      data = {
        ...emp,
        bg_verification_document: bg || null,
        status: 'Employee Onboard Completed'
      };
      await updateApplicant(data);
      await dispatch(getemployees(null));
    }
  };
  const interviewCompleted = (element: any) => {
    if (
      element.scheduled_interviews &&
      element.scheduled_interviews.length > 0
    ) {
      return (
        new Date(
          element.scheduled_interviews[
            element.scheduled_interviews.length - 1
          ].scheduled_interview_date
        ) < new Date(Date.now()) &&
        element.scheduled_interviews[element.scheduled_interviews.length - 1]
          .scheduled_interview_time < new Date().toString().split(' ')[4] &&
        element?.status === 'Interview Scheduled'
      );
    }
    return false;
  };
  const yes = async () => {
    if (emp.status === 'Interview Scheduled' && interviewCompleted(emp)) {
      await updateApplicant({
        ...emp,
        status: 'Interview Completed'
      });
    }
    if (emp.status === 'Orientation Completed') {
      await updateApplicant({
        ...emp,
        status:
          localStorage.getItem('org_id') === import.meta.env.VITE_FRESNO_ORG_ID
            ? 'Orientation Completed'
            : 'In Person Orientation Completed'
      });
    }
    await dispatch(getemployees(null));
  };

  const no = async () => {
    if (emp.status === 'Interview Scheduled' && interviewCompleted(emp)) {
      await updateApplicant({
        ...emp,
        status: 'Application Completed'
      });
    }
    if (emp.status === 'In Person Orientation Completed') {
      await updateApplicant({
        ...emp,
        bg_verification_document: bg || null,
        status: 'Rejected'
      });
    }
  };

  const handleChange = (event: any) => {
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      setBg({
        name: file?.name,
        file: reader?.result,
        mimetype: file?.type
      });
    };
  };

  switch (emp.status) {
    case 'Schedule Orientation':
      return (
        <Box>
          <Typography id="modal-modal-title" variant="h6" component="h2">
            Schedule Orientation
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <AppForm
            initialValues={{
              orientation_date: new Date().toISOString().split('T')[0]
            }}
            validationSchema={Yup.object().shape({
              orientation_date: Yup.date()
                .required('Schedule date is required')
                .min(getYesterdayDate(), 'Schedule date cannot be in the past')
            })}
            onSubmit={async (values) => {
              await updateApplicant({
                ...emp,
                orientation_date: values?.orientation_date || null,
                status: 'Orientation Scheduled'
              });
            }}
          >
            <>
              <Box sx={{ mt: 2 }}>
                <FormInput
                  placeholder="Date"
                  type="date"
                  name="orientation_date"
                  label="Date"
                  containerStyles={{
                    width: '100%',
                    marginBottom: '16px'
                  }}
                />
              </Box>

              <Box sx={{ display: 'flex', mt: 2, justifyContent: 'end' }}>
                <Button
                  variant="contained"
                  onClick={handleClose}
                  sx={{
                    mr: 1,
                    backgroundColor: 'white2.main',
                    color: '#242424'
                  }}
                >
                  Cancel
                </Button>
                <SubmitButton
                  title="Schedule"
                  sx={{
                    backgroundColor: 'primaryBlue.main',
                    color: 'white2.main'
                  }}
                />
              </Box>
            </>
          </AppForm>
        </Box>
      );
    case 'Orientation Completed':
      return (
        <Box sx={{ textAlign: 'center' }}>
          <Typography
            variant="h6"
            component="h2"
            sx={{ mb: 2, textAlign: 'left' }}
          >
            In-Person Orientation
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <Typography id="modal-modal-title" variant="h6" component="h2">
            {localStorage.getItem('org_id') ===
            import.meta.env.VITE_FRESNO_ORG_ID
              ? 'Start In-Person Orientation ?'
              : 'Is In-Person Orientation For Applicant Completed ?'}
          </Typography>
          <Box sx={{ mt: 2 }}>
            <Button
              variant="contained"
              onClick={handleClose}
              sx={{
                mr: 1,
                backgroundColor: 'white2.main',
                color: '#242424'
              }}
            >
              No
            </Button>
            <Button
              variant="contained"
              onClick={yes}
              sx={{
                backgroundColor: 'primaryBlue.main',
                color: 'white2.main'
              }}
            >
              Yes
            </Button>
          </Box>
        </Box>
      );
    case 'In Person Orientation Completed':
      return localStorage.getItem('org_id') ===
        import.meta.env.VITE_FRESNO_ORG_ID ? (
        <Box sx={{ textAlign: 'center' }}>
          <Typography
            variant="h6"
            component="h2"
            sx={{ mb: 2, textAlign: 'left' }}
          >
            Onboard Applicant
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <Typography id="modal-modal-title" variant="h6" component="h2">
            Are you sure you want to onboard this applicant ?
          </Typography>
          <Box sx={{ mt: 2 }}>
            <Button
              variant="contained"
              onClick={no}
              sx={{
                mr: 1,
                backgroundColor: 'white2.main',
                color: '#242424'
              }}
            >
              Reject
            </Button>
            <Button
              variant="contained"
              onClick={handleSubmit}
              sx={{
                backgroundColor: 'primaryBlue.main',
                color: 'white2.main'
              }}
            >
              Accept
            </Button>
          </Box>
        </Box>
      ) : (
        <Box>
          <Typography id="modal-modal-title" variant="h6" component="h2">
            Background Check
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <AppForm
            initialValues={{
              background_check_file: ''
            }}
            validationSchema={bgCheckValidationSchema}
            onSubmit={handleSubmit}
          >
            <>
              <Typography id="modal-modal-title" variant="body1">
                Please Upload Background Check Form
              </Typography>
              <Box sx={{ mt: 1 }}>
                <FormInput
                  placeholder=""
                  type="file"
                  name="background_check_file"
                  label=""
                  containerStyles={{
                    width: '100%',
                    marginBottom: '16px'
                  }}
                  handleInputChange={handleChange}
                />
              </Box>

              <Box sx={{ display: 'flex', mt: 2, justifyContent: 'end' }}>
                <Button
                  variant="contained"
                  onClick={no}
                  sx={{
                    mr: 1,
                    backgroundColor: 'white2.main',
                    color: '#242424'
                  }}
                >
                  Reject
                </Button>
                <SubmitButton
                  title="Accept"
                  sx={{
                    backgroundColor: 'primaryBlue.main',
                    color: 'white2.main'
                  }}
                />
              </Box>
            </>
          </AppForm>
        </Box>
      );

    default:
      if (emp.status === 'Interview Scheduled' && interviewCompleted(emp))
        return (
          <Box sx={{ textAlign: 'center' }}>
            <Typography
              variant="h6"
              component="h2"
              sx={{ mb: 2, textAlign: 'left' }}
            >
              Interview Status
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Typography id="modal-modal-title" variant="h6" component="h2">
              Is Interview Completed ?
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Button
                variant="contained"
                onClick={no}
                sx={{
                  mr: 1,
                  backgroundColor: 'white2.main',
                  color: '#242424'
                }}
              >
                No
              </Button>
              <Button
                variant="contained"
                onClick={yes}
                sx={{
                  backgroundColor: 'primaryBlue.main',
                  color: 'white2.main'
                }}
              >
                Yes
              </Button>
            </Box>
          </Box>
        );

      return (
        <Box>
          <Typography id="modal-modal-title" variant="h6" component="h2">
            Schedule Interview
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <AppForm
            initialValues={stepperIntialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            <>
              <Box sx={{ mt: 2 }}>
                <FormInput
                  placeholder="Interview Date"
                  type="date"
                  name="interview_date"
                  label="Interview Date"
                  containerStyles={{
                    width: '100%',
                    marginBottom: '16px'
                  }}
                />

                <FormInput
                  placeholder="Interview Time"
                  type="time"
                  name="interview_time"
                  label="Interview Time"
                  containerStyles={{
                    width: '100%'
                  }}
                />
              </Box>

              <Box sx={{ display: 'flex', mt: 2, justifyContent: 'end' }}>
                <Button
                  variant="contained"
                  onClick={handleClose}
                  sx={{
                    mr: 1,
                    backgroundColor: 'white2.main',
                    color: '#242424'
                  }}
                >
                  Cancel
                </Button>
                {currentStatus === 'Application Completed' && (
                  <SubmitButton
                    title="Save"
                    sx={{
                      backgroundColor: 'primaryBlue.main',
                      color: 'white2.main'
                    }}
                  />
                )}
                {currentStatus === 'Interview Scheduled' && (
                  <SubmitButton
                    title="Update"
                    sx={{
                      backgroundColor: 'primaryBlue.main',
                      color: 'white2.main'
                    }}
                  />
                )}
              </Box>
            </>
          </AppForm>
        </Box>
      );
  }
};

const Row = (props: EmployeeRowProps) => {
  const {
    row,
    steps,
    handleReject,
    handleRowClick,
    handleViewClick,
    handleEditClick,
    interviewCompleted,
    handleOnboardClick
    // handlesetCurrentStatus
  } = props;
  const [open, setOpen] = useState(false);

  const getStatusIndex = (status: any) => {
    const normalizedStatus = status
      .replace(/_/g, ' ')
      .replace(/\b\w/g, (l: any) => l.toUpperCase());
    return steps.indexOf(normalizedStatus);
  };
  const currentStep = getStatusIndex(row.status);

  // useEffect(() => {
  //   if (row.status) {
  //     handlesetCurrentStatus(row.status);
  //   }
  // }, [row.status]);

  const render = () => {
    if (row.status === 'Application Completed' && !interviewCompleted(row))
      return (
        <Button
          variant="contained"
          size="small"
          sx={{
            marginLeft: 1,
            marginTop: 1,
            backgroundColor: 'green',
            color: 'white',
            '&:hover': {
              backgroundColor: 'darkgreen'
            }
          }}
          onClick={(e) => handleOnboardClick(e, row.id)}
        >
          Schedule Interview
        </Button>
      );
    if (row.status === 'Interview Scheduled' && !interviewCompleted(row))
      return (
        <Button
          variant="contained"
          size="small"
          sx={{
            marginLeft: 1,
            marginTop: 1,
            backgroundColor: 'green',
            color: 'white',
            '&:hover': {
              backgroundColor: 'darkgreen'
            }
          }}
          onClick={(e) => handleOnboardClick(e, row.id)}
        >
          Re-Schedule Interview
        </Button>
      );
    if (row.status === 'Interview Scheduled' && interviewCompleted(row))
      return (
        <Button
          variant="contained"
          size="small"
          sx={{
            marginLeft: 1,
            marginTop: 1,
            backgroundColor: 'green',
            color: 'white',
            '&:hover': {
              backgroundColor: 'darkgreen'
            }
          }}
          onClick={(e) => handleOnboardClick(e, row.id)}
        >
          Interview Completed
        </Button>
      );
    if (row.status === 'Schedule Orientation')
      return (
        <Button
          variant="contained"
          size="small"
          sx={{
            marginLeft: 1,
            marginTop: 1,
            backgroundColor: 'green',
            color: 'white',
            '&:hover': {
              backgroundColor: 'darkgreen'
            }
          }}
          onClick={(e) => handleOnboardClick(e, row.id)}
        >
          Schedule Orientation
        </Button>
      );
    if (row.status === 'Orientation Completed')
      return (
        <Button
          variant="contained"
          size="small"
          sx={{
            marginLeft: 1,
            marginTop: 1,
            backgroundColor: 'green',
            color: 'white',
            '&:hover': {
              backgroundColor: 'darkgreen'
            }
          }}
          onClick={(e) => handleOnboardClick(e, row.id)}
        >
          In Person Orientation
        </Button>
      );
    if (row.status === 'In Person Orientation Completed')
      return (
        <Button
          variant="contained"
          size="small"
          sx={{
            marginLeft: 1,
            marginTop: 1,
            backgroundColor: 'green',
            color: 'white',
            '&:hover': {
              backgroundColor: 'darkgreen'
            }
          }}
          onClick={(e) => handleOnboardClick(e, row.id)}
        >
          {localStorage.getItem('org_id') === import.meta.env.VITE_FRESNO_ORG_ID
            ? 'Onboard Applicant'
            : 'Background Check'}
        </Button>
      );
    return '';
    // return <Typography>{row.status}</Typography>;
  };

  return (
    <>
      <TableRow
        sx={{
          '& > *': { borderBottom: 'unset' },
          cursor: 'pointer',
          '&:hover': { backgroundColor: '#f5f5f5' }
        }}
        onClick={(e) => handleRowClick(e, row.id)}
      >
        <TableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              setOpen(!open);
            }}
          >
            {open ? (
              <RemoveCircleOutlineIcon color="primary" />
            ) : (
              <AddCircleOutlineIcon color="primary" />
            )}
          </IconButton>
        </TableCell>
        <TableCell component="th" scope="row">
          {row.name}
        </TableCell>
        <TableCell component="th" scope="row">
          {row.email}
        </TableCell>
        <TableCell component="th" scope="row">
          {row.mobile_number}
        </TableCell>
        <TableCell component="th" scope="row">
          {row?.is_rejected ? (
            <span style={{ color: 'red' }}>Rejected</span>
          ) : (
            row.status
          )}
        </TableCell>
        {/* <TableCell align="right">{row.form_filling_status}</TableCell> */}
        <TableCell align="left">
          {row?.is_rejected ? (
            // <IconButton onClick={(e) => handleViewClick(e, row.id)}>
            //   <VisibilityIcon color="primary" />
            // </IconButton>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 1,
                flexWrap: 'wrap',
                width: '100%'
              }}
            >
              <Button
                variant="contained"
                // color="error"
                size="small"
                onClick={(e) => handleViewClick(e, row.id)}
                sx={{ marginLeft: 1, marginTop: 1 }}
              >
                View
              </Button>
            </Box>
          ) : (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 1,
                flexWrap: 'wrap',
                width: '100%'
              }}
            >
              {/* <IconButton onClick={(e) => handleEditClick(e, row.id)}>
                <EditIcon color="primary" />
              </IconButton> */}
              <Button
                variant="contained"
                // color="error"
                size="small"
                onClick={(e) => handleEditClick(e, row.id)}
                sx={{ marginLeft: 1, marginTop: 1 }}
              >
                Edit
              </Button>
              {render()}
              <Button
                variant="contained"
                color="error"
                size="small"
                onClick={(e) => handleReject(e, 'Rejected', row.id)}
                sx={{ marginLeft: 1, marginTop: 1 }}
              >
                Reject
              </Button>
            </Box>
          )}
        </TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={6}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1 }}>
              {/* Stepper Component */}
              <Typography variant="h6" gutterBottom component="div">
                Application Progress
              </Typography>
              <Stepper activeStep={currentStep} alternativeLabel>
                {steps.map((label: string) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </Stepper>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  );
};

const EmployeeOnboarding = () => {
  const { employeeList, isLoading }: any = useSelector(
    (state: AppState) => state.user
  );

  const dispatch = useDispatch<AppDispatch>();
  const [localEmployeeList, setLocalEmployeeList] = useState<
    typeof employeeList
  >([]);
  const navigate = useNavigate();

  const [emp, setEmp]: any = useState();
  const [open, setOpen] = useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: '',
  //   type: 'success'
  // });
  const [stepperIntialValues, setStepperIntialValues] = useState({
    interview_date: '',
    interview_time: ''
  });
  // const [currentStatus, setCurrentStatus] = useState('');
  const [activePage, setActivePage] = useState(1);
  const rowsPerPage = 5;
  const totalPages = Math.ceil(localEmployeeList.length / rowsPerPage);

  const handleOnboardClick = (e: any, id: any) => {
    e.stopPropagation();
    const employee = localEmployeeList.find(
      (empployee: any) => empployee.onboarding_employee_id === id
    );
    setEmp(employee);

    if (
      employee?.scheduled_interviews &&
      employee?.scheduled_interviews.length > 0
    ) {
      setStepperIntialValues({
        interview_date:
          employee.scheduled_interviews[
            employee.scheduled_interviews.length - 1
          ].scheduled_interview_date,
        interview_time:
          employee.scheduled_interviews[
            employee.scheduled_interviews.length - 1
          ].scheduled_interview_time
      });
    }
    handleOpen();
  };

  const interviewCompleted = (element: any) => {
    if (
      element.scheduled_interviews &&
      element.scheduled_interviews.length > 0
    ) {
      return (
        new Date(
          element.scheduled_interviews[
            element.scheduled_interviews.length - 1
          ].scheduled_interview_date
        ) < new Date(Date.now()) &&
        element.scheduled_interviews[element.scheduled_interviews.length - 1]
          .scheduled_interview_time < new Date().toString().split(' ')[4] &&
        element?.status === 'Interview Scheduled'
      );
    }
    return false;
  };

  const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 950,
    bgcolor: 'background.paper',
    border: '2px solid #000',
    boxShadow: 24,
    p: 4
  };

  const menuItems = UserNavBarModules();

  const handleRowClick = (e: React.MouseEvent, id: any) => {
    e.stopPropagation();
    navigate(`/users/employee-onboarding/checklist/${id}`);
  };

  const handleEditClick = (e: React.MouseEvent, id: any) => {
    e.stopPropagation();
    navigate(`/users/employee-onboarding/edit-applicant/${id}`);
  };

  const handleViewClick = (e: React.MouseEvent, id: any) => {
    e.stopPropagation();
    navigate(`/users/employee-onboarding/checklist/${id}`);
  };

  const handleReject = async (e: React.MouseEvent, type: any, id: any) => {
    e.stopPropagation();
    const formData: any = {
      status: type
    };

    if (id) {
      try {
        const userData = await dispatch(
          deleteapplicant({ id, data: formData })
        );
        if (userData.payload) {
          setLocalEmployeeList((prevList: any) =>
            prevList.map((employee: any) => {
              if (employee.id !== id) {
                return employee;
              }
              return {
                ...employee,
                is_rejected: true
              };
            })
          );
        }
        if (userData.payload.error) {
          // setSnackbarOpen({
          //   status: true,
          //   message: userData.payload?.error,
          //   type: 'error'
          // });
          toast.error(userData.payload?.error);
        } else if (userData.payload.status) {
          const rejectedMessage = 'Applicant has been rejected';
          // setSnackbarOpen({
          //   status: true,
          //   message: rejectedMessage,
          //   type: 'success'
          // });
          toast.success(rejectedMessage);
        }
      } catch (error: any) {
        // setSnackbarOpen({
        //   status: true,
        //   message:
        //     error?.message || 'Something Went Wrong Please try again later',
        //   type: 'error'
        // });
        toast.error(
          error?.message || 'Something Went Wrong Please try again later'
        );
      }
    }
  };

  // const handlesetCurrentStatus = (value: string) => setCurrentStatus(value);

  useEffect(() => {
    setLocalEmployeeList(employeeList);
  }, [employeeList, employeeList?.length]);

  const getSubMenu = () => {
    return <SubMenu backNavigation createChecklist createApplicant />;
  };

  const getDrawer = () => {
    return <Sidebar menuItems={menuItems} userType={undefined} />;
  };

  const steps = [
    'Not Started',
    'In Progress',
    'Application Completed',
    'Interview Scheduled',
    'Interview Completed',
    'Orientation Not Started',
    'Orientation In Progress',
    'Orientation Completed',
    'In Person Orientation Completed',
    'Employee Onboard Completed'
  ];

  if (localStorage.getItem('org_id') === import.meta.env.VITE_FRESNO_ORG_ID) {
    steps.splice(5, 0, 'Schedule Orientation');
    steps.splice(6, 0, 'Orientation Scheduled');
    steps.splice(10, 0, 'In Person Orientation Not Started');
    steps.splice(11, 0, 'In Person Orientation In Progress');
  }

  const handlePageChange = (_event: any, value: any) => {
    setActivePage(value);
  };

  const displayedRows = localEmployeeList.slice(
    (activePage - 1) * rowsPerPage,
    activePage * rowsPerPage
  );

  const tableDataBasedOnRole = [
    {
      title: 'SR HR Manager'
    },
    {
      title: 'Caregiver'
    }
  ];

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID' },
    { field: 'applicant', headerName: 'Applicant', minWidth: 200 },
    { field: 'phone', headerName: 'Phone Number', minWidth: 200 },
    {
      field: 'email',
      headerName: 'Email Address',
      // type: 'number',
      minWidth: 200
    },
    {
      field: 'status',
      headerName: 'Status',
      // description: 'This column has a value getter and is not sortable.',
      // sortable: false,
      minWidth: 180
      // valueGetter: (value, row) =>
      //   `${row.firstName || ''} ${row.lastName || ''}`
    },
    { field: 'action', headerName: 'Action', minWidth: 180 }
  ];

  const rows = [
    { id: 1, applicant: 'Snow', phone: '7658723455', email: '<EMAIL>' },
    {
      id: 2,
      applicant: 'Lannister',
      phone: '6743256897',
      email: '<EMAIL>'
    },
    {
      id: 3,
      applicant: 'Sunni',
      phone: '9872341234',
      email: '<EMAIL>'
    },
    {
      id: 4,
      applicant: 'Stark',
      phone: '9943467234',
      email: '<EMAIL>'
    },
    {
      id: 5,
      applicant: 'Targaryen',
      phone: '6678543123',
      email: '<EMAIL>'
    },
    {
      id: 6,
      applicant: 'Melisandre',
      phone: '7876546754',
      email: '<EMAIL>'
    },
    {
      id: 7,
      applicant: 'Clifford',
      phone: '8341789234',
      email: '<EMAIL>'
    },
    {
      id: 8,
      applicant: 'Frances',
      phone: '9873425646',
      email: '<EMAIL>'
    },
    { id: 9, applicant: 'Roxie', phone: '8765234534', email: '<EMAIL>' }
  ];

  const paginationModel = { page: 0, pageSize: 5 };

  return (
    <Shell subMenu={getSubMenu()} showDrawer drawerData={getDrawer()}>
      {isLoading ? (
        <LoaderUI />
      ) : (
        <Box className="main-container">
          {/* <Box>
            {tableDataBasedOnRole?.map((tableData: any, index: number) => {
              return (
                <Box key={index} sx={{ padding: '20px' }}>
                  <Box
                    sx={{
                      paddingTop: '20px',
                      paddingBottom: '20px',
                      display: 'flex',
                      justifyContent: 'flex-start',
                      alignItems: 'center',
                      gap: '20px'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 600,
                        color: '#27292D',
                        fontSize: 'clamp(1rem, 1.5vw, 1.25rem)'
                      }}
                    >
                      {tableData?.title}
                    </Typography>
                    <Badge badgeContent={4} color="primary" />
                    <SearchWithFilters />
                  </Box>
                  <Paper sx={{ height: 400, width: '100%' }}>
                    <DataGrid
                      rows={rows}
                      columns={columns}
                      initialState={{ pagination: { paginationModel } }}
                      pageSizeOptions={[5, 10]}
                      checkboxSelection
                      sx={{ border: 0 }}
                    />
                  </Paper>
                </Box>
              );
            })}
          </Box> */}
          <Box sx={{ width: '100%', overflowX: 'auto' }}>
            <Box sx={{ backgroundColor: '#FAF9F8' }}>
              <div>
                <Typography className="title">Applicants</Typography>
              </div>
            </Box>

            <Box className="content">
              <Box className="div-styles">
                <Box>
                  {/* <Box className="container">
                    <input
                      name="search"
                      type="text"
                      className="search-bar"
                      placeholder="Search in all columns..."
                    />
                    <Box className="export-buttons">
                      <Link className="export-button" to="/">
                        <IconButton>
                          <InsertDriveFileOutlinedIcon
                            sx={{ color: '#0483BA' }}
                          />
                        </IconButton>
                        <Typography>Export to Excel</Typography>
                      </Link>
                      <Link to="/" className="export-button">
                        <IconButton>
                          <InsertDriveFileOutlinedIcon
                            sx={{ color: '#0483BA' }}
                          />
                        </IconButton>
                        <Typography>Export to PDF</Typography>
                      </Link>
                    </Box>
                  </Box> */}

                  {/* <Box className="drag-column">
                    <Typography>
                      Drag a column header and drop it here to group by that
                      column.
                    </Typography>
                  </Box> */}
                </Box>

                <Box
                  sx={{
                    width: '100%',
                    flexGrow: 1,
                    minHeight: '300px'
                  }}
                >
                  {localEmployeeList?.length > 0 ? (
                    <TableContainer component={Paper}>
                      <Table aria-label="collapsible table">
                        <TableHead>
                          <TableRow>
                            <TableCell>Show</TableCell>
                            <TableCell>Name</TableCell>
                            <TableCell>Email</TableCell>
                            <TableCell>MobileNumber</TableCell>
                            <TableCell>Status</TableCell>
                            {/* <TableCell align="right">
                              FormFillingStatus
                            </TableCell> */}
                            <TableCell align="left">Action</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {displayedRows.map((row: any) => (
                            <Row
                              key={row.name}
                              row={row}
                              steps={steps}
                              interviewCompleted={interviewCompleted}
                              handleRowClick={handleRowClick}
                              handleViewClick={handleViewClick}
                              handleEditClick={handleEditClick}
                              handleOnboardClick={handleOnboardClick}
                              handleReject={handleReject}
                              // handlesetCurrentStatus={handlesetCurrentStatus}
                            />
                          ))}
                        </TableBody>
                      </Table>
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'center',
                          padding: '16px',
                          width: '100%',
                          minHeight: '120px'
                        }}
                      >
                        <Pagination
                          count={totalPages}
                          shape="rounded"
                          page={activePage}
                          onChange={handlePageChange}
                          sx={{
                            '& .MuiPaginationItem-root': {
                              '&.Mui-selected': {
                                backgroundColor: '#29abe2',
                                color: 'white',
                                '&:hover': {
                                  backgroundColor: '#29abe2'
                                }
                              }
                            },
                            '& .MuiPaginationItem-previousNext': {
                              color: '#29abe2'
                            }
                          }}
                        />
                      </Box>
                    </TableContainer>
                  ) : (
                    <Box className="no-applicants">
                      <Typography sx={{ fontSize: '17px', fontWeight: '500' }}>
                        No records found.
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Box>
            </Box>
          </Box>

          <Modal
            open={open}
            onClose={handleClose}
            aria-labelledby="modal-modal-title"
            aria-describedby="modal-modal-description"
          >
            <Box sx={style}>
              <Box style={{ backgroundColor: '#f6f6f6', padding: '20px' }}>
                <RenderStatusStepperModal
                  emp={emp}
                  handleClose={handleClose}
                  currentStatus={emp?.status}
                  stepperIntialValues={stepperIntialValues}
                  setLocalEmployeeList={setLocalEmployeeList}
                />
              </Box>
            </Box>
          </Modal>
        </Box>
      )}
      {/* <SnackbarElement
        message={snackbarOpen?.message}
        statusType={snackbarOpen?.type || 'success'}
        snackbarOpen={snackbarOpen?.status}
        setSnackbarOpen={setSnackbarOpen}
      /> */}
    </Shell>
  );
};
export default EmployeeOnboarding;
