import { Box } from '@mui/material';
import { SubMenu } from '../../form.elements';
import Shell from '../../layout/Shell';
import LoaderUI from '../../reusable/loaderUI';
import { AppState } from '../../../redux/app.store';
import { useSelector } from 'react-redux';
interface ViewPdfProps {
  message: string;
}
const ViewPdf: React.FC<ViewPdfProps> = ({ message }) => {
  const { isLoading } = useSelector((state: AppState) => state.clients);
  const getSubMenu = () => {
    return <SubMenu backNavigation />;
  };
  console.log(message);
  return (
    <Shell subMenu={getSubMenu()}>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box sx={{ padding: '20px 100px' }}>{message ? message : <></>}</Box>
      )}
    </Shell>
  );
};
export default ViewPdf;
