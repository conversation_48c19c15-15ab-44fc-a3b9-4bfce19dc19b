import { PayloadAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import api, { apiRoutes, authHeaders } from '../../apis/config';
import {
  getForm,
  getFormFields,
  getCountriesList,
  getFormSections,
  validatePreviewForm,
  UpdateForm,
  UpdateField,
  AddFieldMethod,
  AddSectionMethod,
  DeleteField,
  DeleteSection,
  DuplicateField,
  DuplicateSection,
  dragAndDropSection,
  getSectionTemplate,
  getThemes,
  addTheme,
  updateTheme,
  activeteTheme,
  UpdateFormStatus,
  UpdateFieldIndexes,
  UpdateSectionIndexes,
  getStatesList,
  getCitiesList,
  updateformLayoutChanges,
  UpdateFormsList,
  toggleFormsList,
  formsRepository,
  getFormResponseAsEmail,
  saveFormResponseAsEmail,
  updateFormResponseAsEmail,
  updateCustomValidations,
  createReuseField
} from '../../apis/forms';
import { SnackBarStoreState } from '../../types';

interface ActiveForm {
  name: string | null;
  description: string | null;
  groups: string[]; // Adjust the type depending on what `groups` should contain
  status: boolean;
}

interface DefaultDict {
  formFields: any | null; // Replace `any` with the specific type if known
  countriesList: any | null; // Replace `any` with the specific type if known
  statesList: any | null; // Replace `any` with the specific type if known
  citiesList: any | null; // Replace `any` with the specific type if known
  formId: string | null;
  appId: string | null;
  orgId: string | null;
  sectionIndex: any;
  columnIndex: any;
  previousSectionIndex: any;
  previousColumnIndex: any;
  isQuizForm: boolean;
  has_sub_forms: boolean;
  parentFormId: string | null;
  isLoading: boolean;
  loadingSpinner: boolean;
  fieldSpinner: boolean;
  sectionSpinner: boolean;
  templateSpinner: boolean;
  userType: string | null;
  formData: any | null; // Replace `any` with the specific type if known
  form: Record<string, any>; // Replace `any` with the specific type if known
  newField: Record<string, any>; // Replace `any` with the specific type if known
  activeForm: ActiveForm;
  sectionTemplates: any[]; // Replace `any` with the specific type if known
  themesData: any;
  theme: any;
  activeTheme: any;
  fieldActive: any;
  selectedCountry: any;
  selectedState: any;
  selectedCity: any;
  tabletLayout: any;
  sectionViewType: any;
  deviceLayoutMode?: any;
  snackbar: SnackBarStoreState;
}

const defaultDict: DefaultDict = {
  formFields: null,
  countriesList: null,
  statesList: null,
  citiesList: null,
  formId: null,
  appId: null,
  orgId: null,
  sectionIndex: 0,
  columnIndex: 0,
  previousSectionIndex: 0,
  previousColumnIndex: 0,
  isQuizForm: false,
  has_sub_forms: false,
  parentFormId: null,
  isLoading: false,
  loadingSpinner: false,
  fieldSpinner: false,
  sectionSpinner: false,
  templateSpinner: false,
  userType: null,
  formData: null,
  form: {},
  newField: {},
  activeForm: {
    name: null,
    description: null,
    groups: [],
    status: false
  },
  sectionTemplates: [],
  themesData: [],
  theme: {},
  activeTheme: {},
  fieldActive: {},
  selectedCountry: null,
  selectedState: null,
  selectedCity: null,
  tabletLayout: null,
  sectionViewType: null,
  deviceLayoutMode: null,
  snackbar: {
    snackbarMessage: '',
    snackbarSeverity: 'success',
    snackbarOpen: false,
    setSnackbarOpen: null
  }
};

const initialState = localStorage.getItem('access_token')
  ? {
      ...defaultDict,
      userType: localStorage.getItem('user_type'),
      appId: localStorage.getItem('app_id'),
      orgId: localStorage.getItem('org_id') || null
    }
  : defaultDict;

export const getformwithformid = createAsyncThunk('getformwithformid', getForm);
export const getform = createAsyncThunk('getform', getForm);
export const refetchform = createAsyncThunk('refetchform', getForm);
export const getsectiontemplate = createAsyncThunk(
  'getsectiontemplate',
  getSectionTemplate
);
export const updateform = createAsyncThunk('updateform', UpdateForm);
export const updateformslist = createAsyncThunk(
  'updateformslist',
  UpdateFormsList
);
export const toggleformslist = createAsyncThunk(
  'toggleformslist',
  toggleFormsList
);
export const formsrepository = createAsyncThunk(
  'formsrepository',
  formsRepository
);
export const getformswithappid = createAsyncThunk(
  'getformswithappid',
  formsRepository
);

export const addSectionMethod = createAsyncThunk(
  'addSectionMethod',
  AddSectionMethod
);
export const addFieldMethod = createAsyncThunk(
  'addFieldMethod',
  AddFieldMethod
);
export const updatefield = createAsyncThunk('updatefield', UpdateField);
export const updateFieldIndexes = createAsyncThunk(
  'updateFieldIndexes',
  UpdateFieldIndexes
);
export const updateSectionIndexes = createAsyncThunk(
  'updateSectionIndexes',
  UpdateSectionIndexes
);
export const updateformstatus = createAsyncThunk(
  'updateformstatus',
  UpdateFormStatus
);
export const draganddropsection = createAsyncThunk(
  'draganddropsection',
  dragAndDropSection
);
export const deletefield = createAsyncThunk('deletefield', DeleteField);
export const duplicatefield = createAsyncThunk(
  'duplicatefield',
  DuplicateField
);
export const deletesection = createAsyncThunk('deletesection', DeleteSection);
export const duplicatesection = createAsyncThunk(
  'duplicatesection',
  DuplicateSection
);
export const getformFields = createAsyncThunk('getformFields', getFormFields);
export const getcountrieslist = createAsyncThunk(
  'getcountrieslist',
  getCountriesList
);
export const getstateslist = createAsyncThunk('getstateslist', getStatesList);
export const getcitieslist = createAsyncThunk('getcitieslist', getCitiesList);
export const getformsections = createAsyncThunk(
  'getformsections',
  getFormSections
);
export const validatepreviewform = createAsyncThunk(
  'validatepreviewform',
  validatePreviewForm
);
export const getthemes = createAsyncThunk('getthemes', getThemes);
export const addtheme = createAsyncThunk('addtheme', addTheme);
export const updatetheme = createAsyncThunk('updatetheme', updateTheme);
export const activatetheme = createAsyncThunk('activatetheme', activeteTheme);
export const formlayoutchanges = createAsyncThunk(
  'formlayoutchanges',
  updateformLayoutChanges
);
export const getformresponseasemail = createAsyncThunk(
  'getformresponseasemail',
  getFormResponseAsEmail
);
export const saveformresponseasemail = createAsyncThunk(
  'saveformresponseasemail',
  saveFormResponseAsEmail
);
export const updateformresponseasemail = createAsyncThunk(
  'updateformresponseasemail',
  updateFormResponseAsEmail
);
export const updatecustomvalidations = createAsyncThunk(
  'updatecustomvalidations',
  updateCustomValidations
);

// --- Reusable Field Reducer Actions ---
export const createreusefield = createAsyncThunk(
  'createReuseField',
  createReuseField
);

const formSlice = createSlice({
  name: 'form',
  initialState,
  reducers: {
    updateOrgId: (state, action) => {
      state.orgId = action.payload;
    },
    updateAppId: (state, action) => {
      state.appId = action.payload;
    },
    updateFormId: (state, action) => {
      state.formId = action.payload;
    },
    updateSectionIndex: (state, action) => {
      state.previousSectionIndex = state.sectionIndex;
      state.sectionIndex = action.payload;
    },
    updatePreviousSectionIndex: (state) => {
      state.previousSectionIndex = state.sectionIndex;
    },
    updateColumnIndex: (state, action) => {
      state.previousColumnIndex = state.columnIndex;
      state.columnIndex = action.payload;
    },
    updatePreviousColumnIndex: (state) => {
      state.previousColumnIndex = state.columnIndex;
    },
    updateQuizStatus: (state, action) => {
      state.isQuizForm = action.payload.form.is_quiz_form;
    },
    formLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    loadSpinner: (state, action) => {
      state.loadingSpinner = action.payload;
    },
    loadFieldSpinner: (state, action) => {
      state.fieldSpinner = action.payload;
    },
    loadSectionSpinner: (state, action) => {
      state.sectionSpinner = action.payload;
    },
    loadTemplateSpinner: (state, action) => {
      state.templateSpinner = action.payload;
    },
    formData: (state, action: PayloadAction<{ form: any }>) => {
      state.formData = action.payload.form;
      state.formId = action?.payload?.form?.form_id;
    },
    updateActiveForm: (state: any, action) => {
      state.activeForm = action.payload.form;
    },
    updateSnackbar: (state: any, action) => {
      state.snackbar = action.payload;
    },
    updateSelectedCountry: (state: any, action) => {
      state.selectedCountry = action.payload;
    },
    updateSelectedState: (state: any, action) => {
      state.selectedState = action.payload;
    },
    updateSelectedCity: (state: any, action) => {
      state.selectedCity = action.payload;
    },
    updateFormLayout: (state: any, action) => {
      state.tabletLayout = action.payload;
    },
    updateSectionViewType: (state: any, action) => {
      state.sectionViewType = action.payload;
    },
    updateDeviceLayoutType: (state: any, action) => {
      state.deviceLayoutMode = action.payload;
    },

    updateChanges: (state, action) => {
      const { name, data } = action.payload;
      const keys = name.split(/[[\].]+/).filter(Boolean);

      // Handle top-level `name` field update
      if (keys.length === 1) {
        if (name === 'name') {
          state.form.name = data.name;
        } else if (name === 'description') {
          state.form.description = data.description;
        } else if (name === 'icon') {
          state.form.icon = data.icon;
        }
        return;
      }

      // Handle nested group field updates
      if (keys.length === 3) {
        const groupIndex = parseInt(keys[1], 10);
        // const fieldKey = keys[2];

        if (!state.form.groups || !state.form.groups[groupIndex]) return;

        state.form.groups = state.form.groups.map((group: any, i: number) =>
          i === groupIndex
            ? {
                ...action.payload?.data?.fields[group?.group_key]
              }
            : group
        );
      }
    },

    updateFieldActive: (state, action) => {
      state.fieldActive = action.payload;
      state.form = {
        ...state.form,
        groups: state.form.groups.map((group: any, i: number) =>
          i === state.sectionIndex
            ? {
                ...group,
                fields: group.fields.map((field: any, j: number) =>
                  j === state.columnIndex ? action.payload : field
                )
              }
            : group
        )
      };
    },

    addFormField: (state, action) => {
      const { sectionIndex, columnIndex, data } = action.payload;

      return {
        ...state,
        form: {
          ...state.form,
          groups: state.form.groups.map((group: any, i: number) =>
            i === sectionIndex
              ? {
                  ...group,
                  fields: [
                    ...group.fields.slice(0, columnIndex + 1),
                    data,
                    ...group.fields.slice(columnIndex + 1)
                  ]
                }
              : group
          )
        }
      };
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getform.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getform.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;

        if (data) {
          data.groups = Array.isArray(data.groups) ? data.groups : [];
        }

        state.form = data;
        state.activeForm = data;
        state.formId = data?.form_id;
        state.appId = data?.app_id;
        state.isQuizForm = !!data?.is_quiz_form;
        state.has_sub_forms = !!data?.has_sub_forms;
        state.sectionIndex = data?.groups?.length ? data.groups.length - 1 : 0;
        state.columnIndex =
          Array.isArray(data?.groups) &&
          data.groups.length > 0 &&
          data.groups[data.groups.length - 1] &&
          Array.isArray(data.groups[data.groups.length - 1].fields)
            ? data.groups[data.groups.length - 1].fields.length - 1
            : 0;
      })
      .addCase(getform.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      // .addCase(refetchform.pending, (state) => {
      //   // state.isLoading = true;
      // })
      .addCase(refetchform.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;

        if (data) {
          data.groups = Array.isArray(data.groups) ? data.groups : [];
        }

        state.form = data;
      })
      .addCase(refetchform.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(getsectiontemplate.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getsectiontemplate.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(getsectiontemplate.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });

    // builder
    // .addCase(updateform.pending, (state) => {
    //   // state.isLoading = true;
    // })

    builder
      .addCase(updateform.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        // state.form = data;
        state.activeForm = data;
        state.appId = data.app_id;
      })
      .addCase(updateform.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });

    builder
      .addCase(updateformslist.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateformslist.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        // state.form = data;
        state.activeForm = data;
        state.appId = data.app_id;
      })
      .addCase(updateformslist.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });

    builder.addCase(toggleformslist.fulfilled, (state, action) => {
      state.isLoading = false;
      const { data } = action.payload;
      state.activeForm = data;
      state.appId = data.app_id;
    });

    builder
      .addCase(formsrepository.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(formsrepository.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        console.log('Formssss: ', data);
        // state.form = data;
        state.activeForm = data;
        state.appId = data.app_id;
      })
      .addCase(formsrepository.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });

    builder
      .addCase(getformswithappid.pending, (state) => {
        state.isLoading = false;
      })
      .addCase(getformswithappid.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        console.log('Formssss: ', data);
        // state.form = data;
        state.activeForm = data;
        state.appId = data.app_id;
      })
      .addCase(getformswithappid.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });

    builder
      .addCase(addSectionMethod.pending, (state) => {
        state.isLoading = false;
      })
      .addCase(addSectionMethod.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(addSectionMethod.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(addFieldMethod.pending, () => {
        // state.loadingSpinner = false;
      })
      .addCase(addFieldMethod.fulfilled, () => {
        // state.loadingSpinner = false;
      })
      .addCase(addFieldMethod.rejected, (state) => {
        state.fieldSpinner = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(updateFieldIndexes.pending, () => {
        // state.loadingSpinner = false;
      })
      .addCase(updateFieldIndexes.fulfilled, () => {
        // state.loadingSpinner = false;
      })
      .addCase(updateFieldIndexes.rejected, (state) => {
        state.fieldSpinner = false;
      });
    builder
      .addCase(updatefield.pending, () => {
        // state.isLoading = true;
      })
      .addCase(updatefield.fulfilled, () => {})
      .addCase(updatefield.rejected, () => {});
    builder
      .addCase(updateformstatus.pending, () => {})
      .addCase(updateformstatus.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(updateformstatus.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(draganddropsection.pending, () => {
        // state.isLoading = true;
      })
      .addCase(draganddropsection.fulfilled, () => {})
      .addCase(draganddropsection.rejected, () => {});
    builder
      // .addCase(deletefield.pending, (state) => {
      //   // state.isLoading = true;
      // })
      .addCase(deletefield.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(deletefield.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      // .addCase(duplicatefield.pending, (state) => {
      //   // state.isLoading = true;
      // })
      .addCase(duplicatefield.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(duplicatefield.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      // .addCase(deletesection.pending, (state) => {
      //   // state.isLoading = true;
      // })
      .addCase(deletesection.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(deletesection.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getformFields.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getformFields.fulfilled, (state, action) => {
        state.isLoading = false;
        state.formFields = action.payload;
      })
      .addCase(getformFields.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getcountrieslist.pending, () => {
        // state.isLoading = true;
      })
      .addCase(getcountrieslist.fulfilled, (state, action) => {
        state.isLoading = false;
        state.countriesList = action.payload;
      })
      .addCase(getcountrieslist.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getstateslist.pending, () => {
        // state.isLoading = true;
      })
      .addCase(getstateslist.fulfilled, (state, action) => {
        state.isLoading = false;
        state.statesList = action.payload;
      })
      .addCase(getstateslist.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getcitieslist.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getcitieslist.fulfilled, (state, action) => {
        state.isLoading = false;
        state.citiesList = action.payload;
      })
      .addCase(getcitieslist.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    // builder
    //   .addCase(getformsections.pending, (state) => {
    //     state.isLoading = true;
    //   })
    //   .addCase(getformsections.fulfilled, (state, action) => {
    //     state.isLoading = false;
    //     const filteredData =
    //       action.payload.data?.filter((form: any) => !form?.deleted_at) || [];
    //     state.sectionTemplates = filteredData;
    //   })
    //   .addCase(getformsections.rejected, (state) => {
    //     state.isLoading = false;
    //     // state.loadingError = action.payload;
    //   });
    builder
      .addCase(validatepreviewform.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(validatepreviewform.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(validatepreviewform.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getthemes.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getthemes.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.themesData = data;
        if (data.length > 0) {
          const activeTheme = data?.find((theme: any) => theme.status);
          if (activeTheme) state.activeTheme = activeTheme;
        }
      })
      .addCase(getthemes.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(addtheme.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addtheme.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.theme = data;
      })
      .addCase(addtheme.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(updatetheme.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updatetheme.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.theme = data;
      })
      .addCase(updatetheme.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(activatetheme.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(activatetheme.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(activatetheme.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(getformresponseasemail.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getformresponseasemail.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(getformresponseasemail.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(saveformresponseasemail.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(saveformresponseasemail.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(saveformresponseasemail.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(updateformresponseasemail.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateformresponseasemail.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(updateformresponseasemail.rejected, (state) => {
        state.isLoading = false;
      });
  }
});
export default formSlice.reducer;

export const {
  updateOrgId,
  updateAppId,
  updateFormId,
  updateSectionIndex,
  updatePreviousSectionIndex,
  updateColumnIndex,
  updatePreviousColumnIndex,
  updateQuizStatus,
  formLoading,
  loadSpinner,
  loadFieldSpinner,
  loadSectionSpinner,
  loadTemplateSpinner,
  formData,
  updateActiveForm,
  updateFieldActive,
  updateChanges,
  updateSnackbar,
  addFormField,
  updateSelectedCountry,
  updateSelectedState,
  updateSelectedCity,
  updateFormLayout,
  updateSectionViewType,
  updateDeviceLayoutType
} = formSlice.actions;

export const fetchFormData = (formId: any) => async (dispatch: any) => {
  try {
    const headers = await authHeaders();
    const { data } = await api.get(`${apiRoutes.form}/${formId}`, {
      headers
    });

    if (data.data) {
      data.data.groups = Array.isArray(data.data.groups)
        ? data.data.groups
        : [];
    }

    dispatch(formData({ form: data.data }));
    dispatch(updateAppId(data.data.app_id));
    if (data?.data?.isQuizForm) {
      dispatch(updateQuizStatus(data?.data?.isQuizForm));
    }

    if (Array.isArray(data.data.groups) && data.data.groups.length > 0) {
      dispatch(updateSectionIndex(data.data.groups.length - 1));
      const lastGroup = data.data.groups[data.data.groups.length - 1];
      if (lastGroup && Array.isArray(lastGroup.fields)) {
        dispatch(updateColumnIndex(lastGroup.fields.length - 1));
      } else {
        dispatch(updateColumnIndex(0));
      }
    } else {
      dispatch(updateSectionIndex(0));
      dispatch(updateColumnIndex(0));
    }
  } catch (error) {
    dispatch(formData({ form: null }));
  }
};

//
