// Package Imports
import {
  Box,
  Button,
  Card,
  CardMedia,
  CircularProgress,
  Dialog,
  // DialogActions,
  DialogContent,
  // DialogContentText,
  DialogTitle,
  // FormControl,
  // FormControlLabel,
  // FormHelperText,
  Grid,
  // Input,
  // Radio,
  // RadioGroup,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme
} from '@mui/material';
import { Delete, Upload } from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import {
  useEffect,
  // useRef,
  useState
} from 'react';
import * as Yup from 'yup';
import { toast } from 'react-toastify';

// Locale Imports
import { AppForm, FormInput, Icon } from '../form.elements';
import { RootState } from '../../redux/reducers';
import { FormObject } from '../../types/form-types';
import { AppDispatch } from '../../redux/app.store';
import {
  addFieldMethod,
  addSectionMethod,
  loadTemplateSpinner,
  refetchform,
  updateChanges,
  updateColumnIndex,
  updateform,
  updatePreviousColumnIndex,
  updatePreviousSectionIndex,
  updateSectionIndex,
  updateSnackbar
} from '../../redux/reducers/form.reducer';
import { RenderGroup } from '../reusable/CreateForm.elements';
import { numberToWords, toCamelCase } from '../reusable/StringToNumber';
import { FormContainerTypes } from '../../types';
import LoaderUI from '../reusable/loaderUI';

import '../../css/org-setup.scss/organizationRegistrationForm.scss';
// import { autocreate } from '../../redux/reducers/apps.reducer';
// import { LoadingButton } from '@mui/lab';
import formSvgIcons from '../../utils/form_svg_icons';

const FormContainer: React.FC<FormContainerTypes> = ({
  showTemplate,
  setShowTemplate,
  selectedTemplate
}) => {
  const {
    form,
    formId,
    isLoading,
    sectionIndex,
    columnIndex,
    isQuizForm,
    formFields
  }: any = useSelector((state: RootState) => state.form);

  const dispatch = useDispatch<AppDispatch>();

  const [formData, setFormData] = useState<FormObject>({
    name: '',
    description: '',
    icon: '',
    groups: [],
    status: false
  });

  const small: any = {
    zero: 0,
    one: 1,
    two: 2,
    three: 3,
    four: 4,
    five: 5,
    six: 6,
    seven: 7,
    eight: 8,
    nine: 9,
    ten: 10,
    eleven: 11,
    twelve: 12,
    thirteen: 13,
    fourteen: 14,
    fifteen: 15,
    sixteen: 16,
    seventeen: 17,
    eighteen: 18,
    nineteen: 19,
    twenty: 20,
    twentyone: 21,
    twentytwo: 22,
    twentythree: 23,
    twentyfour: 24,
    twentyfive: 25,
    twentysix: 26,
    twentyseven: 27,
    twentyeight: 28,
    twentynine: 29,
    thirty: 30,
    forty: 40,
    fifty: 50,
    sixty: 60,
    seventy: 70,
    eighty: 80,
    ninety: 90
  };

  const addSectionTemplate = async (template: any) => {
    dispatch(updatePreviousSectionIndex(sectionIndex));
    dispatch(updatePreviousColumnIndex(columnIndex));

    const getSectionNumber = formData.groups[
      formData.groups.length - 1
    ].group_key
      .split('_')[1]
      .toLowerCase();

    const newSection = {
      id: '',
      group_title: template?.name,
      group_key: `section_${numberToWords[small[toCamelCase(getSectionNumber)] + 1]}`,
      group_index: '',
      group_description: template?.description,
      is_iterative_or_not: false,
      iteration_min_length: 1,
      iteration_max_length: 2,
      fields: template?.fields
    };

    const secIdx = formData.groups.length;

    const data = {
      group: {
        key: newSection?.group_key,
        title: newSection?.group_title,
        group_description: newSection?.group_description,
        is_iterative_or_not: newSection?.is_iterative_or_not,
        iteration_max_length: newSection?.iteration_max_length,
        iteration_min_length: newSection?.iteration_min_length
      }
    };
    try {
      const response = await dispatch(addSectionMethod({ data, formId }));

      if (response.payload.status) {
        const city = formFields?.find(
          (s: any) => s.skelton.input_type === 'city'
        );

        template.fields.forEach(async (field: any, index: number) => {
          const newField = {
            name: field?.name,
            field_index: index,
            field_id: '',
            original_field_id: field?.original_field_id,
            type: field?.type,
            input_type: field?.input_type,
            label: field?.label,
            label_url: '',
            label_url_type: '',
            placeHolder: field?.placeholder,
            description_status: field?.description_status,
            description: field?.description,
            validation_schema: {
              required: field?.validation_schema?.required
            },
            options: field?.options,
            is_iterative_or_not: field?.is_iterative_or_not,
            iteration_min_length: field?.iteration_min_length || 1,
            iteration_max_length: field?.iteration_max_length || 2,
            points: 0,
            checkbox_answer_type: 'No limit',
            checkbox_answer_limit: '',
            is_quiz_field: false,
            default_country: city ? city.skelton.default_country : '',
            default_state: city ? city.skelton.default_state : '',
            signature_action_by: field?.signature_action_by || 'user'
          };

          const fieldData = {
            field: newField,
            group_title: newSection?.group_title,
            group_key: newSection?.group_key
          };

          const res = await dispatch(addFieldMethod({ fieldData, formId }));
          if (res.payload.status && template.fields.length === index + 1) {
            if (sectionIndex !== secIdx) {
              dispatch(updateSectionIndex(secIdx));
            }
            if (columnIndex !== template.fields.length - 1) {
              dispatch(updateColumnIndex(template.fields.length - 1));
            }
            if (index === template.fields.length - 1) {
              dispatch(refetchform(formId)).then(() => {
                const snackbarmsg = {
                  snackbarMessage: 'Template selected successfully',
                  snackbarSeverity: 'success',
                  snackbarOpen: true,
                  setSnackbarOpen: null
                };
                dispatch(updateSnackbar(snackbarmsg));
                dispatch(loadTemplateSpinner(false));
              });
            }
          } else {
            dispatch(
              updateSnackbar({
                open: true,
                message:
                  res.payload.message ||
                  'Something Went Wrong, Please Try Again Later.',
                severity: 'error'
              })
            );
          }
        });
      } else {
        dispatch(
          updateSnackbar({
            open: true,
            message:
              response.payload.message ||
              'Something Went Wrong, Please Try Again Later.',
            severity: 'error'
          })
        );
      }
    } catch (e) {
      dispatch(
        updateSnackbar({
          open: true,
          message: e || 'Something Went Wrong, Please Try Again Later.',
          severity: 'error'
        })
      );
    }
  };

  useEffect(() => {
    const data = {
      name: form?.name || '',
      description: form?.description || '',
      icon: form?.icon || null,
      groups: form?.groups || [],
      status: form?.status || false
    };

    setFormData(data);
  }, [form]);

  useEffect(() => {
    if (selectedTemplate) {
      addSectionTemplate(selectedTemplate);
    }
  }, [selectedTemplate]);

  const validationSchema = Yup.object().shape({
    name: Yup.string().required('Please enter form name'),
    groups: Yup.array()
      .of(
        Yup.object().shape({
          group_title: Yup.string().required('Group title is required'),
          iteration_max_length: Yup.string()
            .matches(
              /^(?!-)(?!0$)(?!1$)\d+(\.\d+)?$/,
              'Only positive values greater than 1 are allowed'
            )
            .required('Repeat Length is required'),
          fields: Yup.array()
            .of(
              Yup.object().shape({
                // label: Yup.string().required('Label is required'),
              })
            )
            .min(1, 'At least one field is required')
        })
      )
      .min(1, 'At least one group is required')
  });

  const submitForm = () => {};

  const handleInputChange = async (e: any, values?: any) => {
    const { name, value } = e.target;

    if (value.trim() === '') {
      toast.warning('Empty or whitespace value detected');
      return;
    }

    const keys = name.split(/[[\].]+/).filter(Boolean);
    const updatedValues = { ...values };

    let obj = updatedValues;
    keys.forEach((key: any, index: number) => {
      if (index === keys.length - 1) {
        obj[key] = value;
      } else {
        obj[key] = { ...obj[key] };
        obj = obj[key];
      }
    });

    try {
      if (name === 'name' || name === 'description') {
        if ((name === 'name' && value !== '') || name === 'description') {
          const res = await dispatch(
            updateform({ value, values, name, formId })
          );
          if (res.payload.status) {
            dispatch(
              updateChanges({
                name,
                data: res.payload.data
              })
            );
          } else {
            dispatch(
              updateSnackbar({
                open: true,
                message:
                  res.payload.message ||
                  'Something Went Wrong, Please Try Again Later.',
                severity: 'error'
              })
            );
          }
        }
      } else if (name.endsWith('is_iterative_or_not')) {
        const groupIndex = parseInt(keys[1], 10);
        const data = {
          name: formData.name,
          description: formData.description,
          group: {
            index: formData.groups[groupIndex].group_index,
            key: formData.groups[groupIndex].group_key,
            title: formData.groups[groupIndex].group_title,
            description: formData.groups[groupIndex].group_description,
            is_iterative_or_not: e.target.checked,
            iteration_max_length: e.target.checked
              ? formData.groups[groupIndex].iteration_max_length || 2
              : formData.groups[groupIndex].iteration_max_length,
            iteration_min_length:
              formData.groups[groupIndex].iteration_min_length || 1
          },
          status: formData.status
        };

        const res = await dispatch(
          updateform({ value, values: data, name, formId })
        );

        if (res.payload.status) {
          dispatch(
            updateChanges({
              name,
              data: res.payload.data
            })
          );
        } else {
          dispatch(
            updateSnackbar({
              open: true,
              message:
                res.payload.message ||
                'Something Went Wrong, Please Try Again Later.',
              severity: 'error'
            })
          );
        }
      } else {
        const data = {
          name: updatedValues.name,
          description: updatedValues.description,
          group: {
            index: updatedValues.groups[parseInt(keys[1], 10)].group_index,
            key: updatedValues.groups[parseInt(keys[1], 10)].group_key,
            title: updatedValues.groups[parseInt(keys[1], 10)].group_title,
            description:
              updatedValues.groups[parseInt(keys[1], 10)].group_description,
            is_iterative_or_not:
              updatedValues.groups[parseInt(keys[1], 10)].is_iterative_or_not,
            iteration_max_length:
              updatedValues.groups[parseInt(keys[1], 10)].iteration_max_length <
              2
                ? 2
                : Number(
                    value ||
                      updatedValues.groups[parseInt(keys[1], 10)]
                        .iteration_max_length
                  ) || 2,
            iteration_min_length:
              updatedValues.groups[parseInt(keys[1], 10)].iteration_min_length
          },
          status: updatedValues.status
        };

        const res = await dispatch(
          updateform({ value, values: data, name, formId })
        );

        if (res.payload.status) {
          dispatch(
            updateChanges({
              name,
              data: res.payload.data
            })
          );
        } else {
          dispatch(
            updateSnackbar({
              open: true,
              message:
                res.payload.message ||
                'Something Went Wrong, Please Try Again Later.',
              severity: 'error'
            })
          );
        }
      }
    } catch (error) {
      dispatch(
        updateSnackbar({
          open: true,
          message: 'Something Went Wrong, Please Try Again Later.',
          severity: 'error'
        })
      );
    }
  };

  const [openIconDialog, setOpenIconDialog] = useState(false);
  const [handleFormIconLoading, setHandleFormIconLoading] = useState(false);

  const handleFormIconChange = async (value: string) => {
    try {
      setHandleFormIconLoading(true);
      const response = await dispatch(
        updateform({ value, values: formData, name: 'icon', formId })
      );
      setHandleFormIconLoading(false);
      if (response.payload.status) {
        setOpenIconDialog(false);
        dispatch(
          updateChanges({
            name: 'icon',
            data: response.payload.data
          })
        );
      } else {
        toast.error(
          response?.payload?.message ||
            'Something Went Wrong, Please Try Again Later.'
        );
      }
    } catch (error) {
      setHandleFormIconLoading(false);
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  // const inputLogo: any = useRef(null);
  // const [logo, setLogo]: any = useState(null);

  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('sm'));
  // const [error, setError] = useState('');

  const handleClose = () => {
    setOpenIconDialog(false);
  };
  const handleIconDialogOpen = () => {
    setOpenIconDialog(true);
  };

  const deleteLogo = async () => {
    handleFormIconChange('');
  };

  return isLoading || handleFormIconLoading ? (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        backgroundColor: 'rgba(255, 255, 255, 0.5)',
        zIndex: 1300
      }}
    >
      <CircularProgress />
    </Box>
  ) : (
    formId && (
      <Box
        className={`${
          showTemplate !== '' ? 'w-template' : 'w-full'
        } position-relative h-full form-container overflow-auto`}
      >
        {!isQuizForm && (
          <Box
            className={`left-action-buttons ${showTemplate !== '' && 'left-310'}`}
          >
            {/* <Tooltip title="Suggestions">
              <Button
                className="mt-3"
                onClick={() => setShowTemplate('suggestion')}
                variant="contained"
                startIcon={<Icon name="CollectionsBookmark" />}
              />
            </Tooltip> */}
            <Tooltip title="Section Template" arrow>
              <Button
                className="mt-3"
                onClick={() => setShowTemplate('sectionTemplate')}
                variant="contained"
                startIcon={<Icon name="Subtitles" />}
              />
            </Tooltip>
          </Box>
        )}

        <Box className="flex-1">
          <AppForm
            initialValues={formData}
            validationSchema={validationSchema}
            onSubmit={submitForm}
          >
            <Box className="form">
              <Box className="form-title-card d-flex flex-column mb-3 pt-5 align-items-center">
                <Grid container spacing={2}>
                  <Grid item xs={4} sm={3} md={2}>
                    <Box className="org-registn-upload-logo-box">
                      <Box className="d-flex">
                        <Card
                          className="org-registn-upload-logo-card"
                          onClick={handleIconDialogOpen}
                        >
                          <Box className="org-registn-logo-box">
                            <CardMedia
                              className="org-registn-logo-media p-4"
                              image={
                                formData?.icon ||
                                `https://ui-avatars.com/api/?name=${encodeURIComponent(
                                  'upload logo'
                                )}&size=128&rounded=true`
                              }
                              title="Logo"
                            />
                          </Box>
                          <Upload className="org-registn-upload-icon" />
                        </Card>
                        <Box>
                          {formData?.icon && (
                            <Delete
                              onClick={deleteLogo}
                              sx={{
                                color: 'red2.light',
                                cursor: 'pointer'
                              }}
                            />
                          )}
                        </Box>
                      </Box>
                    </Box>

                    <Dialog
                      fullScreen={fullScreen}
                      open={openIconDialog}
                      onClose={handleClose}
                      aria-labelledby="customized-dialog-title"
                      sx={{
                        '& .MuiDialog-paper': {
                          borderRadius: '20px',
                          padding: '20px',
                          backgroundColor: '#ece9e6',
                          textTransform: 'capitalize',
                          maxHeight: '90vh', // Prevent the dialog from exceeding the viewport height
                          overflow: 'hidden' // Prevent the dialog itself from scrolling
                        }
                      }}
                      PaperProps={{
                        component: 'form',
                        onSubmit: async (
                          event: React.FormEvent<HTMLFormElement>
                        ) => {
                          event.preventDefault();
                        }
                      }}
                    >
                      <DialogTitle
                        id="customized-dialog-title"
                        sx={{
                          fontSize: '18px',
                          fontWeight: 'bold',
                          textAlign: 'center',
                          color: '#08366B',
                          marginBottom: '0px' // Remove extra space below the title
                        }}
                      >
                        Select Form Icon
                      </DialogTitle>

                      <DialogContent
                        dividers
                        sx={{
                          maxHeight: '70vh', // Limit the height of the scrollable content
                          overflowY: 'auto', // Enable vertical scrolling for the content
                          paddingRight: '10px',
                          paddingTop: '0px' // Remove extra padding at the top
                        }}
                      >
                        {formSvgIcons.map(
                          (
                            iconsByCategory: { name: string; images: any },
                            index: number
                          ) => (
                            <Box
                              key={`${index + 1}`}
                              className="d-flex flex-column"
                              sx={{
                                gap: '10px',
                                justifyContent: 'center'
                              }}
                            >
                              {/* Sticky Category Name */}
                              <Typography
                                sx={{
                                  fontSize: '16px',
                                  fontWeight: 'bold',
                                  color: '#08366B',
                                  marginBottom: '10px',
                                  position: 'sticky', // Make the category name sticky
                                  top: 0, // Stick to the top of the scrollable container
                                  backgroundColor: '#ece9e6', // Match the background color of the dialog
                                  zIndex: 1, // Ensure it stays above other content
                                  padding: '5px 0', // Add some padding for better visibility
                                  borderBottom: '1px solid #dcdcdc' // Optional: Add a subtle border for better separation
                                }}
                              >
                                {iconsByCategory.name}
                              </Typography>

                              {/* Icons Container */}
                              <Box className="d-flex flex-wrap">
                                {iconsByCategory.images.map(
                                  (icon: any, iconIndex: number) => {
                                    const dataUrl = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(icon.svg)}`;
                                    return (
                                      // <Tooltip
                                      //   title="Tap here to select"
                                      //   key={`${iconIndex + 1}`}
                                      //   arrow
                                      // >
                                      <Box
                                        key={`${iconIndex + 1}`}
                                        sx={{
                                          margin: '7px',
                                          padding: '2px',
                                          backgroundColor: 'white',
                                          border:
                                            formData.icon === icon.svg
                                              ? '2px solid #36C0ED'
                                              : '1px solid transparent',
                                          cursor: 'pointer' // Add pointer cursor for selection
                                        }}
                                      >
                                        {/* Icon Image */}
                                        <CardMedia
                                          component="img"
                                          image={dataUrl}
                                          alt={icon.name}
                                          sx={{
                                            height: 80,
                                            width: 80,
                                            margin: 'auto'
                                          }}
                                          onClick={() =>
                                            handleFormIconChange(dataUrl)
                                          }
                                        />
                                      </Box>
                                      // </Tooltip>
                                    );
                                  }
                                )}
                              </Box>
                            </Box>
                          )
                        )}
                      </DialogContent>
                    </Dialog>
                  </Grid>
                  <Grid item xs={8} sm={9} md={10}>
                    <Box className="d-flex flex-column">
                      <FormInput
                        name="name"
                        className="form-title-field"
                        label=""
                        autoComplete="off"
                        placeholder="Form Title"
                        isCreateForm
                        fullWidth
                        handleInputChange={handleInputChange}
                        containerStyles={{
                          marginBottom: '0px'
                        }}
                      />
                      <FormInput
                        name="description"
                        label=""
                        placeholder="Description"
                        autoComplete="off"
                        className="description-title-field"
                        isCreateForm
                        handleInputChange={handleInputChange}
                        containerStyles={{
                          marginBottom: '0px'
                        }}
                      />
                    </Box>
                  </Grid>
                </Grid>
              </Box>
              {isLoading && <LoaderUI />}
              {!isLoading && (
                <Box className="w-100">
                  {formData &&
                    formData.groups
                      .slice()
                      .sort(
                        (prev: any, next: any) =>
                          parseInt(prev.group_index, 10) -
                          parseInt(next.group_index, 10)
                      )
                      .map((group: any, index: number) => {
                        const groupKey = `groups[${index}].group_key`;
                        const groupTitleName = `groups[${index}].group_title`;
                        const groupDescriptionName = `groups[${index}].group_description`;
                        const groupIteration = `groups[${index}].is_iterative_or_not`;
                        const groupMaxIterationLength = `groups[${index}].iteration_max_length`;
                        const key = `${index}-${index * 2}-group-key`;

                        return (
                          <RenderGroup
                            formData={formData}
                            group={group}
                            groupKey={groupKey}
                            groupsCount={formData.groups.length}
                            groupTitle={groupTitleName}
                            groupDescription={groupDescriptionName}
                            groupIteration={groupIteration}
                            groupMaxIterationLength={groupMaxIterationLength}
                            handleInputChange={handleInputChange}
                            key={key + 1}
                            secIndex={index}
                          />
                        );
                      })}
                </Box>
              )}

              <Box
                sx={{
                  height: '100px',
                  width: '100%'
                }}
              />
            </Box>
          </AppForm>
        </Box>
      </Box>
    )
  );
};

export default FormContainer;
