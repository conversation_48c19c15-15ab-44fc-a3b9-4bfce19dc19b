import { Box, Tab, Tabs, Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import { ChevronRight } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { Icon } from '../form.elements';
import '../../css/apps-page-styles.scss';
import '../../css/index.scss';

const Sidebar: React.FC<{
  children?: React.ReactNode;
  menuItems: any;
  userType: any;
}> = ({ children, menuItems, userType }) => {
  const [value, setValue] = useState(0);
  const navigate = useNavigate();

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
    const url = menuItems[newValue]?.url;
    if (event && url) {
      navigate(url);
    }
  };

  useEffect(() => {
    const currentPath = window.location.pathname;
    const currentTab = menuItems.findIndex(
      (item: any) => item.url === currentPath
    );
    if (currentTab !== -1) {
      setValue(currentTab);
    }
  }, [menuItems]);

  if (userType === 'super_admin') {
    return null;
  }

  return (
    <Box className="d-flex h-full bg-FAF9F8 bg-fff">
      <Box
        className="custom-sidebar h-full"
        sx={{
          width: '250px',
          overflowY: 'auto',
          borderRight: 1,
          borderColor: 'divider'
        }}
      >
        <Tabs
          orientation="vertical"
          value={value}
          onChange={handleChange}
          className="custom-tabs"
          TabIndicatorProps={{ style: { display: 'none' } }}
        >
          {menuItems.map((item: any, index: number) => {
            const key = `${index}-${index * 3}-item-key`;
            return (
              <Tab
                key={key}
                className={`custom-tab ${value === index ? 'custom-tab-selected' : ''}`}
                label={
                  <Box
                    className="custom-tab-label d-flex justify-content-between w-full"
                    sx={{
                      padding: '0px'
                    }}
                  >
                    <Box className="d-flex align-items-center">
                      {item.icon && <Icon name={item.icon} />}
                      <Typography
                        className="custom-tab-text"
                        sx={{
                          fontSize: '14px',
                          color: '#62656C',
                          ml: item.icon ? 1 : 0,
                          width: 200,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {item.name}
                      </Typography>
                    </Box>
                    <ChevronRight sx={{ color: 'inherit' }} />
                  </Box>
                }
              />
            );
          })}
        </Tabs>
      </Box>
      {children && <Box sx={{ flex: 1, overflowY: 'auto' }}>{children}</Box>}
    </Box>
  );
};
export default Sidebar;
