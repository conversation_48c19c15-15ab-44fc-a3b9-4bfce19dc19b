import { Line, Rect, Svg } from '@react-pdf/renderer';

const Checkbox = ({ checked }: { checked: boolean }) => (
  <Svg
    width="12"
    height="12"
    style={{
      marginLeft: 8,
      backgroundColor: checked ? '#0078d4' : '#fff',
      border: '1px solid black'
    }}
  >
    {/* Draw the checkbox square */}
    <Rect
      x="0"
      y="0"
      width="14"
      height="14"
      stroke="white"
      fill="none"
      strokeWidth="1"
    />
    {/* Add a checkmark if checked */}
    {checked && (
      <Line x1="2" y1="6" x2="5" y2="9" stroke="white" strokeWidth="1" />
    )}
    {checked && (
      <Line x1="5" y1="9" x2="10" y2="3" stroke="white" strokeWidth="1" />
    )}
  </Svg>
);
export default Checkbox;
