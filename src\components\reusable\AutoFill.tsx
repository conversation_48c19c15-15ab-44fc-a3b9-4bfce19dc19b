/* eslint-disable no-param-reassign */
// Package Imports
import { useEffect, useState } from 'react';
import { Box, Button, Typography } from '@mui/material';
import { useDispatch } from 'react-redux';

// Local Imports
import { AppForm, FormSelect } from '../form.elements';
import '../../css/auto-fill-styles.scss';
import '../../css/index.scss';
import { AppDispatch } from '../../redux/app.store';
import {
  // formsrepository,
  // getform,
  getformswithappid,
  getformwithformid
} from '../../redux/reducers/form.reducer';

const SelectInputRound = ({
  changeEvent,
  render,
  name,
  value,
  groupedOptions
}: any) => {
  return (
    <div className="custom-selectstyles">
      <select
        id={name}
        className="inputSelectRound-styles form-control override-fc"
        name={name}
        value={value}
        onChange={changeEvent}
      >
        {Object.keys(groupedOptions).map((group, index) => {
          const keyIndex = `key-card${index}-${index * 3}`;
          return (
            <optgroup key={keyIndex} label={group}>
              {render(groupedOptions[group])}
            </optgroup>
          );
        })}
      </select>
    </div>
  );
};

const AutoFill = ({ appId, handleAutoFillSaveButton, onClose, field }: any) => {
  const dispatch = useDispatch<AppDispatch>();
  const [formsList, setFormsList] = useState([]);
  const [formsListOptions, setFormsListOptions] = useState([]);
  const [selectedForm, setSelectedForm] = useState('');
  const [selectedFormData, setSelectedFormData] = useState<any>({});
  const [selectedFormOptions, setSelectedFormOptions] = useState([]);
  const [selectedField, setSelectedField] = useState('');
  const [firstField, setFirstField] = useState();

  const getFormWithId = async (formId: string) => {
    const res = await dispatch(getformwithformid(formId));
    setSelectedFormData(res.payload.data);
    const fieldsArray = res.payload.data.groups;
    // const formData = res.payload.data.forms;
    const groupedOptions = fieldsArray.reduce((acc: any, section: any) => {
      const sectionName = section.group_title;
      const options = section.fields.map((fd: any) => ({
        value: fd.field_id,
        label: fd.label
      }));
      acc[sectionName] = options;
      return acc;
    }, {});
    setSelectedFormOptions(groupedOptions);

    // const groupedOptions = {
    //   'Section Name 1': [
    //     { value: 'fullname', label: 'Full Name' },
    //     { value: 'email', label: 'Email' },
    //     { value: 'address', label: 'Address' }
    //   ],
    //   'Section Name 2': [
    //     { value: 'mobileno', label: 'Mobile No' },
    //     { value: 'alternativemobileno', label: 'Alternative Mobile No' }
    //   ]
    // };
  };

  const handleFormChange = (event: any) => {
    const { value } = event.target;
    if (value) {
      getFormWithId(value);
    }
    setSelectedForm(value);
  };

  const handleFieldChange = (event: any) => {
    setSelectedField(event.target.value);
    const { name, value } = event.target;
    if (name === 'field-select') {
      setFirstField(value);
      window.console.log(selectedForm, selectedField);
    }
  };

  const getForms = async () => {
    const res = await dispatch(getformswithappid(appId));
    const formsData = res.payload.data.forms;
    setFormsList(formsData);
    const options = formsData?.map((form: any) => ({
      value: form.form_id,
      label: form.name
    }));
    setFormsListOptions(options);
  };

  // const formOptions = [
  //   { value: 'profileinformation', label: 'Profile Information' },
  //   { value: 'contactinformation', label: 'Contact Information' },
  //   { value: 'aggrements', label: 'Aggrements' }
  // ];

  // const groupedOptions = {
  //   'Section Name 1': [
  //     { value: 'fullname', label: 'Full Name' },
  //     { value: 'email', label: 'Email' },
  //     { value: 'address', label: 'Address' }
  //   ],
  //   'Section Name 2': [
  //     { value: 'mobileno', label: 'Mobile No' },
  //     { value: 'alternativemobileno', label: 'Alternative Mobile No' }
  //   ]
  // };

  useEffect(() => {
    getForms();
    if (field) {
      setSelectedForm(field?.auto_fill?.source_form_id);
      getFormWithId(field?.auto_fill?.source_form_id);
      setFirstField(field?.auto_fill?.source_field_id);
    }
  }, []);

  const initialValues = {
    'form-select': field ? field?.auto_fill?.source_form_id : '',
    'field-select': field ? field?.auto_fill?.source_field_id : ''
  };
  const handleSave = async (autoFillValues: any) => {
    // const { form_select, first_field } = autoFillValues;
    console.log('AutoFill Values: ', autoFillValues);
    const selectedFormDetails: any = formsList?.find(
      (form: any) => form.form_id === selectedForm
    );

    let selectedFieldData: any = {};

    if (selectedFormData) {
      selectedFormData?.groups?.map((section: any) => {
        const sectionName = section.group_title;
        const sectionKey = section.group_key;
        const fieldsArray = section.fields?.find(
          (fieldData: any) => fieldData.field_id === selectedField
        );
        if (fieldsArray !== undefined) selectedFieldData = fieldsArray;

        return (
          fieldsArray && {
            sectionName,
            sectionKey,
            fieldName: fieldsArray.name,
            fieldId: fieldsArray.field_id
          }
        );
      });
    }

    const data = {
      formId: selectedFormDetails.form_id || '',
      formName: selectedFormDetails.name || '',
      sectionName: selectedFieldData.group_title || '',
      sectionKey: selectedFieldData.group_key || '',
      fieldName: selectedFieldData.label || '',
      fieldId: selectedFieldData.field_id || ''
    };

    handleAutoFillSaveButton(data);
  };

  const renderOptions = (fields: any) => {
    return fields.map((fd: any) => (
      <option key={fd.value} value={fd.value}>
        {fd.label}
      </option>
    ));
  };

  return (
    <Box sx={{ marginTop: '10px', maxWidth: '900px' }}>
      <Typography
        sx={{
          fontSize: '18px',
          fontWeight: '600',
          color: '#0483BA',
          marginBottom: '16px',
          textAlign: 'center'
        }}
      >
        Auto Fill/Pre Fill
      </Typography>
      <AppForm initialValues={initialValues} onSubmit={handleSave}>
        <Box
          sx={{
            marginTop: '30px',
            border: '1px solid #0483BA',
            borderRadius: '10px',
            backgroundColor: '#FAF9F8',
            width: '450px',
            padding: '20px'
          }}
        >
          {/* Container for Select Form */}
          <Box className="w-full mr-2">
            <Typography
              variant="subtitle1"
              color="textSecondary"
              className="font-weight-400 font-size-15 color-808080 pb-8"
            >
              Select Form
            </Typography>
            <FormSelect
              name="form-select"
              data={formsListOptions}
              placeholder="Select Form"
              // data={formOptions}
              onChange={handleFormChange}
              containerStyles={{
                width: '100%',
                height: '45px',
                marginBottom: '16px',
                backgroundColor: '#FFFFFF'
              }}
            />
          </Box>

          {/* Container for Select Field */}
          <Box className="custom-select">
            <Typography
              variant="subtitle2"
              color="textSecondary"
              className="font-weight-400 font-size-15 color-808080 pb-8"
            >
              Select Field
            </Typography>
            <SelectInputRound
              changeEvent={handleFieldChange}
              render={renderOptions}
              name="field-select"
              // name="first_field"
              value={firstField}
              groupedOptions={selectedFormOptions}
              // groupedOptions={groupedOptions}
            />
          </Box>

          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              gap: 2,
              marginTop: '20px'
            }}
          >
            <Button
              onClick={onClose} // Close modal on cancel
              sx={{
                backgroundColor: 'white2.main',
                color: 'primaryBlue.main',
                padding: '10px 30px',
                boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                textTransform: 'capitalize'
              }}
            >
              CANCEL
            </Button>
            <Button
              type="submit"
              sx={{
                backgroundColor: 'primaryBlue.main',
                color: 'white2.main',
                padding: '10px 35px',
                boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                '&:hover': {
                  color: 'white2.main',
                  backgroundColor: '#08366b',
                  boxShadow: '0px 8px 10px 4px rgba(0,0,0,0.2)'
                }
              }}
            >
              SAVE
            </Button>
          </Box>
        </Box>
      </AppForm>
    </Box>
  );
};
export default AutoFill;
