/* eslint-disable no-param-reassign */
import React, { useEffect, useState } from 'react';
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  Radio,
  RadioGroup,
  Typography
} from '@mui/material';
import { Field, useField, useFormikContext } from 'formik';
import { useDispatch } from 'react-redux';

import { AppForm, FormInput, FormSelect } from '../form.elements';
import { ValidationInputTypes } from '../../types';
import { AppDispatch } from '../../redux/app.store';
import { updatecustomvalidations } from '../../redux/reducers/form.reducer';

interface ValidationOption {
  value: string;
  label: string;
  type: string;
  style: string;
  name: string;
}

const validationOptions: ValidationOption[] = [
  {
    value: 'minimum',
    label: 'Min Length',
    type: 'number',
    style: 'checkbox',
    name: 'text.minLength'
  },
  {
    value: 'maximum',
    label: 'Max Length',
    type: 'number',
    style: 'checkbox',
    name: 'text.maxLength'
  },
  {
    value: 'decimals',
    label: 'Allow only Numeric',
    type: 'number',
    style: 'radio',
    name: 'text.inputFormat'
  },
  {
    value: 'alphabets',
    label: 'Allow only Alphabets',
    type: 'text',
    style: 'radio',
    name: 'text.inputFormat'
  },
  {
    value: 'alphanumeric',
    label: 'Allow only AlphaNumeric',
    type: 'text',
    style: 'radio',
    name: 'text.inputFormat'
  },
  {
    value: 'text',
    label: 'Allow only Text',
    type: 'text',
    style: 'radio',
    name: 'text.inputFormat'
  },
  {
    value: 'regx',
    label: 'Regular Expression',
    type: 'text',
    style: 'radio',
    name: 'text.inputFormat'
  }
];

const validationTypes = [
  { value: 'custom', label: 'Custom Validations' },
  { value: 'conditional', label: 'Conditional Validations' }
];

// const SelectInputRound = ({
//   changeEvent,
//   render,
//   name,
//   value,
//   groupedOptions
// }: any) => {
//   return (
//     <div className="custom-selectstyles">
//       <select
//         id={name}
//         className="inputSelectRound-styles form-control override-fc"
//         name={name}
//         value={value}
//         onChange={changeEvent}
//       >
//         {Object.keys(groupedOptions).map((group, index) => {
//           const keyIndex = `key-card${index}-${index * 3}`;
//           return (
//             <optgroup key={keyIndex} label={group}>
//               {render(groupedOptions[group])}
//             </optgroup>
//           );
//         })}
//       </select>
//     </div>
//   );
// };

const SelectInputRound = ({
  name,
  render,
  groupedOptions,
  onCustomChange
}: any) => {
  const [field] = useField(name); // includes value, name, and onChange
  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    field.onChange(event); // updates Formik
    if (onCustomChange) {
      onCustomChange(event); // updates your conditionalValidationValue
    }
  };
  return (
    <div className="custom-selectstyles">
      <select
        id={name}
        className="inputSelectRound-styles form-control override-fc"
        {...field} // this gives you name, value, and onChange from Formik
        onChange={handleChange}
      >
        {Object.keys(groupedOptions).map((group, index) => {
          const keyIndex = `key-card${index}-${index * 3}`;
          return (
            <optgroup key={keyIndex} label={group}>
              {render(groupedOptions[group])}
            </optgroup>
          );
        })}
      </select>
    </div>
  );
};
const ValidationInput: React.FC<ValidationInputTypes> = ({
  type,
  value,
  onChange,
  onBlur,
  error
}) => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      width: '50%',
      marginLeft: '10px'
    }}
  >
    <FormControl fullWidth>
      <FormInput
        name="validationInputField"
        label=""
        type={type}
        placeholder="Enter Value..."
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        containerStyles={{
          width: '100%',
          margin: '0px',
          height: '45px'
        }}
        sx={{
          '& .MuiInputBase-formControl': {
            margin: '0px',
            height: '45px'
          }
        }}
      />
    </FormControl>
    <FormHelperText sx={{ color: 'error.main' }}>{error}</FormHelperText>
  </Box>
);

interface ValidationControlProps {
  optionValue: string;
  checked: boolean;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  style: string; // 'checkbox' or 'radio'
}

const ValidationControl: React.FC<ValidationControlProps> = ({
  optionValue,
  checked,
  onChange,
  style
}) => {
  if (style === 'checkbox') {
    return (
      <Checkbox value={optionValue} checked={checked} onChange={onChange} />
    );
  }

  if (style === 'radio') {
    return <Radio value={optionValue} checked={checked} onChange={onChange} />;
  }

  return null;
};

interface CustomValidationsProps {
  lastUpdatedField: any;
  formId: string;
  activeField: any;
  onClose: () => void;
}

// const allowedInputs = [
//   'date',
//   'time',
//   'datetime-local',
//   'select',
//   'checkbox',
//   'radio',
//   'toggle'
// ];
// const allowedInputsForCustomValidations = [
//   'date',
//   'time',
//   'datetime-local',
//   'phone',
//   'fax',
//   'text',
//   'file',
//   'number'
// ];

export const CustomValidations: React.FC<CustomValidationsProps> = ({
  lastUpdatedField,
  formId,
  activeField,
  onClose
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { values } = useFormikContext<any>();
  const [validationType, setValidationType] = useState<string>('custom');
  const [selectedValidations, setSelectedValidations] = useState<string[]>([]);
  const [inputValues, setInputValues] = useState<{ [key: string]: string }>({});
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [groupsOptions, setGroupsOptions] = useState<any>();
  const [initialValues, setinitialValues] = useState<any>({});
  const [customInitialValues, setCustomInitialValues] = useState<any>({});

  // console.log('Active Field: ', activeField);
  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValidationType(event.target.value);
  };

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value, checked } = event.target;
    const selectedOption = validationOptions.find(
      (option) => option.value === value
    );

    setSelectedValidations((prevSelected) => {
      if (selectedOption?.style === 'checkbox') {
        // If it's a checkbox, add or remove from the selected validations
        return checked
          ? [...prevSelected, value]
          : prevSelected.filter((val) => val !== value);
      }
      console.log('Chek Test: ', [...prevSelected, value]);
      if (selectedOption?.style === 'radio') {
        // If it's a radio button, remove all other radio options and set the new one
        return [
          ...prevSelected.filter(
            (val) =>
              !validationOptions
                .filter((option) => option.style === 'radio')
                .map((option) => option.value)
                .includes(val)
          ),
          value
        ];
      }
      return prevSelected;
    });
  };
  window.console.log(selectedValidations);
  window.console.log(inputValues);

  const handleValidation = (validationKey: string) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      [validationKey]:
        inputValues[validationKey]?.trim() === ''
          ? 'This field is required.'
          : ''
    }));
  };

  const handleInputChange = (validationKey: string, value: string) => {
    setInputValues((prevInputValues) => ({
      ...prevInputValues,
      [validationKey]: value
    }));
  };

  // Only show input for minimum, maximum, and regx
  const showInputFields = ['minimum', 'maximum', 'regx'];

  const [conditionalValidationValue, setConditionalValidationValue] =
    useState('');

  const handleConditionalChange = (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setConditionalValidationValue(event.target.value);
  };

  window.console.log(values);
  // console.log(props?.lastUpdatedField);
  window.console.log(
    'Current Conditional Validation Value:',
    conditionalValidationValue
  );

  const currentField = values.groups.map((group: any) =>
    group.fields.find(
      (field: any) => field.field_id === conditionalValidationValue
    )
  )[0];
  const conditionalFields = () => {
    // return values.groups.map((group: any) =>
    //   group.fields.filter((field: any) =>
    //     allowedInputs.includes(field.input_type ? field.input_type : field.type)
    //   )
    // )[0];
    return values.groups.map((group: any) => group.fields);
  };
  window.console.log('Groups:', values.groups, currentField, conditionalFields);

  // const initialValues = {
  //   conditionalField: '',
  //   date: {
  //     dateComparision: '',
  //     selectedDate: '',
  //     selectedStartDate: '',
  //     selectedEndDate: ''
  //   },
  //   time: {
  //     timeComparision: '',
  //     selectedTime: '',
  //     selectedStartTime: '',
  //     selectedEndTime: ''
  //   },
  //   datetime: {
  //     datetimeComparision: '',
  //     selectedDatetime: '',
  //     selectedStartDateTime: '',
  //     selectedEndDateTime: ''
  //   },
  //   select: {
  //     selectedOption: ''
  //   },
  //   checkbox: {
  //     selectedOptions: []
  //   },
  //   radio: {
  //     selectedOption: ''
  //   },
  //   toggle: {
  //     selectedOption: ''
  //   },
  //   // isConditionsRequired: true,
  //   // isDisplayed: false
  //   conditions: []
  // };

  const handleConditionalInputSaveButton = async (cndvalues: any) => {
    const field = values.groups
      .map((group: any) =>
        group.fields.find(
          (fieldValue: any) =>
            fieldValue.field_id === cndvalues.conditionalField
        )
      )
      .filter(Boolean)[0];

    const getConditionalValidations = () => {
      let data: any = {};
      if (field.input_type === 'date') {
        if (cndvalues.date.dateComparision === 'inbetween') {
          data = {
            selectedFormId: formId,
            selectedGroupKey: field?.group_key,
            selectedInputId: cndvalues.conditionalField,
            dateComparison: cndvalues.date.dateComparision,
            selectedStartDate: cndvalues.date.selectedStartDate,
            selectedEndDate: cndvalues.date.selectedEndDate,
            conditions: cndvalues.conditions
          };
        } else {
          data = {
            selectedFormId: formId,
            selectedGroupKey: field?.group_key,
            selectedInputId: cndvalues.conditionalField,
            dateComparison: cndvalues.date.dateComparision,
            selectedDate: cndvalues.date.selectedDate,
            conditions: cndvalues.conditions
          };
        }
      } else if (field.input_type === 'time') {
        if (cndvalues.time.timeComparision === 'inbetween') {
          data = {
            selectedFormId: formId,
            selectedGroupKey: field?.group_key,
            selectedInputId: cndvalues.conditionalField,
            // timeComparison: cndvalues.time.timeComparision,
            // selectedStartTime: cndvalues.time.selectedStartTime,
            // selectedEndTime: cndvalues.time.selectedEndTime,
            dateComparison: cndvalues.time.timeComparision,
            selectedStartDate: cndvalues.time.selectedStartTime,
            selectedEndDate: cndvalues.time.selectedEndTime,
            conditions: cndvalues.conditions
          };
        } else {
          data = {
            selectedFormId: formId,
            selectedGroupKey: field?.group_key,
            selectedInputId: cndvalues.conditionalField,
            // timeComparison: cndvalues.time.timeComparision,
            // selectedTime: cndvalues.time.selectedTime,
            dateComparison: cndvalues.time.timeComparision,
            selectedDate: cndvalues.time.selectedTime,
            conditions: cndvalues.conditions
          };
        }
      } else if (field.input_type === 'datetime-local') {
        if (cndvalues.datetime.datetimeComparision === 'inbetween') {
          data = {
            selectedFormId: formId,
            selectedGroupKey: field?.group_key,
            selectedInputId: cndvalues.conditionalField,
            // datetimeComparison: cndvalues.datetime.datetimeComparision,
            // selectedStartDateTime: cndvalues.datetime.selectedStartDateTime,
            // selectedEndDateTime: cndvalues.datetime.selectedEndDateTime,
            dateComparison: cndvalues.datetime.datetimeComparision,
            selectedStartDate: cndvalues.datetime.selectedStartDateTime,
            selectedEndDate: cndvalues.datetime.selectedEndDateTime,
            conditions: cndvalues.conditions
          };
        } else {
          data = {
            selectedFormId: formId,
            selectedGroupKey: field?.group_key,
            selectedInputId: cndvalues.conditionalField,
            // datetimeComparison: cndvalues.datetime.datetimeComparision,
            // selectedDatetime: cndvalues.datetime.selectedDatetime,
            dateComparison: cndvalues.datetime.datetimeComparision,
            selectedDate: cndvalues.datetime.selectedDatetime,
            conditions: cndvalues.conditions
          };
        }
      } else if (field.type === 'select') {
        data = {
          selectedFormId: formId,
          selectedGroupKey: field?.group_key,
          selectedInputId: cndvalues.conditionalField,
          selectedOption: cndvalues.select.selectedOption,
          conditions: cndvalues.conditions
        };
      } else if (field.input_type === 'checkbox') {
        data = {
          selectedFormId: formId,
          selectedGroupKey: field?.group_key,
          selectedInputId: cndvalues.conditionalField,
          selectedOption: cndvalues.checkbox.selectedOptions,
          conditions: cndvalues.conditions
        };
      } else if (field.input_type === 'radio') {
        data = {
          selectedFormId: formId,
          selectedGroupKey: field?.group_key,
          selectedInputId: cndvalues.conditionalField,
          selectedOption: cndvalues.radio.selectedOption,
          conditions: cndvalues.conditions
        };
      } else if (field.input_type === 'toggle') {
        data = {
          selectedFormId: formId,
          selectedGroupKey: field?.group_key,
          selectedInputId: cndvalues.conditionalField,
          selectedOption: cndvalues.toggle.selectedOption,
          conditions: cndvalues.conditions
        };
      }
      return data || null;
    };

    const form = {
      field_id: activeField.field_id,
      validation_schema: {
        required: activeField.validation_schema.required,
        conditional_validation: getConditionalValidations()
      },
      group_key: activeField.group_key
    };

    const response = await dispatch(
      updatecustomvalidations({ id: formId, data: form })
    );
    if (response.meta.requestStatus === 'fulfilled') {
      onClose();
    }
  };

  // const customInitialValues = {
  //   phone: {
  //     country: ''
  //   },
  //   fax: {
  //     country: ''
  //   },
  //   text: {
  //     minLength: '',
  //     maxLength: '',
  //     inputFormat: ''
  //   },
  //   file: {
  //     maxFiles: '',
  //     maxFileSize: '',
  //     fileFormat: ''
  //   },
  //   number: {
  //     minValue: '',
  //     maxValue: ''
  //   },
  //   date: {
  //     startDate: '',
  //     endDate: ''
  //   },
  //   time: {
  //     startTime: '',
  //     endTime: ''
  //   },
  //   datetime: {
  //     startDateTime: '',
  //     endDateTime: ''
  //   }
  // };

  const handleCustomInputSaveButton = async (cstValues: any) => {
    const filtered = selectedValidations.filter(
      (val) => val !== 'minimum' && val !== 'maximum'
    );

    const getCustomValidations = () => {
      let data: any = {};
      if (lastUpdatedField?.input_type === 'phone') {
        data = {
          country: cstValues.phone.country
        };
      } else if (lastUpdatedField?.input_type === 'fax') {
        data = {
          country: cstValues.fax.country
        };
      } else if (lastUpdatedField?.input_type === 'text') {
        data = {
          minLength: inputValues?.minimum || '',
          maxLength: inputValues?.maximum || '',
          inputFormat: selectedValidations?.includes('regx')
            ? inputValues?.regx
            : filtered[0]
        };
      } else if (lastUpdatedField?.input_type === 'file') {
        data = {
          maxFiles: cstValues.file.maxFiles,
          maxFileSize: cstValues.file.maxFileSize,
          fileFormat: cstValues.file.fileFormat
        };
      } else if (lastUpdatedField?.input_type === 'number') {
        data = {
          minValue: cstValues.number.minValue,
          maxValue: cstValues.number.maxValue
        };
      } else if (lastUpdatedField?.input_type === 'date') {
        data = {
          startDate: cstValues.date.startDate,
          endDate: cstValues.date.endDate
        };
      } else if (lastUpdatedField?.input_type === 'time') {
        data = {
          startTime: cstValues.time.startTime,
          endTime: cstValues.time.endTime
        };
      } else if (lastUpdatedField?.input_type === 'datetime-local') {
        data = {
          startDateTime: cstValues.datetime.startDateTime,
          endDateTime: cstValues.datetime.endDateTime
        };
      }
      return data || null;
    };

    const form = {
      field_id: activeField.field_id,
      // field_id: cndvalues.conditionalField,
      validation_schema: {
        required: activeField.validation_schema.required,
        custom_validation: getCustomValidations()
        // conditional_validation: {}
      },
      // group_key: values.groups.map((group: any) =>
      //   group.fields.find(
      //     (fieldValue: any) => fieldValue.field_id === activeField.field_id
      //   )
      // )[0]?.group_key
      group_key: activeField.group_key
    };

    const response = await dispatch(
      updatecustomvalidations({ id: formId, data: form })
    );

    if (response.meta.requestStatus === 'fulfilled') {
      onClose();
    }
    console.log('Form: ', formId, form);
  };

  const renderOptions = (fields: any) => {
    return fields.map((field: any) => (
      <option key={field.value} value={field.value}>
        {field.label}
      </option>
    ));
  };

  useEffect(() => {
    const groupedOptions = values.groups.reduce((acc: any, section: any) => {
      const sectionName = section.group_title;
      const options = section.fields.map((field: any) => ({
        value: field.field_id,
        label: field.label
      }));
      acc[sectionName] = options;
      return acc;
    }, {});
    // console.log('Grouped Options: ', groupedOptions);
    setGroupsOptions(groupedOptions);
  }, []);

  useEffect(() => {
    const fid = values?.groups
      ?.flatMap((group: any) => group.fields || [])
      .find(
        (fd: any) =>
          fd?.field_id ===
          activeField?.validation_schema?.conditional_validation
            ?.selectedInputId
      );
    const initData = {
      conditionalField:
        activeField?.validation_schema?.conditional_validation
          ?.selectedInputId || '',
      date: {
        dateComparision:
          fid?.input_type === 'date'
            ? activeField?.validation_schema?.conditional_validation
                ?.dateComparison
            : '',
        selectedDate:
          fid?.input_type === 'date'
            ? activeField?.validation_schema?.conditional_validation
                ?.selectedDate
            : '',
        selectedStartDate:
          fid?.input_type === 'date'
            ? activeField?.validation_schema?.conditional_validation
                ?.selectedStartDate
            : '',
        selectedEndDate:
          fid?.input_type === 'date'
            ? activeField?.validation_schema?.conditional_validation
                ?.selectedEndDate
            : ''
      },
      time: {
        timeComparision:
          fid?.input_type === 'time'
            ? activeField?.validation_schema?.conditional_validation
                ?.dateComparison
            : '',
        selectedTime:
          fid?.input_type === 'time'
            ? activeField?.validation_schema?.conditional_validation
                ?.selectedDate
            : '',
        selectedStartTime:
          fid?.input_type === 'time'
            ? activeField?.validation_schema?.conditional_validation
                ?.selectedStartDate
            : '',
        selectedEndTime:
          fid?.input_type === 'time'
            ? activeField?.validation_schema?.conditional_validation
                ?.selectedEndDate
            : ''
      },
      datetime: {
        datetimeComparision:
          fid?.input_type === 'datetime-local'
            ? activeField?.validation_schema?.conditional_validation
                ?.dateComparison
            : '',
        selectedDatetime:
          fid?.input_type === 'datetime-local'
            ? activeField?.validation_schema?.conditional_validation
                ?.selectedDate
            : '',
        selectedStartDateTime:
          fid?.input_type === 'datetime-local'
            ? activeField?.validation_schema?.conditional_validation
                ?.selectedStartDate
            : '',
        selectedEndDateTime:
          fid?.input_type === 'datetime-local'
            ? activeField?.validation_schema?.conditional_validation
                ?.selectedEndDate
            : ''
      },
      select: {
        selectedOption:
          fid?.type === 'select'
            ? activeField?.validation_schema?.conditional_validation
                ?.selectedOption
            : ''
      },
      checkbox: {
        selectedOptions:
          fid?.input_type === 'checkbox'
            ? activeField?.validation_schema?.conditional_validation
                ?.selectedOption
            : []
      },
      radio: {
        selectedOption:
          fid?.input_type === 'radio'
            ? activeField?.validation_schema?.conditional_validation
                ?.selectedOption
            : ''
      },
      toggle: {
        selectedOption:
          fid?.input_type === 'toggle'
            ? activeField?.validation_schema?.conditional_validation
                ?.selectedOption
            : ''
      },
      conditions:
        activeField?.validation_schema?.conditional_validation?.conditions || []
    };
    console.log('Init Data: ', initData, fid, activeField);
    setinitialValues(initData);
    setConditionalValidationValue(
      activeField?.validation_schema?.conditional_validation?.selectedInputId
    );

    const customInitValues = {
      phone: {
        country:
          activeField?.validation_schema?.custom_validation?.country || ''
      },
      fax: {
        country:
          activeField?.validation_schema?.custom_validation?.country || ''
      },
      text: {
        minLength:
          activeField?.validation_schema?.custom_validation?.minLength || '',
        maxLength:
          activeField?.validation_schema?.custom_validation?.maxLength || '',
        inputFormat:
          activeField?.validation_schema?.custom_validation?.inputFormat || ''
      },
      file: {
        maxFiles:
          activeField?.validation_schema?.custom_validation?.maxFiles || '',
        maxFileSize:
          activeField?.validation_schema?.custom_validation?.maxFileSize || '',
        fileFormat:
          activeField?.validation_schema?.custom_validation?.fileFormat || ''
      },
      number: {
        minValue:
          activeField?.validation_schema?.custom_validation?.minValue || '',
        maxValue:
          activeField?.validation_schema?.custom_validation?.maxValue || ''
      },
      date: {
        startDate:
          activeField?.validation_schema?.custom_validation?.startDate || '',
        endDate:
          activeField?.validation_schema?.custom_validation?.endDate || ''
      },
      time: {
        startTime:
          activeField?.validation_schema?.custom_validation?.startTime || '',
        endTime:
          activeField?.validation_schema?.custom_validation?.endTime || ''
      },
      datetime: {
        startDateTime:
          activeField?.validation_schema?.custom_validation?.startDateTime ||
          '',
        endDateTime:
          activeField?.validation_schema?.custom_validation?.endDateTime || ''
      }
    };

    if (activeField?.input_type === 'text') {
      validationOptions?.forEach((option) => {
        console.log('Validation Option for Test: ', option);
        if (option.value === 'minimum') {
          const value =
            activeField?.validation_schema?.custom_validation?.minLength || '';
          const checked = !!value;
          console.log('Value: ', value, checked);

          setSelectedValidations((prevSelected) => {
            if (option?.style === 'checkbox') {
              return checked
                ? [...prevSelected, 'minimum']
                : prevSelected.filter((val) => val !== value);
            }
            console.log('prev Selected: ', [...prevSelected, 'minimum']);
            if (option?.style === 'radio') {
              // If it's a radio button, remove all other radio options and set the new one
              return [
                ...prevSelected.filter(
                  (val) =>
                    !validationOptions
                      .filter((opt) => opt.style === 'radio')
                      .map((opt) => opt.value)
                      .includes(val)
                ),
                value
              ];
            }
            return prevSelected;
          });
          setInputValues((prevInputValues) => ({
            ...prevInputValues,
            minimum: value
          }));
          customInitValues.text.minLength = value;
        }
        if (option.value === 'maximum') {
          const value =
            activeField?.validation_schema?.custom_validation?.maxLength || '';
          const checked = !!value;

          setSelectedValidations((prevSelected) => {
            if (option?.style === 'checkbox') {
              return checked
                ? [...prevSelected, 'maximum']
                : prevSelected.filter((val) => val !== value);
            }
            if (option?.style === 'radio') {
              // If it's a radio button, remove all other radio options and set the new one
              return [
                ...prevSelected.filter(
                  (val) =>
                    !validationOptions
                      .filter((opt) => opt.style === 'radio')
                      .map((opt) => opt.value)
                      .includes(val)
                ),
                value
              ];
            }
            return prevSelected;
          });
          setInputValues((prevInputValues) => ({
            ...prevInputValues,
            maximum: value
          }));
          customInitValues.text.maxLength = value;
        }
        if (option.value === 'inputFormat') {
          const value =
            activeField?.validation_schema?.custom_validation?.inputFormat ||
            '';
          const checked = !!value;

          setSelectedValidations((prevSelected) => {
            if (option?.style === 'checkbox') {
              return checked
                ? [...prevSelected, 'maximum']
                : prevSelected.filter((val) => val !== value);
            }
            if (option?.style === 'radio') {
              // If it's a radio button, remove all other radio options and set the new one
              return [
                ...prevSelected.filter(
                  (val) =>
                    !validationOptions
                      .filter((opt) => opt.style === 'radio')
                      .map((opt) => opt.value)
                      .includes(val)
                ),
                value
              ];
            }
            return prevSelected;
          });
        }
      });
      // const selectedOption = validationOptions?.find(
      //   (option) => option.value === value
      // );
      // setSelectedValidations((prevSelected) => {
      //   if (selectedOption?.style === 'checkbox') {
      //     // If it's a checkbox, add or remove from the selected validations
      //     return checked
      //       ? [...prevSelected, value]
      //       : prevSelected.filter((val) => val !== value);
      //   }
      //   if (selectedOption?.style === 'radio') {
      //     // If it's a radio button, remove all other radio options and set the new one
      //     return [
      //       ...prevSelected.filter(
      //         (val) =>
      //           !validationOptions
      //             .filter((option) => option.style === 'radio')
      //             .map((option) => option.value)
      //             .includes(val)
      //       ),
      //       value
      //     ];
      //   }
      //   return prevSelected;
      // });
    }

    setCustomInitialValues(customInitValues);
  }, []);
  console.log('groupsOptions: ', groupsOptions);
  return (
    <Box sx={{ marginTop: '10px', maxWidth: '900px' }}>
      <Typography
        sx={{
          fontSize: '18px',
          fontWeight: '600',
          color: '#0483BA',
          marginBottom: '16px',
          textAlign: 'center'
        }}
      >
        {' '}
        Validations{' '}
      </Typography>
      <Box
        sx={{
          width: '100%',
          marginTop: '5px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <FormControl component="fieldset">
          <RadioGroup
            name="validations"
            value={validationType}
            onChange={handleRadioChange}
            row
          >
            {validationTypes.map(({ value, label }) => (
              <FormControlLabel
                key={value}
                value={value}
                control={<Radio />}
                label={label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {validationType === 'custom' && (
        <AppForm
          initialValues={customInitialValues}
          onSubmit={handleCustomInputSaveButton}
        >
          <Box
            sx={{
              marginTop: '30px',
              border: '1px solid #0483BA',
              borderRadius: '10px',
              backgroundColor: '#FAF9F8',
              width: '450px',
              padding: '20px'
            }}
          >
            {lastUpdatedField?.input_type === 'text' && (
              <>
                <FormGroup>
                  {validationOptions.map((option) => (
                    <Box
                      key={option.value}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        marginBottom: '10px'
                      }}
                    >
                      <FormControlLabel
                        control={
                          <ValidationControl
                            optionValue={option.value}
                            checked={selectedValidations.includes(option.value)}
                            onChange={handleCheckboxChange}
                            style={option.style} // Pass the style here
                          />
                        }
                        label={option.label}
                        name={option.name}
                      />
                      {selectedValidations.includes(option.value) &&
                        showInputFields.includes(option.value) && (
                          <ValidationInput
                            key={option.value}
                            type={option.type}
                            value={inputValues[option.value] || ''}
                            onChange={(e) =>
                              handleInputChange(option.value, e.target.value)
                            }
                            onBlur={() => handleValidation(option.value)}
                            error={errors[option.value] || ''}
                          />
                        )}
                    </Box>
                  ))}
                </FormGroup>
                {/* <Field name="text">
                  {({ field: compField, form }: any) => {
                    const selectedValues = compField.value || [];
                    return (
                      <FormGroup row>
                        {validationOptions?.map((option: any) => {
                          const checked = selectedValidations.includes(
                            option.value
                          );

                          return (
                            <FormControlLabel
                              key={option.value}
                              control={
                                option.style === 'checkbox' ? (
                                  <Checkbox
                                    checked={checked}
                                    onChange={() => {
                                      const newValue = checked
                                        ? selectedValues.filter(
                                            (val: string) =>
                                              val !== option.value
                                          )
                                        : [...selectedValues, option.value];

                                      form.setFieldValue(
                                        'checkbox.selectedOptions',
                                        // field.name,
                                        newValue
                                      );
                                    }}
                                  />
                                ) : (
                                  <Checkbox
                                    checked={checked}
                                    onChange={() => {
                                      const newValue = checked
                                        ? selectedValues.filter(
                                            (val: string) =>
                                              val !== option.value
                                          )
                                        : [...selectedValues, option.value];

                                      form.setFieldValue(
                                        'checkbox.selectedOptions',
                                        // field.name,
                                        newValue
                                      );
                                    }}
                                  />
                                )
                              }
                              label={option.label}
                            />
                          );
                        })}
                      </FormGroup>
                    );
                  }}
                </Field> */}
              </>
            )}
            {/* {lastUpdatedField?.input_type === 'text' && (
              <Box sx={{ marginBottom: '20px' }}>
                <FormGroup>
                  {validationOptions.map((option) => {
                    return (
                      <Field key={option.value} name={option.name}>
                        {({ field: custField, form }: any) => {
                          const selectedValues = custField.value || [];
                          const checked = selectedValues.includes(option.value);
                          // const shouldShowInput =
                          //   checked && showInputFields.includes(option.value);
                          const shouldShowInput =
                            (option.style === 'checkbox' &&
                              checked &&
                              showInputFields.includes(option.value)) ||
                            (option.style === 'radio' &&
                              checked &&
                              option.value === 'regx');
                          console.log(
                            'Cust Field: ',
                            custField,
                            selectedValues
                          );
                          return (
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                marginBottom: '10px'
                              }}
                            >
                              <FormControlLabel
                                control={
                                  <ValidationControl
                                    optionValue={option.value}
                                    checked={checked}
                                    // onChange={() => {
                                    //   const newValue = checked
                                    //     ? selectedValues.filter(
                                    //         (val: string) =>
                                    //           val !== option.value
                                    //       )
                                    //     : [...selectedValues, option.value];

                                    //   form.setFieldValue(
                                    //     custField.name,
                                    //     newValue
                                    //   );
                                    // }}
                                    onChange={() => {
                                      if (option.style === 'checkbox') {
                                        const newValue = checked
                                          ? selectedValues.filter(
                                              (val: string) =>
                                                val !== option.value
                                            )
                                          : [...selectedValues, option.value];

                                        form.setFieldValue(
                                          custField.name,
                                          newValue
                                        );
                                      }

                                      if (option.style === 'radio') {
                                        form.setFieldValue(
                                          custField.name,
                                          option.value
                                        );
                                      }
                                    }}
                                    style={option.style}
                                  />
                                }
                                label={option.label}
                                // name={option.name}
                              />

                              {shouldShowInput && (
                                // <ValidationInput
                                //   key={option.value}
                                //   type={option.type}
                                //   value={inputValues[option.value] || ''}
                                //   onChange={(e) =>
                                //     handleInputChange(
                                //       option.value,
                                //       e.target.value
                                //     )
                                //   }
                                //   onBlur={() => handleValidation(option.value)}
                                //   error={errors[option.value] || ''}
                                //   name={option.name}
                                // />
                                <ValidationInput
                                  key={option.value}
                                  type={option.type}
                                  value={form.values.text[option.value] || ''}
                                  onChange={(e) =>
                                    form.setFieldValue(
                                      `text.${option.value}`,
                                      e.target.value
                                    )
                                  }
                                  onBlur={() => handleValidation(option.value)}
                                  error={errors[option.value] || ''}
                                  name={`text.${option.value}`}
                                />
                              )}
                            </Box>
                          );
                        }}
                      </Field>
                    );
                  })}
                </FormGroup>
              </Box>
            )} */}

            {lastUpdatedField?.input_type === 'number' && (
              <div style={{ width: '100%' }}>
                {/* Min Value Field */}
                <div style={{ marginBottom: '16px', width: '100%' }}>
                  <FormInput
                    label="Min Value"
                    name="number.minValue"
                    type="number"
                    fullWidth
                    placeholder="Enter minimum value"
                    containerStyles={{
                      width: '100%',
                      backgroundColor: '#FFFFFF'
                    }}
                  />
                </div>

                {/* Max Value Field */}
                <div style={{ marginBottom: '16px', width: '100%' }}>
                  <FormInput
                    label="Max Value"
                    name="number.maxValue"
                    type="number"
                    fullWidth
                    placeholder="Enter maximum value"
                    containerStyles={{
                      width: '100%',
                      backgroundColor: '#FFFFFF'
                    }}
                  />
                </div>
              </div>
            )}
            {lastUpdatedField?.input_type === 'date' && (
              <div style={{ width: '100%' }}>
                <div style={{ marginBottom: '16px', width: '100%' }}>
                  <FormInput
                    label="Start Date"
                    name="date.startDate"
                    type="date"
                    fullWidth
                    // onChange={handleInputChange}
                    containerStyles={{
                      width: '100%',
                      backgroundColor: '#FFFFFF'
                    }}
                  />
                </div>
                <div style={{ width: '100%' }}>
                  <FormInput
                    label="End Date"
                    name="date.endDate"
                    type="date"
                    fullWidth
                    // onChange={handleInputChange}
                    containerStyles={{
                      width: '100%',
                      backgroundColor: '#FFFFFF'
                    }}
                  />
                </div>
              </div>
            )}
            {lastUpdatedField?.input_type === 'time' && (
              <div style={{ width: '100%' }}>
                <div style={{ marginBottom: '16px', width: '100%' }}>
                  <FormInput
                    label="Start Time"
                    name="time.startTime"
                    type="time"
                    fullWidth
                    // onChange={handleInputChange}
                    containerStyles={{
                      width: '100%',
                      backgroundColor: '#FFFFFF'
                    }}
                  />
                </div>
                <div style={{ width: '100%' }}>
                  <FormInput
                    label="End Time"
                    name="time.endTime"
                    type="time"
                    fullWidth
                    // onChange={handleInputChange}
                    containerStyles={{
                      width: '100%',
                      backgroundColor: '#FFFFFF'
                    }}
                  />
                </div>
              </div>
            )}
            {lastUpdatedField?.input_type === 'datetime-local' && (
              <div style={{ width: '100%' }}>
                {/* Start DateTime Field */}
                <div style={{ marginBottom: '16px', width: '100%' }}>
                  <FormInput
                    label="Start DateTime"
                    name="datetime.startDateTime"
                    type="datetime-local"
                    fullWidth
                    placeholder="Select start date and time"
                    containerStyles={{
                      width: '100%',
                      backgroundColor: '#FFFFFF'
                    }}
                  />
                </div>

                {/* End DateTime Field */}
                <div style={{ marginBottom: '16px', width: '100%' }}>
                  <FormInput
                    label="End DateTime"
                    name="datetime.endDateTime"
                    type="datetime-local"
                    fullWidth
                    placeholder="Select end date and time"
                    containerStyles={{
                      width: '100%',
                      backgroundColor: '#FFFFFF'
                    }}
                  />
                </div>
              </div>
            )}
            {lastUpdatedField?.input_type === 'file' && (
              <div style={{ width: '100%' }}>
                {/* Max No of Files Field */}
                <div style={{ marginBottom: '16px', width: '100%' }}>
                  <FormInput
                    label="Max No of Files"
                    name="file.maxFiles"
                    type="number"
                    fullWidth
                    containerStyles={{
                      width: '100%',
                      backgroundColor: '#FFFFFF'
                    }}
                  />
                </div>

                {/* Max File Size Field */}
                <div style={{ marginBottom: '16px', width: '100%' }}>
                  <FormInput
                    label="Max File Size (MB)"
                    name="file.maxFileSize"
                    type="number"
                    fullWidth
                    containerStyles={{
                      width: '100%',
                      backgroundColor: '#FFFFFF'
                    }}
                  />
                </div>

                {/* File Format Field */}
                <div style={{ width: '100%' }}>
                  <FormInput
                    label="File Format"
                    name="file.fileFormat"
                    type="text"
                    fullWidth
                    placeholder="e.g., jpg, png, pdf"
                    containerStyles={{
                      width: '100%',
                      backgroundColor: '#FFFFFF'
                    }}
                  />
                </div>
              </div>
            )}
            {lastUpdatedField?.input_type === 'phone' && (
              <div style={{ width: '100%' }}>
                {/* Select Country Field */}
                <div style={{ marginBottom: '16px', width: '100%' }}>
                  <FormSelect
                    label="Select Country"
                    name="phone.country"
                    // name="country"
                    fullWidth
                    data={[
                      { value: 'us', label: 'United States' },
                      { value: 'ca', label: 'Canada' },
                      { value: 'in', label: 'India' },
                      { value: 'gb', label: 'United Kingdom' },
                      { value: 'au', label: 'Australia' }
                      // Add more countries as needed
                    ]}
                    placeholder="Choose a country"
                    containerStyles={{
                      width: '100%',
                      backgroundColor: '#FFFFFF'
                    }}
                  />
                </div>
              </div>
            )}
            {lastUpdatedField?.input_type === 'fax' && (
              <div style={{ width: '100%' }}>
                {/* Select Country Field */}
                <div style={{ marginBottom: '16px', width: '100%' }}>
                  <FormSelect
                    label="Select Country"
                    name="fax.country"
                    // name="country"
                    fullWidth
                    data={[
                      { value: 'us', label: 'United States' },
                      { value: 'ca', label: 'Canada' },
                      { value: 'in', label: 'India' },
                      { value: 'gb', label: 'United Kingdom' },
                      { value: 'au', label: 'Australia' }
                      // Add more countries as needed
                    ]}
                    placeholder="Choose a country"
                    containerStyles={{
                      width: '100%',
                      backgroundColor: '#FFFFFF'
                    }}
                  />
                </div>
              </div>
            )}

            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                gap: 2,
                marginTop: '20px'
              }}
            >
              <Button
                onClick={onClose}
                sx={{
                  backgroundColor: 'white2.main',
                  color: 'primaryBlue.main',
                  padding: '10px 30px',
                  boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                  textTransform: 'capitalize'
                }}
              >
                CANCEL
              </Button>
              <Button
                type="submit"
                sx={{
                  backgroundColor: 'primaryBlue.main',
                  color: 'white2.main',
                  padding: '10px 35px',
                  boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                  '&:hover': {
                    color: 'white2.main',
                    backgroundColor: '#08366b',
                    boxShadow: '0px 8px 10px 4px rgba(0,0,0,0.2)'
                  }
                }}
              >
                SAVE
              </Button>
            </Box>
          </Box>
        </AppForm>
      )}
      {/* For conditional validation */}

      {validationType === 'conditional' && (
        <AppForm
          initialValues={initialValues}
          onSubmit={handleConditionalInputSaveButton}
        >
          <Box
            sx={{
              marginTop: '30px',
              border: '1px solid #0483BA',
              borderRadius: '10px',
              backgroundColor: '#FAF9F8',
              width: '450px',
              padding: '20px'
            }}
          >
            {/* Dropdown for conditional validation */}
            <Box
              sx={{
                marginBottom: '20px'
              }}
            >
              {/* <FormSelect
                name="conditionalField"
                // name="conditionalValidation"
                // data={values.groups.flatMap((group: any) =>
                //   group.fields.map(
                //     (field: any) =>
                //       allowedInputs.includes(
                //         field.input_type ? field.input_type : field.type
                //       ) && {
                //         value: field.field_id,
                //         label: field.label
                //       }
                //   )
                // )}
                data={conditionalFields()?.map((field: any) => {
                  return {
                    value: field.field_id,
                    label: field.label
                  };
                })}
                onChange={handleConditionalChange}
                label="Select Input to Compare*"
                labelStyles={{
                  color: '#3B5864',
                  fontSize: '18px',
                  fontWeight: 400
                }}
                placeholder="Select Conditional Validation"
                containerStyles={{
                  width: '100%',
                  backgroundColor: '#FFFFFF'
                }}
              /> */}
              {/* <SelectInputRound
                changeEvent={handleConditionalChange}
                render={renderOptions}
                name="conditionalField"
                value={conditionalValidationValue}
                groupedOptions={groupsOptions}
                // groupedOptions={groupedOptions}
              /> */}
              <SelectInputRound
                name="conditionalField"
                render={renderOptions}
                groupedOptions={groupsOptions}
                onCustomChange={handleConditionalChange}
              />
            </Box>

            {/* Dynamically rendered input fields based on dropdown selection */}
            {conditionalFields()?.map((group: any) =>
              group?.map((field: any) => {
                return (
                  field.field_id === conditionalValidationValue && (
                    <Box
                      key={field.field_id}
                      sx={{
                        marginTop: '10px'
                      }}
                    >
                      {field.type === 'select' && (
                        <Box sx={{ marginBottom: '20px' }}>
                          <FormSelect
                            name="select.selectedOption"
                            // name={field.label}
                            data={field.options}
                            labelStyles={{
                              color: '#3B5864',
                              fontSize: '18px',
                              fontWeight: 400
                            }}
                            label="Select:"
                            placeholder="Select an Option"
                            containerStyles={{
                              width: '100%'
                            }}
                          />
                        </Box>
                      )}
                      {field.input_type === 'radio' &&
                        field.type === 'input' && (
                          <Box sx={{ marginBottom: '20px' }}>
                            <Field name="radio.selectedOption">
                              {({ field: compField }: any) => (
                                <FormControl component="fieldset">
                                  <RadioGroup {...compField} row>
                                    {field.options.map((option: any) => (
                                      <FormControlLabel
                                        key={option.value}
                                        value={option.value}
                                        control={<Radio />}
                                        label={option.label}
                                      />
                                    ))}
                                  </RadioGroup>
                                </FormControl>
                              )}
                            </Field>
                          </Box>
                        )}
                      {field.input_type === 'checkbox' &&
                        field.type === 'input' && (
                          <Box sx={{ marginBottom: '20px' }}>
                            <Field name="checkbox.selectedOptions">
                              {({ field: compField, form }: any) => {
                                const selectedValues = compField.value || [];
                                return (
                                  <FormGroup row>
                                    {field?.options?.map((option: any) => {
                                      const checked = selectedValues.includes(
                                        option.value
                                      );

                                      return (
                                        <FormControlLabel
                                          key={option.value}
                                          control={
                                            <Checkbox
                                              checked={checked}
                                              onChange={() => {
                                                const newValue = checked
                                                  ? selectedValues.filter(
                                                      (val: string) =>
                                                        val !== option.value
                                                    )
                                                  : [
                                                      ...selectedValues,
                                                      option.value
                                                    ];

                                                form.setFieldValue(
                                                  'checkbox.selectedOptions',
                                                  // field.name,
                                                  newValue
                                                );
                                              }}
                                            />
                                          }
                                          label={option.label}
                                        />
                                      );
                                    })}
                                  </FormGroup>
                                );
                              }}
                            </Field>
                          </Box>
                        )}

                      {field.input_type === 'toggle' && (
                        <Box sx={{ marginBottom: '10px' }}>
                          <FormSelect
                            name="toggle.selectedOption"
                            // name="toggleOption"
                            data={field.options}
                            labelStyles={{
                              color: '#7E949D',
                              fontSize: '18px',
                              fontWeight: 400
                            }}
                            label="Select Option"
                            placeholder="Select"
                            containerStyles={{
                              width: '100%'
                            }}
                          />
                        </Box>
                      )}
                      {field.input_type === 'date' && (
                        <Box
                          sx={{
                            display: 'block',
                            gap: '10px'
                          }}
                        >
                          <Box sx={{ marginBottom: '10px' }}>
                            <FormSelect
                              name="date.dateComparision"
                              // name="comparisonOperator"
                              data={[
                                { value: 'lessthan', label: 'Is LessThan' },
                                {
                                  value: 'greaterthan',
                                  label: 'Is GreaterThan'
                                },
                                { value: 'equalto', label: 'Is EqualTo' },
                                {
                                  value: 'lessthanorequalto',
                                  label: 'Is LessThanOrEqualTo'
                                },
                                {
                                  value: 'greaterthanorequalto',
                                  label: 'Is GreaterThanOrEqualTo'
                                },
                                { value: 'inbetween', label: 'Is InBetween' }
                              ]}
                              labelStyles={{
                                color: '#7E949D',
                                fontSize: '18px',
                                fontWeight: 400
                              }}
                              label="Select Date Comparison"
                              placeholder="Select Date Comparison"
                              containerStyles={{
                                width: '100%'
                              }}
                            />
                          </Box>

                          <Box>
                            <Field name="date.dateComparision">
                              {({ field: compField }: any) => {
                                const value = compField?.value || '';
                                return value === 'inbetween' ? (
                                  <>
                                    <FormInput
                                      name="date.selectedStartDate"
                                      type="date"
                                      placeholder="Start Date"
                                      label="Start Date"
                                      containerStyles={{
                                        width: '100%'
                                      }}
                                    />
                                    <FormInput
                                      name="date.selectedEndDate"
                                      type="date"
                                      placeholder="End Date"
                                      label="End Date"
                                      containerStyles={{
                                        width: '100%'
                                      }}
                                    />
                                  </>
                                ) : (
                                  <FormInput
                                    name="date.selectedDate"
                                    type="date"
                                    placeholder="Select Date"
                                    label="Select Date"
                                    containerStyles={{
                                      width: '100%'
                                    }}
                                  />
                                );
                              }}
                            </Field>
                          </Box>
                        </Box>
                      )}
                      {field.input_type === 'time' && (
                        <Box
                          sx={{
                            display: 'block',
                            gap: '10px'
                          }}
                        >
                          <Box sx={{ marginBottom: '10px' }}>
                            <FormSelect
                              name="time.timeComparision"
                              // name="comparisonOperator"
                              data={[
                                { value: 'lessthan', label: 'Is LessThan' },
                                {
                                  value: 'greaterthan',
                                  label: 'Is GreaterThan'
                                },
                                { value: 'equalto', label: 'Is EqualTo' },
                                {
                                  value: 'lessthanorequalto',
                                  label: 'Is LessThanOrEqualTo'
                                },
                                {
                                  value: 'greaterthanorequalto',
                                  label: 'Is GreaterThanOrEqualTo'
                                },
                                { value: 'inbetween', label: 'Is InBetween' }
                              ]}
                              labelStyles={{
                                color: '#7E949D',
                                fontSize: '18px',
                                fontWeight: 400
                              }}
                              label="Select Time Comparison"
                              placeholder="Select Time Comparison"
                              containerStyles={{
                                width: '100%'
                              }}
                            />
                          </Box>

                          <Box>
                            <Field name="time.timeComparision">
                              {({ field: compField }: any) => {
                                const value = compField?.value || '';
                                return value === 'inbetween' ? (
                                  <>
                                    <FormInput
                                      name="time.selectedStartTime"
                                      type="time"
                                      placeholder="Start Time"
                                      label="Start Time"
                                      containerStyles={{
                                        width: '100%'
                                      }}
                                    />
                                    <FormInput
                                      name="time.selectedEndTime"
                                      type="time"
                                      placeholder="End Time"
                                      label="End Time"
                                      containerStyles={{
                                        width: '100%'
                                      }}
                                    />
                                  </>
                                ) : (
                                  <FormInput
                                    name="time.selectedTime"
                                    type="time"
                                    placeholder={field.label}
                                    label={field.label}
                                    containerStyles={{
                                      width: '100%'
                                    }}
                                  />
                                );
                              }}
                            </Field>

                            {/* <FormInput
                            placeholder={field.label}
                            type="time"
                            name="time.selectedTime"
                            // name={field.label}
                            label={undefined}
                            containerStyles={{
                              width: '100%'
                            }}
                          /> */}
                          </Box>
                        </Box>
                      )}
                      {field.input_type === 'datetime-local' && (
                        <Box
                          sx={{
                            display: 'block',
                            gap: '10px'
                          }}
                        >
                          <Box sx={{ marginBottom: '10px' }}>
                            <FormSelect
                              name="datetime.datetimeComparision"
                              // name="comparisonOperator"
                              data={[
                                { value: 'lessthan', label: 'Is LessThan' },
                                {
                                  value: 'greaterthan',
                                  label: 'Is GreaterThan'
                                },
                                { value: 'equalto', label: 'Is EqualTo' },
                                {
                                  value: 'lessthanorequalto',
                                  label: 'Is LessThanOrEqualTo'
                                },
                                {
                                  value: 'greaterthanorequalto',
                                  label: 'Is GreaterThanOrEqualTo'
                                },
                                { value: 'inbetween', label: 'Is InBetween' }
                              ]}
                              labelStyles={{
                                color: '#7E949D',
                                fontSize: '18px',
                                fontWeight: 400
                              }}
                              label="Select DateTime Comparison"
                              placeholder="Select DateTime Comparison"
                              containerStyles={{
                                width: '100%'
                              }}
                            />
                          </Box>

                          <Box>
                            <Field name="datetime.datetimeComparision">
                              {({ field: compField }: any) => {
                                const value = compField?.value || '';
                                return value === 'inbetween' ? (
                                  <>
                                    <FormInput
                                      name="datetime.selectedStartDateTime"
                                      type="datetime-local"
                                      placeholder="Start DateTime"
                                      label="Start DateTime"
                                      containerStyles={{
                                        width: '100%'
                                      }}
                                    />
                                    <FormInput
                                      name="datetime.selectedEndDateTime"
                                      type="datetime-local"
                                      placeholder="End DateTime"
                                      label="End DateTime"
                                      containerStyles={{
                                        width: '100%'
                                      }}
                                    />
                                  </>
                                ) : (
                                  <FormInput
                                    name="datetime.selectedDatetime"
                                    type="datetime-local"
                                    placeholder={field.label}
                                    label="Select DateTime"
                                    containerStyles={{
                                      width: '100%'
                                    }}
                                  />
                                );
                              }}
                            </Field>

                            {/* <FormInput
                            placeholder={field.label}
                            type="datetime-local"
                            name="datetime.selectedDatetime"
                            // name={field.label}
                            label={undefined}
                            containerStyles={{
                              width: '100%'
                            }}
                          /> */}
                          </Box>
                        </Box>
                      )}
                    </Box>
                  )
                );
              })
            )}

            <Box sx={{ marginBottom: '20px' }}>
              <Field name="conditions">
                {({ field, form }: any) => {
                  const selectedValues = field.value || [];
                  const options = [
                    {
                      value: 'isRequired',
                      label: 'IsRequired'
                    },
                    { value: 'isDisplay', label: 'IsDisplay' }
                  ];
                  return (
                    <FormGroup row>
                      {options?.map((option: any) => {
                        const checked = selectedValues.includes(option.value);

                        return (
                          <FormControlLabel
                            key={option.value}
                            control={
                              <Checkbox
                                checked={checked}
                                onChange={() => {
                                  const newValue = checked
                                    ? selectedValues.filter(
                                        (val: string) => val !== option.value
                                      )
                                    : [...selectedValues, option.value];

                                  form.setFieldValue(
                                    'conditions',
                                    // field.name,
                                    newValue
                                  );
                                }}
                              />
                            }
                            label={option.label}
                          />
                        );
                      })}
                    </FormGroup>
                  );
                }}
              </Field>
            </Box>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                gap: 2,
                marginTop: '20px'
              }}
            >
              <Button
                onClick={onClose}
                sx={{
                  backgroundColor: 'white2.main',
                  color: 'primaryBlue.main',
                  padding: '10px 30px',
                  boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                  textTransform: 'capitalize'
                }}
              >
                CANCEL
              </Button>
              <Button
                type="submit"
                sx={{
                  backgroundColor: 'primaryBlue.main',
                  color: 'white2.main',
                  padding: '10px 35px',
                  boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                  '&:hover': {
                    color: 'white2.main',
                    backgroundColor: '#08366b',
                    boxShadow: '0px 8px 10px 4px rgba(0,0,0,0.2)'
                  }
                }}
              >
                SAVE
              </Button>
            </Box>
          </Box>
        </AppForm>
      )}
    </Box>
  );
};
export default CustomValidations;
