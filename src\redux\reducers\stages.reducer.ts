import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import {
  createStages,
  getStages,
  getStageWithId,
  updateStages
} from '../../apis/stages';

interface InitialStateProps {
  errors: Record<string, string>;
  isLoading: boolean;
  loadingError: Record<string, string>;
  loadingSpinner: boolean;
  stage: any;
  stagesList: any[];
}
const initialState: InitialStateProps = {
  errors: {},
  isLoading: false,
  loadingError: {},
  loadingSpinner: false,

  stage: {},
  stagesList: []
};

export const createstage = createAsyncThunk('createstage', createStages);
export const updatestage = createAsyncThunk('updatestage', updateStages);
export const getstages = createAsyncThunk('getstages', getStages);
export const getstagewithid = createAsyncThunk(
  'getstagewithid',
  getStageWithId
);

const stagesSlice = createSlice({
  name: 'stages',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(createstage.pending, (state) => {
        state.isLoading = true;
        state.loadingSpinner = true;
      })
      .addCase(createstage.fulfilled, (state, action: any) => {
        state.isLoading = false;
        const { stage } = action.payload;
        state.stage = stage;
        state.stagesList.push(stage);
        state.loadingSpinner = false;
      })
      .addCase(createstage.rejected, (state) => {
        state.isLoading = false;
        state.loadingSpinner = false;

        // state.loadingError = action.payload;
      });

    builder
      .addCase(updatestage.pending, (state) => {
        state.isLoading = true;
        state.loadingSpinner = true;
      })
      .addCase(updatestage.fulfilled, (state, action: any) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.stage = data;
        state.loadingSpinner = false;
      })
      .addCase(updatestage.rejected, (state) => {
        state.isLoading = false;
        state.loadingSpinner = false;

        // state.loadingError = action.payload;
      });

    builder
      .addCase(getstages.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getstages.fulfilled, (state, action: any) => {
        state.isLoading = false;
        state.stagesList = action.payload.stages;
      })
      .addCase(getstages.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });

    builder
      .addCase(getstagewithid.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getstagewithid.fulfilled, (state, action: any) => {
        state.isLoading = false;
        const { stage } = action.payload;
        state.stage = stage;
      })
      .addCase(getstagewithid.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
  }
});

export default stagesSlice.reducer;
