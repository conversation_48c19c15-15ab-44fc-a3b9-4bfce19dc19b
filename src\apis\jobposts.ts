import api, { handleError, apiRoutes, authHeaders } from './config';

export const createsJobPost = async (
  data: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post(apiRoutes.job, data, { headers });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const updateJobPost = async (
  payload: { id: string; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.put(
      `${apiRoutes.job}/${payload.id}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getJobPosts = async (
  payload: { page: number; limit: number } | undefined,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(
      `${apiRoutes.job}?page=${payload?.page}&limit=${payload?.limit}`,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getJobPostWithId = async (
  id: string | undefined,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.job}/${id}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
