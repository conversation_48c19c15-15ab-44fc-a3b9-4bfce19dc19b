import axios, { AxiosError, AxiosInstance, isAxiosError } from 'axios';
import { getStorageItem } from '../utils/index';

const getAPIBaseURL = (): string => {
  switch (import.meta.env.VITE_API_ENV) {
    case 'DEV':
      return 'https://klezaformsapiv2.klezaclients.net/';
    case 'DEV_V3':
      return 'https://klezaformsapiv3.klezaclients.net/';
    case 'DEV_DEMO':
      return 'https://demoklezafabapi.klezaclients.net/';
    case 'PROD':
      return 'https://api.klezafab.com/';
    case 'LOCAL':
      return 'http://localhost:3001/';
    default:
      return import.meta.env.VITE_API_URL;
  }
};

export const HTTP_ENDPOINT: string = getAPIBaseURL();

const api: AxiosInstance = axios.create({
  baseURL: HTTP_ENDPOINT,
  formSerializer: {}
});

export const apiRoutes = {
  admin: 'admin',
  organization: 'organization',
  user: 'user',
  onboardingEmployee: 'onboarding-employee',
  onboardEmpChecklist: 'onboard-emp-checklist',
  formValue: 'form-value',
  onboardOrgChecklist: 'onboard-org-checklist',
  userValidation: 'auth/validate-user',
  apps: 'apps',
  orgAppConfiguration: 'org-app-configuration',
  orgAppDashboardConfiguration: 'org-app-dashboard-configuration',
  primaryForm: 'primary-forms-repository/primary-form',
  form: 'forms-repository',
  formsSection: 'forms-section-repository',
  organizations: 'organization/public',
  industryTypes: 'industry-types',
  industryAppProcess: 'industry-app-process',
  clientsRepository: 'clients-repository',
  caregivers: 'caregivers',
  configurations: 'configurations',
  auth: {
    login: 'auth/admin/login',
    orgAuth: 'auth/organization/login'
    // login: 'auth/login',
  },
  forms: 'user/forms',
  clients: 'clients-repository',
  themes: 'themes',
  countryStateCity: {
    states: 'country-state-city/states/',
    countries: 'country-state-city/countries',
    countryCodeByLatLng: 'country-state-city/county-code',
    cities: 'country-state-city/cities/'
  },
  applicant: '/onboarding-employee',
  stages: 'stages',
  job: 'job',
  // editApplicant: "/onboarding-employee/:employeeId",
  // removeApplicant: "onboarding-employee/:empId",
  reusableFields: '/reusable-fields'
};

export const getToken = async () => getStorageItem('access_token');

export const authHeaders = async (headers?: any) => {
  const token = await getToken();
  const appheaders = {
    'Content-Type': 'application/json'
  };
  if (token) {
    if (headers) {
      return {
        ...headers,
        ...appheaders,
        Authorization: `Bearer ${token}`
      };
    }
    return {
      ...appheaders,
      Authorization: `Bearer ${token}`
    };
  }
  return {
    ...appheaders,
    ...headers
  };
};

export const handleError = (error: any, cb: any) => {
  if (isAxiosError(error)) {
    const serverError = error as AxiosError;
    if (serverError && serverError.response) {
      return cb(serverError.response.data);
    }
  }
  return cb({
    error: {
      message: 'Something went wrong. Please try again.'
    }
  });
};

export default api;
