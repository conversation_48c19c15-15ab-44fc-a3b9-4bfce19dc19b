import { Box, Button, Typography, useTheme } from '@mui/material';
import { useSortable } from '@dnd-kit/sortable';
import { useSelector } from 'react-redux';
import { isUndefined } from 'lodash';
import ReactQuill from 'react-quill';
import { useState } from 'react';
// Locale Imports
import '../../css/index.scss';
import { APPFIELD } from '../../types';
import { RootState } from '../../redux/reducers';
import {
  capitalizeWords,
  formatText,
  getCanvasWidth,
  removeHtmlTags
} from '../../utils/functions';
import { ActiveField } from './ActiveField';
import { InactiveField } from './InactiveField';
import {
  FormCheckbox,
  FormFileInput,
  FormInput,
  FormParagraph,
  FormPhoneInput,
  FormRadio,
  FormSelect,
  FormSignature,
  FormToggle
} from '../form.elements';

export const RenderField: React.FC<APPFIELD> = ({
  field,
  groupKey,
  index,
  findex: aindex,
  secIndex,
  colIndex,
  id
}) => {
  let component;
  const theme = useTheme();
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });
  const { sectionIndex, columnIndex } = useSelector(
    (state: RootState) => state.form
  );
  const [paragraphText, setParagraphText] = useState('');

  const handlePragraphText = async (value: any) => {
    setParagraphText(value);
  };

  const generateSignatureNames = (type: 'date' | 'agree'): string => {
    let fieldName = `${field.name}_${type}`;
    if (field.is_iterative_or_not) {
      fieldName = `${fieldName}.${aindex}`;
    }
    if (isUndefined(index) === false) {
      fieldName = `${index}.${fieldName}`;
    }
    fieldName = `${groupKey}.${fieldName}`;
    return fieldName;
  };
  const generateOriginalFieldId = (): string => {
    let fieldName = field?.original_field_id;
    if (field?.is_iterative_or_not) {
      fieldName = `${fieldName}.${aindex}`;
    }
    if (isUndefined(index) === false) {
      fieldName = `${index}.${fieldName}`;
    }
    fieldName = `${groupKey}.${fieldName}`;
    return fieldName;
  };
  const generateFieldName = (): string => {
    let fieldName = field?.name;
    if (field?.is_iterative_or_not) {
      fieldName = `${fieldName}.${aindex}`;
    }
    if (isUndefined(index) === false) {
      fieldName = `${index}.${fieldName}`;
    }
    fieldName = `${groupKey}.${fieldName}`;
    return fieldName;
  };

  const fieldName = generateFieldName();
  const formFieldId = generateOriginalFieldId();

  const fieldLabelId = `groups[${secIndex}].fields[${colIndex}].label`;
  const fielddescription = `groups[${secIndex}].fields[${colIndex}].description`;
  const fieldpoints = `groups[${secIndex}].fields[${colIndex}].points`;
  const fieldCheckboxAnswerType = `groups[${secIndex}].fields[${colIndex}].checkbox_answer_type`;
  const fieldCheckboxAnswerLimit = `groups[${secIndex}].fields[${colIndex}].checkbox_answer_limit`;
  const fieldOriginalId = `groups[${secIndex}].fields[${colIndex}].original_field_id`;
  const fieldIteration = `groups[${secIndex}].fields[${colIndex}].is_iterative_or_not`;
  const fieldMaxIterationLength = `groups[${secIndex}].fields[${colIndex}].iteration_max_length`;
  const fieldValue = `groups[${secIndex}].fields[${colIndex}].value`;
  const fieldrequired = `groups[${secIndex}].fields[${colIndex}].validationSchema.required`;

  const fieldId = field?.field_id;

  const fieldLabel = () => {
    const label = capitalizeWords(
      formatText(removeHtmlTags(field?.label), 'plain')
    );

    const isRequired = field?.validation_schema?.required;
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
        <span>
          {label} {isRequired && <span style={{ color: 'red' }}>*</span>}
        </span>
        {field?.label_url_type === 'image' && (
          <img
            src={field?.label_url}
            alt="Label related"
            style={{ marginLeft: '8px', width: '260px', height: '180px' }}
          />
        )}
        {field?.label_url_type === 'video' && (
          <iframe
            width="260"
            height="180"
            src={field?.label_url}
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            style={{ marginLeft: '8px' }}
            title="Label related video"
          />
        )}
      </div>
    );
  };

  const label = fieldLabel();

  switch (field?.type?.toLowerCase()) {
    case 'input':
      if (field.input_type === 'number') {
        if (secIndex === sectionIndex && colIndex === columnIndex) {
          component = (
            <ActiveField
              name={fieldName}
              field={field}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
              formFieldId={formFieldId}
              fieldLabelId={fieldLabelId}
              fieldOriginalId={fieldOriginalId}
              fieldIteration={fieldIteration}
              fieldMaxIterationLength={fieldMaxIterationLength}
              groupKey={groupKey}
              fieldId={fieldId}
              fielddescription={fielddescription}
              fieldpoints={fieldpoints}
              fieldCheckboxAnswerType={fieldCheckboxAnswerType}
              fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
              fieldrequired={fieldrequired}
            />
          );
        } else if (colIndex !== columnIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormInput
                type="number"
                name={fieldLabelId}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormInput
                type="number"
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else {
          component = (
            <FormInput
              type="number"
              name={fieldName}
              label={label}
              description={field?.description_status ? field?.description : ''}
              containerStyles={{
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                width: {
                  xs: '100%',
                  sm: `calc(100% / 2 - ${theme.spacing(1.2)})`
                }
              }}
            />
          );
        }
      } else if (field.input_type === 'email') {
        if (secIndex === sectionIndex && colIndex === columnIndex) {
          component = (
            <ActiveField
              name={fieldName}
              field={field}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
              formFieldId={formFieldId}
              fieldLabelId={fieldLabelId}
              fieldOriginalId={fieldOriginalId}
              fieldIteration={fieldIteration}
              fieldMaxIterationLength={fieldMaxIterationLength}
              groupKey={groupKey}
              fieldId={fieldId}
              fielddescription={fielddescription}
              fieldpoints={fieldpoints}
              fieldCheckboxAnswerType={fieldCheckboxAnswerType}
              fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
              fieldrequired={fieldrequired}
            />
          );
        } else if (colIndex !== columnIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormInput
                name={fieldName}
                type="email"
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormInput
                name={fieldName}
                type="email"
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else {
          component = (
            <FormInput
              name={fieldName}
              type="email"
              label={label}
              description={field?.description_status ? field?.description : ''}
              containerStyles={{
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                width: {
                  xs: '100%',
                  sm: `calc(100% / 2 - ${theme.spacing(1.2)})`
                }
              }}
            />
          );
        }
      } else if (field.input_type === 'checkbox') {
        if (secIndex === sectionIndex && colIndex === columnIndex) {
          component = (
            <ActiveField
              name={fieldName}
              field={field}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
              formFieldId={formFieldId}
              fieldLabelId={fieldLabelId}
              fieldOriginalId={fieldOriginalId}
              fieldIteration={fieldIteration}
              fieldMaxIterationLength={fieldMaxIterationLength}
              groupKey={groupKey}
              fieldId={fieldId}
              fielddescription={fielddescription}
              fieldpoints={fieldpoints}
              fieldCheckboxAnswerType={fieldCheckboxAnswerType}
              fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
              fieldrequired={fieldrequired}
            />
          );
        } else if (colIndex !== columnIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormCheckbox
                data={field?.options}
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  width: '100%',
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`
                }}
              />
            </InactiveField>
          );
        } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormCheckbox
                data={field?.options}
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  width: '100%',
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`
                }}
              />
            </InactiveField>
          );
        } else {
          component = (
            <FormCheckbox
              data={field?.options}
              name={fieldName}
              label={label}
              description={field?.description_status ? field?.description : ''}
              containerStyles={{
                width: '100%',
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`
              }}
            />
          );
        }
      } else if (field.input_type === 'radio') {
        if (secIndex === sectionIndex && colIndex === columnIndex) {
          component = (
            <ActiveField
              name={fieldName}
              field={field}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
              formFieldId={formFieldId}
              fieldLabelId={fieldLabelId}
              fieldOriginalId={fieldOriginalId}
              fieldIteration={fieldIteration}
              fieldMaxIterationLength={fieldMaxIterationLength}
              groupKey={groupKey}
              fieldId={fieldId}
              fielddescription={fielddescription}
              fieldpoints={fieldpoints}
              fieldCheckboxAnswerType={fieldCheckboxAnswerType}
              fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
              fieldrequired={fieldrequired}
            />
          );
        } else if (colIndex !== columnIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormRadio
                data={field?.options}
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  width: '100%',
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`
                }}
              />
            </InactiveField>
          );
        } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormRadio
                data={field?.options}
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  width: '100%',
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`
                }}
              />
            </InactiveField>
          );
        } else {
          component = (
            <FormRadio
              data={field?.options}
              name={fieldName}
              label={label}
              description={field?.description_status ? field?.description : ''}
              containerStyles={{
                width: '100%',
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`
              }}
            />
          );
        }
      } else if (field.input_type === 'file') {
        if (secIndex === sectionIndex && colIndex === columnIndex) {
          component = (
            <ActiveField
              name={fieldName}
              field={field}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
              formFieldId={formFieldId}
              fieldLabelId={fieldLabelId}
              fieldOriginalId={fieldOriginalId}
              fieldIteration={fieldIteration}
              fieldMaxIterationLength={fieldMaxIterationLength}
              groupKey={groupKey}
              fieldId={fieldId}
              fielddescription={fielddescription}
              fieldpoints={fieldpoints}
              fieldCheckboxAnswerType={fieldCheckboxAnswerType}
              fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
              fieldrequired={fieldrequired}
            />
          );
        } else if (colIndex !== columnIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormFileInput
                name={fieldName}
                type="file"
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                isCreateForm
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormFileInput
                name={fieldName}
                type="file"
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                isCreateForm
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else {
          component = (
            <FormFileInput
              name={fieldName}
              type="file"
              label={label}
              description={field?.description_status ? field?.description : ''}
              isCreateForm
              containerStyles={{
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                width: {
                  xs: '100%',
                  sm: `calc(100% / 2 - ${theme.spacing(1.2)})`
                }
              }}
            />
          );
        }
      } else if (
        field.input_type.includes('data') ||
        field.input_type.includes('time')
      ) {
        if (secIndex === sectionIndex && colIndex === columnIndex) {
          component = (
            <ActiveField
              name={fieldName}
              field={field}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
              formFieldId={formFieldId}
              fieldLabelId={fieldLabelId}
              fieldOriginalId={fieldOriginalId}
              fieldIteration={fieldIteration}
              fieldMaxIterationLength={fieldMaxIterationLength}
              groupKey={groupKey}
              fieldId={fieldId}
              fielddescription={fielddescription}
              fieldpoints={fieldpoints}
              fieldCheckboxAnswerType={fieldCheckboxAnswerType}
              fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
              fieldrequired={fieldrequired}
            />
          );
        } else if (colIndex !== columnIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormInput
                name={fieldName}
                type={
                  field.input_type.includes('datetime')
                    ? 'datetime-local'
                    : field.input_type
                }
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormInput
                name={fieldName}
                type={
                  field.input_type.includes('datetime')
                    ? 'datetime-local'
                    : field.input_type
                }
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else {
          component = (
            <FormInput
              name={fieldName}
              type={
                field.input_type.includes('datetime')
                  ? 'datetime-local'
                  : field.input_type
              }
              label={label}
              description={field?.description_status ? field?.description : ''}
              containerStyles={{
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                width: {
                  xs: '100%',
                  sm: `calc(100% / 2 - ${theme.spacing(1.2)})`
                }
              }}
            />
          );
        }
      } else if (field.input_type === 'phone') {
        if (secIndex === sectionIndex && colIndex === columnIndex) {
          component = (
            <ActiveField
              name={fieldName}
              field={field}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
              formFieldId={formFieldId}
              fieldLabelId={fieldLabelId}
              fieldOriginalId={fieldOriginalId}
              fieldIteration={fieldIteration}
              fieldMaxIterationLength={fieldMaxIterationLength}
              groupKey={groupKey}
              fieldId={fieldId}
              fielddescription={fielddescription}
              fieldpoints={fieldpoints}
              fieldCheckboxAnswerType={fieldCheckboxAnswerType}
              fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
              fieldrequired={fieldrequired}
            />
          );
        } else if (colIndex !== columnIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormPhoneInput
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormPhoneInput
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else {
          component = (
            <FormPhoneInput
              name={fieldName}
              label={label}
              description={field?.description_status ? field?.description : ''}
              containerStyles={{
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                width: {
                  xs: '100%',
                  sm: `calc(100% / 2 - ${theme.spacing(1.2)})`
                }
              }}
            />
          );
        }
      } else if (field.input_type === 'fax') {
        if (secIndex === sectionIndex && colIndex === columnIndex) {
          component = (
            <ActiveField
              name={fieldName}
              field={field}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
              formFieldId={formFieldId}
              fieldLabelId={fieldLabelId}
              fieldOriginalId={fieldOriginalId}
              fieldIteration={fieldIteration}
              fieldMaxIterationLength={fieldMaxIterationLength}
              groupKey={groupKey}
              fieldId={fieldId}
              fielddescription={fielddescription}
              fieldpoints={fieldpoints}
              fieldCheckboxAnswerType={fieldCheckboxAnswerType}
              fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
              fieldrequired={fieldrequired}
            />
          );
        } else if (colIndex !== columnIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormPhoneInput
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormPhoneInput
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else {
          component = (
            <FormPhoneInput
              name={fieldName}
              label={label}
              description={field?.description_status ? field?.description : ''}
              containerStyles={{
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                width: '100%'
              }}
            />
          );
        }
      } else if (field.input_type === 'url') {
        if (secIndex === sectionIndex && colIndex === columnIndex) {
          component = (
            <ActiveField
              name={fieldName}
              field={field}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
              formFieldId={formFieldId}
              fieldLabelId={fieldLabelId}
              fieldOriginalId={fieldOriginalId}
              fieldIteration={fieldIteration}
              fieldMaxIterationLength={fieldMaxIterationLength}
              groupKey={groupKey}
              fieldId={fieldId}
              fielddescription={fielddescription}
              fieldpoints={fieldpoints}
              fieldCheckboxAnswerType={fieldCheckboxAnswerType}
              fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
              fieldrequired={fieldrequired}
              fieldValue={fieldValue}
            />
          );
        } else if (colIndex !== columnIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              {label}
              {field?.value && (
                <Box sx={{ display: 'flex' }}>
                  <p>{field?.value}</p>
                </Box>
              )}
            </InactiveField>
          );
        } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              {label}
              {field?.value && (
                <Box sx={{ display: 'flex' }}>
                  <p>{field?.value}</p>
                </Box>
              )}
            </InactiveField>
          );
        } else {
          component = (
            <>
              {label}
              {field?.value && (
                <Box sx={{ display: 'flex' }}>
                  <p>{field?.value}</p>
                </Box>
              )}
            </>
          );
        }
      } else if (field.input_type === 'currency') {
        if (secIndex === sectionIndex && colIndex === columnIndex) {
          component = (
            <ActiveField
              name={fieldName}
              field={field}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
              formFieldId={formFieldId}
              fieldLabelId={fieldLabelId}
              fieldOriginalId={fieldOriginalId}
              fieldIteration={fieldIteration}
              fieldMaxIterationLength={fieldMaxIterationLength}
              groupKey={groupKey}
              fieldId={fieldId}
              fielddescription={fielddescription}
              fieldpoints={fieldpoints}
              fieldCheckboxAnswerType={fieldCheckboxAnswerType}
              fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
              fieldrequired={fieldrequired}
            />
          );
        } else if (colIndex !== columnIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormInput
                name={fieldName}
                type="text"
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormInput
                name={fieldName}
                type="text"
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else {
          component = (
            <FormInput
              name={fieldName}
              type="text"
              label={label}
              description={field?.description_status ? field?.description : ''}
              containerStyles={{
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                width: {
                  xs: '100%',
                  sm: `calc(100% / 2 - ${theme.spacing(1.2)})`
                }
              }}
            />
          );
        }
      } else if (secIndex === sectionIndex && colIndex === columnIndex) {
        component = (
          <ActiveField
            name={fieldName}
            field={field}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
            formFieldId={formFieldId}
            fieldLabelId={fieldLabelId}
            fieldOriginalId={fieldOriginalId}
            fieldIteration={fieldIteration}
            fieldMaxIterationLength={fieldMaxIterationLength}
            groupKey={groupKey}
            fieldId={fieldId}
            fielddescription={fielddescription}
            fieldpoints={fieldpoints}
            fieldCheckboxAnswerType={fieldCheckboxAnswerType}
            fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
            fieldrequired={fieldrequired}
          />
        );
      } else if (colIndex !== columnIndex) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            <FormInput
              name={fieldName}
              type="text"
              label={label}
              description={field?.description_status ? field?.description : ''}
              containerStyles={{
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                width: '100%'
              }}
            />
          </InactiveField>
        );
      } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            <FormInput
              name={fieldName}
              type="text"
              label={label}
              description={field?.description_status ? field?.description : ''}
              containerStyles={{
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                width: '100%'
              }}
            />
          </InactiveField>
        );
      } else {
        component = (
          <FormInput
            name={fieldName}
            type="text"
            label={label}
            description={field?.description_status ? field?.description : ''}
            containerStyles={{
              margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
              width: {
                xs: '100%',
                sm: `calc(100% / 2 - ${theme.spacing(1.2)})`
              }
            }}
          />
        );
      }
      break;
    case 'toggle':
      if (secIndex === sectionIndex && colIndex === columnIndex) {
        component = (
          <ActiveField
            name={fieldName}
            field={field}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
            formFieldId={formFieldId}
            fieldLabelId={fieldLabelId}
            fieldOriginalId={fieldOriginalId}
            fieldIteration={fieldIteration}
            fieldMaxIterationLength={fieldMaxIterationLength}
            groupKey={groupKey}
            fieldId={fieldId}
            fielddescription={fielddescription}
            fieldpoints={fieldpoints}
            fieldCheckboxAnswerType={fieldCheckboxAnswerType}
            fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
            fieldrequired={fieldrequired}
          />
        );
      } else if (colIndex !== columnIndex) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            <FormToggle
              name={fieldName}
              label={label}
              description={field?.description_status ? field?.description : ''}
              data={field?.options}
              containerStyles={{
                width: '100%',
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`
              }}
            />
          </InactiveField>
        );
      } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            <FormToggle
              name={fieldName}
              label={label}
              description={field?.description_status ? field?.description : ''}
              data={field?.options}
              containerStyles={{
                width: '100%',
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`
              }}
            />
          </InactiveField>
        );
      } else {
        component = (
          <FormToggle
            name={fieldName}
            label={label}
            description={field?.description_status ? field?.description : ''}
            data={field?.options}
            containerStyles={{
              width: '100%',
              margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`
            }}
          />
        );
      }
      break;
    case 'download':
      if (secIndex === sectionIndex && colIndex === columnIndex) {
        component = (
          <ActiveField
            name={fieldName}
            field={field}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
            formFieldId={formFieldId}
            fieldLabelId={fieldLabelId}
            fieldOriginalId={fieldOriginalId}
            fieldIteration={fieldIteration}
            fieldMaxIterationLength={fieldMaxIterationLength}
            groupKey={groupKey}
            fieldId={fieldId}
            fielddescription={fielddescription}
            fieldpoints={fieldpoints}
            fieldCheckboxAnswerType={fieldCheckboxAnswerType}
            fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
            fieldrequired={fieldrequired}
          />
        );
      } else if (colIndex !== columnIndex) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            {label}
            <Box>
              {!field?.value ? (
                <>
                  <p>Upload a file for the user to download</p>
                  <Button
                    variant="outlined"
                    className="d-flex align-items-center"
                    sx={{
                      p: '7px 30px',
                      borderRadius: '50px',
                      boxShadow: '0px 0px 2px 0px',
                      color: '#000'
                    }}
                  >
                    Upload
                  </Button>
                  <p style={{ color: 'red' }}>Upload document is mandatory</p>
                </>
              ) : (
                <p>Document uploaded successfully</p>
              )}
            </Box>
          </InactiveField>
        );
      } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            {label}
            <Box>
              {!field?.value ? (
                <>
                  <p>Upload a file for the user to download</p>
                  <Button
                    variant="outlined"
                    className="d-flex align-items-center"
                    sx={{
                      p: '7px 30px',
                      borderRadius: '50px',
                      boxShadow: '0px 0px 2px 0px',
                      color: '#000'
                    }}
                  >
                    Upload
                  </Button>
                  <p style={{ color: 'red' }}>Upload document is mandatory</p>
                </>
              ) : (
                <p>Document uploaded successfully</p>
              )}
            </Box>
          </InactiveField>
        );
      } else {
        component = (
          <>
            {label}
            <Box>
              {!field?.value ? (
                <>
                  <p>Upload a file for the user to download</p>
                  <Button
                    variant="outlined"
                    className="d-flex align-items-center"
                    sx={{
                      p: '7px 30px',
                      borderRadius: '50px',
                      boxShadow: '0px 0px 2px 0px',
                      color: '#000'
                    }}
                  >
                    Upload
                  </Button>
                  <p style={{ color: 'red' }}>Upload document is mandatory</p>
                </>
              ) : (
                <p>Document uploaded successfully</p>
              )}
            </Box>
          </>
        );
      }
      break;
    case 'textarea':
      if (secIndex === sectionIndex && colIndex === columnIndex) {
        component = (
          <ActiveField
            name={fieldName}
            field={field}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
            formFieldId={formFieldId}
            fieldLabelId={fieldLabelId}
            fieldOriginalId={fieldOriginalId}
            fieldIteration={fieldIteration}
            fieldMaxIterationLength={fieldMaxIterationLength}
            groupKey={groupKey}
            fieldId={fieldId}
            fielddescription={fielddescription}
            fieldpoints={fieldpoints}
            fieldCheckboxAnswerType={fieldCheckboxAnswerType}
            fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
            fieldrequired={fieldrequired}
          />
        );
      } else if (colIndex !== columnIndex || secIndex !== sectionIndex) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            <FormInput
              name={fieldName}
              type="text"
              label={label}
              description={field?.description_status ? field?.description : ''}
              multiline
              containerStyles={{
                width: '100%',
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`
              }}
            />
          </InactiveField>
        );
      } else {
        component = (
          <FormInput
            name={fieldName}
            type="text"
            label={label}
            description={field?.description_status ? field?.description : ''}
            multiline
            containerStyles={{
              width: '100%',
              margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`
            }}
          />
        );
      }
      break;
    case 'select':
      if (secIndex === sectionIndex && colIndex === columnIndex) {
        component = (
          <ActiveField
            name={fieldName}
            field={field}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
            formFieldId={formFieldId}
            fieldLabelId={fieldLabelId}
            fieldOriginalId={fieldOriginalId}
            fieldIteration={fieldIteration}
            fieldMaxIterationLength={fieldMaxIterationLength}
            groupKey={groupKey}
            fieldId={fieldId}
            fielddescription={fielddescription}
            fieldpoints={fieldpoints}
            fieldCheckboxAnswerType={fieldCheckboxAnswerType}
            fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
            fieldrequired={fieldrequired}
          />
        );
      } else if (colIndex !== columnIndex || secIndex !== sectionIndex) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            <FormSelect
              name={fieldName}
              label={label}
              description={field?.description_status ? field?.description : ''}
              data={field?.options}
              containerStyles={{
                width: '100%',
                color: '#000000'
              }}
            />
          </InactiveField>
        );
      } else {
        component = (
          <FormSelect
            name={fieldName}
            label={label}
            description={field?.description_status ? field?.description : ''}
            data={field?.options}
            containerStyles={{
              width: '100%',
              color: '#000000'
            }}
          />
        );
      }
      break;
    case 'signature':
      if (secIndex === sectionIndex && colIndex === columnIndex) {
        component = (
          <ActiveField
            name={fieldName}
            field={field}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
            formFieldId={formFieldId}
            fieldLabelId={fieldLabelId}
            fieldOriginalId={fieldOriginalId}
            fieldIteration={fieldIteration}
            fieldMaxIterationLength={fieldMaxIterationLength}
            groupKey={groupKey}
            fieldId={fieldId}
            fielddescription={fielddescription}
            fieldpoints={fieldpoints}
            fieldCheckboxAnswerType={fieldCheckboxAnswerType}
            fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
            fieldrequired={fieldrequired}
          />
        );
      } else if (colIndex !== columnIndex || secIndex !== sectionIndex) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            <FormSignature
              name={fieldName}
              label={label}
              description={field?.description_status ? field?.description : ''}
              containerStyles={{
                width: '100%',
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`
              }}
              canvasProps={{
                width: getCanvasWidth(),
                height: 180
              }}
              imageProps={{
                width: getCanvasWidth(),
                height: 180
              }}
              dateName={generateSignatureNames('date')}
              agreeName={generateSignatureNames('agree')}
            />
          </InactiveField>
        );
      } else {
        component = (
          <FormSignature
            name={fieldName}
            label={label}
            description={field?.description_status ? field?.description : ''}
            containerStyles={{
              width: '100%',
              margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`
            }}
            canvasProps={{
              width: getCanvasWidth(),
              height: 180
            }}
            imageProps={{
              width: getCanvasWidth(),
              height: 180
            }}
            dateName={generateSignatureNames('date')}
            agreeName={generateSignatureNames('agree')}
          />
        );
      }
      break;
    case 'image':
      if (secIndex === sectionIndex && colIndex === columnIndex) {
        component = (
          <ActiveField
            name={fieldName}
            field={field}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
            formFieldId={formFieldId}
            fieldLabelId={fieldLabelId}
            fieldOriginalId={fieldOriginalId}
            fieldIteration={fieldIteration}
            fieldMaxIterationLength={fieldMaxIterationLength}
            groupKey={groupKey}
            fieldId={fieldId}
            fielddescription={fielddescription}
            fieldpoints={fieldpoints}
            fieldCheckboxAnswerType={fieldCheckboxAnswerType}
            fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
            fieldrequired={fieldrequired}
          />
        );
      } else if (colIndex !== columnIndex || secIndex !== sectionIndex) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            <FormFileInput
              name={fieldName}
              type="file"
              label={label}
              description={field?.description_status ? field?.description : ''}
              isCreateForm
              containerStyles={{
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                width: '100%'
              }}
            />
          </InactiveField>
        );
      } else {
        component = (
          <FormFileInput
            name={fieldName}
            type="file"
            label={label}
            description={field?.description_status ? field?.description : ''}
            isCreateForm
            containerStyles={{
              margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
              width: '100%'
            }}
          />
        );
      }
      break;
    case 'paragraph':
      if (secIndex === sectionIndex && colIndex === columnIndex) {
        component = (
          <ActiveField
            name={fieldName}
            field={field}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
            formFieldId={formFieldId}
            fieldLabelId={fieldLabelId}
            fieldOriginalId={fieldOriginalId}
            fieldIteration={fieldIteration}
            fieldMaxIterationLength={fieldMaxIterationLength}
            groupKey={groupKey}
            fieldId={fieldId}
            fielddescription={fielddescription}
            fieldpoints={fieldpoints}
            fieldCheckboxAnswerType={fieldCheckboxAnswerType}
            fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
            fieldrequired={fieldrequired}
          />
        );
      } else if (colIndex !== columnIndex || secIndex !== sectionIndex) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            <FormParagraph
              label={label}
              description={field?.description || ''}
              containerStyles={{
                width: '100%',
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`
              }}
            />
          </InactiveField>
        );
      } else {
        component = (
          <FormParagraph
            label={label}
            description={field?.description || ''}
            containerStyles={{
              width: '100%',
              margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`
            }}
          />
        );
      }
      break;
    case 'editor':
      if (secIndex === sectionIndex && colIndex === columnIndex) {
        component = (
          <ActiveField
            name={fieldName}
            field={field}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
            formFieldId={formFieldId}
            fieldLabelId={fieldLabelId}
            fieldOriginalId={fieldOriginalId}
            fieldIteration={fieldIteration}
            fieldMaxIterationLength={fieldMaxIterationLength}
            groupKey={groupKey}
            fieldId={fieldId}
            fielddescription={fielddescription}
            fieldpoints={fieldpoints}
            fieldCheckboxAnswerType={fieldCheckboxAnswerType}
            fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
            fieldrequired={fieldrequired}
          />
        );
      } else if (colIndex !== columnIndex) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            <Typography>{label}</Typography>
            {field?.description_status && (
              <Typography
                component="div"
                dangerouslySetInnerHTML={{ __html: field?.description || '' }}
              />
            )}
            <ReactQuill
              theme="snow"
              value={paragraphText}
              onChange={handlePragraphText}
            />
          </InactiveField>
        );
      } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            <Typography>{label}</Typography>
            {field?.description_status && (
              <Typography
                component="div"
                dangerouslySetInnerHTML={{ __html: field?.description || '' }}
              />
            )}
            <ReactQuill
              theme="snow"
              value={paragraphText}
              onChange={handlePragraphText}
            />
          </InactiveField>
        );
      } else {
        component = (
          <>
            <Typography>{label}</Typography>
            {field?.description_status && (
              <Typography
                component="div"
                dangerouslySetInnerHTML={{ __html: field?.description || '' }}
              />
            )}
            <ReactQuill
              theme="snow"
              value={paragraphText}
              onChange={handlePragraphText}
            />
          </>
        );
      }
      break;
    case 'scribble':
      if (secIndex === sectionIndex && colIndex === columnIndex) {
        component = (
          <ActiveField
            name={fieldName}
            field={field}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
            formFieldId={formFieldId}
            fieldLabelId={fieldLabelId}
            fieldOriginalId={fieldOriginalId}
            fieldIteration={fieldIteration}
            fieldMaxIterationLength={fieldMaxIterationLength}
            groupKey={groupKey}
            fieldId={fieldId}
            fielddescription={fielddescription}
            fieldpoints={fieldpoints}
            fieldCheckboxAnswerType={fieldCheckboxAnswerType}
            fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
            fieldrequired={fieldrequired}
          />
        );
      } else if (colIndex !== columnIndex) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            {label && <Typography>{label}</Typography>}
            {field?.description_status && (
              <Typography
                component="div"
                dangerouslySetInnerHTML={{ __html: field?.description || '' }}
              />
            )}
          </InactiveField>
        );
      } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            {label && <Typography>{label}</Typography>}
            {field?.description_status && (
              <Typography
                component="div"
                dangerouslySetInnerHTML={{ __html: field?.description || '' }}
              />
            )}
          </InactiveField>
        );
      } else {
        component = (
          <>
            {label && <Typography>{label}</Typography>}
            {field?.description_status && (
              <Typography
                component="div"
                dangerouslySetInnerHTML={{ __html: field?.description || '' }}
              />
            )}
          </>
        );
      }
      break;
    case 'address':
      if (field.input_type === 'country') {
        if (secIndex === sectionIndex && colIndex === columnIndex) {
          component = (
            <ActiveField
              name={fieldName}
              field={field}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
              formFieldId={formFieldId}
              fieldLabelId={fieldLabelId}
              fieldOriginalId={fieldOriginalId}
              fieldIteration={fieldIteration}
              fieldMaxIterationLength={fieldMaxIterationLength}
              groupKey={groupKey}
              fieldId={fieldId}
              fielddescription={fielddescription}
              fieldpoints={fieldpoints}
              fieldCheckboxAnswerType={fieldCheckboxAnswerType}
              fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
              fieldrequired={fieldrequired}
            />
          );
        } else if (colIndex !== columnIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormSelect
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                data={
                  field?.options || [{ label: 'Select Country', value: '' }]
                }
                containerStyles={{ width: '100%', color: '#000000' }}
              />
            </InactiveField>
          );
        } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormSelect
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                data={
                  field?.options || [{ label: 'Select Country', value: '' }]
                }
                containerStyles={{ width: '100%', color: '#000000' }}
              />
            </InactiveField>
          );
        } else {
          component = (
            <FormSelect
              name={fieldName}
              label={label}
              description={field?.description_status ? field?.description : ''}
              data={field?.options || [{ label: 'Select Country', value: '' }]}
              containerStyles={{ width: '100%', color: '#000000' }}
            />
          );
        }
      } else if (field.input_type === 'state') {
        if (secIndex === sectionIndex && colIndex === columnIndex) {
          component = (
            <ActiveField
              name={fieldName}
              field={field}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
              formFieldId={formFieldId}
              fieldLabelId={fieldLabelId}
              fieldOriginalId={fieldOriginalId}
              fieldIteration={fieldIteration}
              fieldMaxIterationLength={fieldMaxIterationLength}
              groupKey={groupKey}
              fieldId={fieldId}
              fielddescription={fielddescription}
              fieldpoints={fieldpoints}
              fieldCheckboxAnswerType={fieldCheckboxAnswerType}
              fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
              fieldrequired={fieldrequired}
            />
          );
        } else if (colIndex !== columnIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormSelect
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                data={field?.options || [{ label: 'Select State', value: '' }]}
                containerStyles={{ width: '100%', color: '#000000' }}
              />
            </InactiveField>
          );
        } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormSelect
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                data={field?.options || [{ label: 'Select State', value: '' }]}
                containerStyles={{ width: '100%', color: '#000000' }}
              />
            </InactiveField>
          );
        } else {
          component = (
            <FormSelect
              name={fieldName}
              label={label}
              description={field?.description_status ? field?.description : ''}
              data={field?.options || [{ label: 'Select State', value: '' }]}
              containerStyles={{ width: '100%', color: '#000000' }}
            />
          );
        }
      } else if (field.input_type === 'city') {
        if (secIndex === sectionIndex && colIndex === columnIndex) {
          component = (
            <ActiveField
              name={fieldName}
              field={field}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
              formFieldId={formFieldId}
              fieldLabelId={fieldLabelId}
              fieldOriginalId={fieldOriginalId}
              fieldIteration={fieldIteration}
              fieldMaxIterationLength={fieldMaxIterationLength}
              groupKey={groupKey}
              fieldId={fieldId}
              fielddescription={fielddescription}
              fieldpoints={fieldpoints}
              fieldCheckboxAnswerType={fieldCheckboxAnswerType}
              fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
              fieldrequired={fieldrequired}
            />
          );
        } else if (colIndex !== columnIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormSelect
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                data={field?.options || [{ label: 'Select City', value: '' }]}
                containerStyles={{ width: '100%', color: '#000000' }}
              />
            </InactiveField>
          );
        } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormSelect
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                data={field?.options || [{ label: 'Select City', value: '' }]}
                containerStyles={{ width: '100%', color: '#000000' }}
              />
            </InactiveField>
          );
        } else {
          component = (
            <FormSelect
              name={fieldName}
              label={label}
              description={field?.description_status ? field?.description : ''}
              data={field?.options || [{ label: 'Select City', value: '' }]}
              containerStyles={{ width: '100%', color: '#000000' }}
            />
          );
        }
      } else if (field.input_type === 'currency') {
        if (secIndex === sectionIndex && colIndex === columnIndex) {
          component = (
            <ActiveField
              name={fieldName}
              field={field}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
              formFieldId={formFieldId}
              fieldLabelId={fieldLabelId}
              fieldOriginalId={fieldOriginalId}
              fieldIteration={fieldIteration}
              fieldMaxIterationLength={fieldMaxIterationLength}
              groupKey={groupKey}
              fieldId={fieldId}
              fielddescription={fielddescription}
              fieldpoints={fieldpoints}
              fieldCheckboxAnswerType={fieldCheckboxAnswerType}
              fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
              fieldrequired={fieldrequired}
            />
          );
        } else if (colIndex !== columnIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormSelect
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                data={
                  field?.options || [{ label: 'Select Currency', value: '' }]
                }
                containerStyles={{ width: '100%', color: '#000000' }}
              />
            </InactiveField>
          );
        } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormSelect
                name={fieldName}
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                data={
                  field?.options || [{ label: 'Select Currency', value: '' }]
                }
                containerStyles={{ width: '100%', color: '#000000' }}
              />
            </InactiveField>
          );
        } else {
          component = (
            <FormSelect
              name={fieldName}
              label={label}
              description={field?.description_status ? field?.description : ''}
              data={field?.options || [{ label: 'Select Currency', value: '' }]}
              containerStyles={{ width: '100%', color: '#000000' }}
            />
          );
        }
      } else if (field.input_type === 'zipcode') {
        if (secIndex === sectionIndex && colIndex === columnIndex) {
          component = (
            <ActiveField
              name={fieldName}
              field={field}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
              formFieldId={formFieldId}
              fieldLabelId={fieldLabelId}
              fieldOriginalId={fieldOriginalId}
              fieldIteration={fieldIteration}
              fieldMaxIterationLength={fieldMaxIterationLength}
              groupKey={groupKey}
              fieldId={fieldId}
              fielddescription={fielddescription}
              fieldpoints={fieldpoints}
              fieldCheckboxAnswerType={fieldCheckboxAnswerType}
              fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
              fieldrequired={fieldrequired}
            />
          );
        } else if (colIndex !== columnIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormInput
                name={fieldName}
                type="number"
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else if (colIndex === columnIndex && secIndex !== sectionIndex) {
          component = (
            <InactiveField
              secIndex={secIndex}
              colIndex={colIndex}
              attributes={attributes}
              listeners={listeners}
              setNodeRef={setNodeRef}
              transform={transform}
              transition={transition}
            >
              <FormInput
                name={fieldName}
                type="number"
                label={label}
                description={
                  field?.description_status ? field?.description : ''
                }
                containerStyles={{
                  margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                  width: '100%'
                }}
              />
            </InactiveField>
          );
        } else {
          component = (
            <FormInput
              name={fieldName}
              type="number"
              label={label}
              description={field?.description_status ? field?.description : ''}
              containerStyles={{
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                width: {
                  xs: '100%',
                  sm: `calc(100% / 2 - ${theme.spacing(1.2)})`
                }
              }}
            />
          );
        }
      }
      break;
    default:
      if (secIndex === sectionIndex && colIndex === columnIndex) {
        component = (
          <ActiveField
            name={fieldName}
            field={field}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
            formFieldId={formFieldId}
            fieldLabelId={fieldLabelId}
            fieldOriginalId={fieldOriginalId}
            fieldIteration={fieldIteration}
            fieldMaxIterationLength={fieldMaxIterationLength}
            groupKey={groupKey}
            fieldId={fieldId}
            fielddescription={fielddescription}
            fieldpoints={fieldpoints}
            fieldCheckboxAnswerType={fieldCheckboxAnswerType}
            fieldCheckboxAnswerLimit={fieldCheckboxAnswerLimit}
            fieldrequired={fieldrequired}
          />
        );
      } else if (
        colIndex !== columnIndex ||
        (colIndex === columnIndex && secIndex !== sectionIndex)
      ) {
        component = (
          <InactiveField
            secIndex={secIndex}
            colIndex={colIndex}
            attributes={attributes}
            listeners={listeners}
            setNodeRef={setNodeRef}
            transform={transform}
            transition={transition}
          >
            <FormInput
              name={fieldName}
              type="text"
              label={label}
              description={field?.description_status ? field?.description : ''}
              containerStyles={{
                margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                width: '100%'
              }}
            />
          </InactiveField>
        );
      } else {
        component = (
          <FormInput
            name={fieldName}
            type="text"
            label={label}
            description={field?.description_status ? field?.description : ''}
            containerStyles={{
              margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
              width: {
                xs: '100%',
                sm: `calc(100% / 2 - ${theme.spacing(1.2)})`
              }
            }}
          />
        );
      }
  }
  return <>{component} </>;
};
export default RenderField;
