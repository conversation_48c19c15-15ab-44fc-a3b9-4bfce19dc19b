/* eslint-disable no-param-reassign */
/* eslint-disable no-nested-ternary */
import {
  Page,
  Document,
  Text,
  View,
  Image,
  StyleSheet
} from '@react-pdf/renderer';
import SignatureRow from './Signature';
import Checkbox from './Checkbox';

const styles = StyleSheet.create({
  body: {
    paddingTop: 35,
    paddingBottom: 65,
    paddingHorizontal: 35,
    backgroundColor: '#FFFFFF'
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    textDecoration: 'underline',
    marginLeft: 10
  },
  header: {
    textAlign: 'center',
    color: 'grey'
  },
  logoContainer: {
    marginBottom: 10,
    alignItems: 'flex-end'
  },
  logo: {
    width: 100,
    height: 30,
    marginBottom: 12
  },
  value: {
    marginLeft: 15
  },
  footer: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  footerText: {
    fontSize: 14,
    textAlign: 'center',
    color: 'black',
    fontWeight: 'bold'
  },
  page: {
    position: 'relative'
  },
  background: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0
  },
  text: {
    margin: 10,
    fontSize: 12
  },
  section: {
    marginBottom: 20
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    marginLeft: 10
  },
  field: {
    marginBottom: 4,
    flexDirection: 'row'
  },
  label: {
    fontWeight: 'bold',
    width: 200,
    marginLeft: 35
  },
  container: {
    padding: 10
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 4,
    marginTop: 8
  },
  signature: {
    width: 150,
    height: 50,
    borderWidth: 1,
    borderColor: '#ccc',
    marginTop: 8
  },
  watermark: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%) rotate(-45deg)', // Center and rotate
    fontSize: 100,
    color: 'rgba(0, 0, 0, 0.1)', // Light transparency
    textAlign: 'center'
  },
  pageNumberStyles: {
    fontSize: 11,
    color: 'black',
    textAlign: 'center'
  },
  formName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    backgroundColor: '#cee4f4',
    height: 50,
    justifyContent: 'center'
  },
  formNameText: {
    marginLeft: 10,
    marginTop: 10
  },
  dateRowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    marginRight: 130
  },
  dateText: {
    fontSize: 16,
    fontWeight: 'bold'
  }
});

const marginStyles: any = {
  Normal: {
    marginTop: 0,
    marginBottom: 0,
    marginLeft: 0,
    marginRight: 0
  },
  Narrow: {
    marginTop: 20,
    marginBottom: 20,
    marginLeft: 20,
    marginRight: 20
  },
  Wide: { marginTop: 60, marginBottom: 60, marginLeft: 60, marginRight: 60 }
};

const MyDocument = ({
  formResponses,
  pdfFormat,
  pdf,
  orgLogo,
  orgName
}: any) => {
  const renderFieldValue = (field: any, value: any, signatures: any) => {
    let normalizedType = '';
    if (field.input_type === 'checkbox') {
      normalizedType = 'checkbox';
    } else if (field.type === 'image') {
      normalizedType = 'image';
    } else if (field.type === 'signature') {
      normalizedType = 'signature';
    }
    // switch(field.input_type || field.type)
    switch (normalizedType) {
      case 'image': {
        const cleanBase64 = value?.file?.replace(
          /^data:image\/\w+;base64,/,
          ''
        );
        const changedMineType = `data:${value?.mimetype};base64,${cleanBase64}`;
        return (
          <View style={[styles.field, { marginBottom: 25 }]}>
            <Text style={styles.label}>{field.label}</Text>
            <Image src={changedMineType} style={styles.image} />
          </View>
        );
      }
      case 'signature': {
        signatures.push({
          label: field.label,
          value: value || null
        });

        if (signatures.length === 2) {
          const tempSignatures = [...signatures];
          signatures.length = 0;
          return (
            <SignatureRow
              // key={`signature-row-${fieldIndex + 1}`}
              signatures={tempSignatures}
            />
          );
        }
        // If not returning a SignatureRow, return null
        return null;
      }
      case 'checkbox': {
        return (
          <View>
            <Text style={styles.label}>{field.label}</Text>
            <View
              style={{
                flexDirection: field.label ? 'column' : 'row',
                flexWrap: field.label ? 'nowrap' : 'wrap',
                marginLeft: field.label ? '43.5%' : 29,
                gap: 5
              }}
            >
              {field.options.map((option: any, optionIndex: any) => (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'flex-start',
                    marginBottom: 18,
                    width: field.label ? 'auto' : '30%',
                    marginRight: field.label ? 0 : 10
                  }}
                  key={`${optionIndex + 1}`}
                >
                  <Checkbox checked={value?.includes(option.value)} />
                  <Text style={styles.value}>{option.label}</Text>
                </View>
              ))}
            </View>
          </View>
        );
      }
      default:
        return <Text>Image not rendered</Text>;
    }
  };

  const renderPage = () => {
    const commonStyles = {
      ...styles.body,
      color: pdf?.details?.textColor,
      fontFamily: pdf?.details?.fontFamily,
      fontSize: pdf?.details?.fontSize,
      marginTop: marginStyles[pdf?.details?.margin].marginTop,
      marginBottom: marginStyles[pdf?.details?.margin]?.marginBottom,
      marginLeft: marginStyles[pdf?.details?.margin].marginLeft,
      marginRight: marginStyles[pdf?.details?.margin].marginRight,
      padding: 10,
      width: '100%'
    };
    switch (pdfFormat) {
      case 'single':
        if (
          Array.isArray(formResponses) &&
          formResponses.length > 0 &&
          pdf?.details?.pageBreaks === 'TextWrap'
        ) {
          return formResponses.map((formResponse: any, index: any) => {
            const formData = formResponse?.form;
            const formResponseValues = formResponse?.values;

            return (
              // <Page
              //   key={formResponse?.form_value_id || index}
              //   style={commonStyles}
              //   orientation={pdf?.details?.orientation.toLowerCase()}
              //   size={pdf?.details?.size}
              //   // wrap={pdf?.details?.pageBreaks === 'TextWrap'}
              //   wrap={false}
              // >
              //   <View
              //     style={{
              //       ...styles.background,
              //       backgroundColor: pdf?.details?.backgroundColor || '#FFFFFF'
              //     }}
              //   />
              //   <View
              //     style={[
              //       styles.header,
              //       {
              //         height: pdf?.details?.headerHeight,
              //         flexDirection: 'row',
              //         alignItems: 'center', // Aligns items vertically in the center
              //         paddingHorizontal: 10 // Adds spacing on both sides
              //       }
              //     ]}
              //     fixed
              //   >
              //     <Text
              //       fixed
              //       style={{
              //         fontSize: 18,
              //         fontWeight: 'bold',
              //         color: 'black',
              //         marginBottom: 10
              //       }}
              //     >
              //       {pdf?.details?.headerTitle}
              //     </Text>
              //   </View>

              //   {pdf?.details?.watermarkText && (
              //     <Text style={[styles.watermark]} fixed>
              //       {pdf?.details?.watermarkText}
              //     </Text>
              //   )}

              //   <View style={styles.container}>
              //     <View style={styles.formName}>
              //       <Text style={styles.formNameText}>
              //         {formResponse?.form?.name}
              //       </Text>
              //     </View>

              //     {/* Iterate through form sections */}
              //     {Object.values(formData?.fields).map(
              //       (sectionDetails: any, sectionIndex: any) => {
              //         const signatures: any[] = [];

              //         // Handle iterative sections (i.e., repeating groups of fields)
              //         if (sectionDetails?.is_iterative_or_not) {
              //           const sectionValues =
              //             formResponseValues?.[sectionDetails.group_key];

              //           // Check if data exists and is an array
              //           if (
              //             sectionValues &&
              //             Array.isArray(sectionValues) &&
              //             sectionValues.length > 0
              //           ) {
              //             return sectionValues.map(
              //               (
              //                 sectionValuesData: any,
              //                 sectionValueIndex: any
              //               ) => {
              //                 const sectionKey = `${sectionDetails.group_key}-${sectionValueIndex}`;
              //                 return (
              //                   <View
              //                     style={[styles.section, { marginBottom: 20 }]}
              //                     key={sectionKey}
              //                   >
              //                     {/* Section title with index number */}
              //                     <Text style={styles.sectionTitle}>
              //                       {`${sectionDetails.group_title} #${sectionValueIndex + 1}`}
              //                     </Text>

              //                     {/* Render fields for each iteration */}
              //                     {sectionDetails.fields.map(
              //                       (field: any, fieldIndex: any) => {
              //                         const value =
              //                           sectionValuesData?.[field.name] ||
              //                           'N/A';

              //                         if (field.type === 'image') {
              //                           return renderFieldValue(
              //                             field,
              //                             value,
              //                             ''
              //                           );
              //                         }

              //                         // Render signature fields in pairs
              //                         if (field.type === 'signature') {
              //                           return renderFieldValue(
              //                             field,
              //                             value,
              //                             signatures
              //                           );
              //                         }

              //                         // Render checkbox options
              //                         if (field.input_type === 'checkbox') {
              //                           return renderFieldValue(
              //                             field,
              //                             value,
              //                             ''
              //                           );
              //                         }

              //                         // Render other field types
              //                         return (
              //                           <View
              //                             style={[
              //                               styles.field,
              //                               { marginBottom: 25 }
              //                             ]}
              //                             key={`${fieldIndex + 1}`}
              //                           >
              //                             <Text style={styles.label}>
              //                               {field.label}
              //                             </Text>

              //                             <Text style={styles.value}>
              //                               {value}
              //                             </Text>
              //                           </View>
              //                         );
              //                       }
              //                     )}

              //                     {/* Render any remaining unpaired signature */}
              //                     {signatures.length > 0 && (
              //                       <SignatureRow signatures={signatures} />
              //                     )}
              //                     <View style={styles.dateRowContainer}>
              //                       {Object.entries(
              //                         sectionValuesData || {}
              //                       ).map(([key, val]: any) => {
              //                         const isDateField = key.endsWith('_date');
              //                         const dateOnly =
              //                           typeof val === 'string'
              //                             ? val.slice(0, 10)
              //                             : val;
              //                         const isAlreadyRendered =
              //                           sectionDetails.fields.some(
              //                             (field: any) => field.name === key
              //                           );

              //                         if (isDateField && !isAlreadyRendered) {
              //                           return (
              //                             <View
              //                               style={styles.dateText}
              //                               key={key}
              //                             >
              //                               <Text>Date:{dateOnly}</Text>
              //                             </View>
              //                           );
              //                         }
              //                         return null;
              //                       })}
              //                     </View>
              //                   </View>
              //                 );
              //               }
              //             );
              //           }

              //           // No data available for iterative section
              //           return null;
              //         }

              //         // Handle non-iterative (single instance) sections
              //         return (
              //           <View
              //             style={[styles.section, { marginBottom: 20 }]}
              //             key={`${sectionIndex + 1}`}
              //           >
              //             {/* Section title without index */}
              //             <Text style={styles.sectionTitle}>
              //               {sectionDetails.group_title}
              //             </Text>

              //             {/* Render fields */}
              //             {sectionDetails.fields.map(
              //               (field: any, fieldIndex: any) => {
              //                 const value =
              //                   formResponseValues?.[
              //                     sectionDetails.group_key
              //                   ]?.[field.name] || 'N/A';

              //                 if (field.type === 'image') {
              //                   return renderFieldValue(field, value, '');
              //                 }

              //                 // Render signature fields in pairs
              //                 if (field.type === 'signature') {
              //                   return renderFieldValue(
              //                     field,
              //                     value,
              //                     signatures
              //                   );
              //                 }

              //                 // Render checkbox options
              //                 if (field.input_type === 'checkbox') {
              //                   return renderFieldValue(field, value, '');
              //                 }

              //                 // Render other field types
              //                 return (
              //                   <View
              //                     style={[styles.field, { marginBottom: 25 }]}
              //                     key={`${fieldIndex + 1}`}
              //                   >
              //                     <Text style={styles.label}>
              //                       {field.label}
              //                     </Text>

              //                     <Text style={styles.value}>{value}</Text>
              //                   </View>
              //                 );
              //               }
              //             )}

              //             {/* Render any remaining unpaired signature */}
              //             {signatures.length > 0 && (
              //               <SignatureRow signatures={signatures} />
              //             )}
              //             <View style={styles.dateRowContainer}>
              //               {Object.entries(
              //                 formResponseValues?.[sectionDetails.group_key] ||
              //                   {}
              //               ).map(([key, val]: any) => {
              //                 // Only render if key ends with _date and is not part of defined fields
              //                 const isDateField = key.endsWith('_date');
              //                 const dateOnly =
              //                   typeof val === 'string'
              //                     ? val.slice(0, 10)
              //                     : val;
              //                 const isAlreadyRendered =
              //                   sectionDetails.fields.some(
              //                     (field: any) => field.name === key
              //                   );

              //                 if (isDateField && !isAlreadyRendered) {
              //                   return (
              //                     <View style={styles.dateText} key={key}>
              //                       <Text>Date:{dateOnly}</Text>
              //                     </View>
              //                   );
              //                 }
              //                 return null;
              //               })}
              //             </View>
              //           </View>
              //         );
              //       }
              //     )}
              //   </View>

              //   <View
              //     style={[
              //       styles.footer,
              //       {
              //         height: pdf?.details?.footerHeight,
              //         flexDirection: 'row',
              //         justifyContent: 'center',
              //         alignItems: 'center'
              //       }
              //     ]}
              //     fixed
              //   >
              //     {pdf?.details?.footerText && (
              //       <Text style={[styles.footerText, { marginRight: 0 }]}>
              //         {typeof pdf.details.footerText === 'string' &&
              //         pdf.details.footerText.length > 50
              //           ? `${pdf.details.footerText.slice(0, 50)}...`
              //           : pdf.details.footerText}
              //       </Text>
              //     )}
              //     <Text
              //       style={[styles.pageNumberStyles]}
              //       render={({ pageNumber, totalPages }) =>
              //         `${pdf?.details?.footerText ? ' - ' : ''}Page ${pageNumber} of ${totalPages}`
              //       }
              //     />
              //   </View>
              // </Page>
              <Page
                key={formResponse?.form_value_id || index}
                style={commonStyles}
                orientation={pdf?.details?.orientation.toLowerCase()}
                size={pdf?.details?.size}
                wrap
              >
                <View
                  style={[
                    styles.header,
                    {
                      height: pdf?.details?.headerHeight,
                      flexDirection: 'row',
                      alignItems: 'center', // Aligns items vertically in the center
                      paddingHorizontal: 10 // Adds spacing on both sides
                    }
                  ]}
                  fixed
                >
                  {orgLogo && (
                    <Image
                      src={orgLogo}
                      style={[styles.logo, { width: 50, height: 50 }]}
                    />
                  )}
                  <Text
                    fixed
                    style={{
                      fontSize: 18,
                      fontWeight: 'bold',
                      color: 'black',
                      marginBottom: 10
                    }}
                  >
                    {pdf?.details?.headerTitle || orgName}
                  </Text>
                </View>

                <View
                  style={{
                    height: 2,
                    backgroundColor: 'black',
                    width: '100%',
                    marginTop: 5,
                    marginBottom: 10
                  }}
                />

                {pdf?.details?.watermarkText && (
                  <Text style={[styles.watermark]} fixed>
                    {pdf?.details?.watermarkText}
                  </Text>
                )}

                <View style={styles.container}>
                  <View style={styles.formName}>
                    <Text style={styles.formNameText}>
                      {formResponse?.form?.name}
                    </Text>
                  </View>
                  {/* Iterate through form sections */}
                  {Object.values(formData?.fields).map(
                    (sectionDetails: any, sectionIndex: number) => {
                      const signatures: any[] = [];

                      if (sectionDetails?.is_iterative_or_not) {
                        const sectionValues =
                          formResponseValues?.[sectionDetails.group_key];

                        if (
                          sectionValues &&
                          Array.isArray(sectionValues) &&
                          sectionValues.length > 0
                        ) {
                          return sectionValues.map(
                            (
                              sectionValuesData: any,
                              sectionValueIndex: number
                            ) => {
                              const sectionKey = `${sectionDetails.group_key}-${sectionValueIndex}`;
                              return (
                                <View style={[styles.section]} key={sectionKey}>
                                  <Text style={styles.sectionTitle}>
                                    {`${sectionDetails.group_title} #${sectionValueIndex + 1}`}
                                  </Text>

                                  {sectionDetails.fields.map(
                                    (field: any, fieldIndex: number) => {
                                      const value =
                                        sectionValuesData?.[field.name] ||
                                        'N/A';

                                      if (field.type === 'image') {
                                        return renderFieldValue(
                                          field,
                                          value,
                                          ''
                                        );
                                      }

                                      if (field.type === 'signature') {
                                        return renderFieldValue(
                                          field,
                                          value,
                                          signatures
                                        );
                                      }

                                      if (field.input_type === 'checkbox') {
                                        return renderFieldValue(
                                          field,
                                          value,
                                          ''
                                        );
                                      }
                                      // Render other field types
                                      return (
                                        <View
                                          style={[
                                            styles.field,
                                            { marginBottom: 25 }
                                          ]}
                                          key={`${fieldIndex + 1}`}
                                        >
                                          <Text style={styles.label}>
                                            {field.label}
                                          </Text>

                                          <Text style={styles.value}>
                                            {value}
                                          </Text>
                                        </View>
                                      );
                                    }
                                  )}

                                  {/* Render any remaining unpaired signature */}
                                  {signatures.length > 0 && (
                                    <View
                                      wrap={false}
                                      style={{ marginBottom: 25 }}
                                    >
                                      <SignatureRow signatures={signatures} />
                                    </View>
                                  )}

                                  {/* Render any remaining unpaired signature */}
                                  <View style={styles.dateRowContainer}>
                                    {Object.entries(
                                      formResponseValues?.[
                                        sectionDetails.group_key
                                      ] || {}
                                    ).map(([key, val]: any) => {
                                      // Only render if key ends with _date and is not part of defined fields
                                      const isDateField = key.endsWith('_date');
                                      const dateOnly =
                                        typeof val === 'string'
                                          ? val.slice(0, 10)
                                          : val;
                                      const isAlreadyRendered =
                                        sectionDetails.fields.some(
                                          (field: any) => field.name === key
                                        );

                                      if (isDateField && !isAlreadyRendered) {
                                        return (
                                          <View
                                            style={styles.dateText}
                                            key={key}
                                          >
                                            <Text>Date:{dateOnly}</Text>
                                          </View>
                                        );
                                      }
                                      return null;
                                    })}
                                  </View>
                                </View>
                              );
                            }
                          );
                        }
                      }
                      // Handle non-iterative (single instance) sections
                      return (
                        <View
                          style={[styles.section]}
                          key={`${sectionIndex + 1}`}
                        >
                          {/* Section title without index */}
                          <Text style={styles.sectionTitle}>
                            {sectionDetails.group_title}
                          </Text>

                          {/* Render fields */}
                          {sectionDetails.fields.map(
                            (field: any, fieldIndex: any) => {
                              const value =
                                formResponseValues?.[
                                  sectionDetails.group_key
                                ]?.[field.name] || 'N/A';

                              if (field.type === 'image') {
                                return renderFieldValue(field, value, '');
                              }
                              if (field.type === 'signature') {
                                return renderFieldValue(
                                  field,
                                  value,
                                  signatures
                                );
                              }

                              if (field.input_type === 'checkbox') {
                                return renderFieldValue(field, value, '');
                              }

                              // Render other field types
                              return (
                                <View
                                  style={[styles.field, { marginBottom: 25 }]}
                                  key={`${fieldIndex + 1}`}
                                >
                                  <Text style={styles.label}>
                                    {field.label}
                                  </Text>

                                  <Text style={styles.value}>{value}</Text>
                                </View>
                              );
                            }
                          )}

                          {/* Render any remaining unpaired signature */}
                          {signatures.length > 0 && (
                            <View wrap={false} style={{ marginBottom: 25 }}>
                              <SignatureRow signatures={signatures} />
                            </View>
                          )}

                          {/* Render extra _date fields */}
                          <View style={styles.dateRowContainer}>
                            {Object.entries(
                              formResponseValues?.[sectionDetails.group_key] ||
                                {}
                            ).map(([key, val]: any) => {
                              // Only render if key ends with _date and is not part of defined fields
                              const isDateField = key.endsWith('_date');
                              const dateOnly =
                                typeof val === 'string'
                                  ? val.slice(0, 10)
                                  : val;
                              const isAlreadyRendered =
                                sectionDetails.fields.some(
                                  (field: any) => field.name === key
                                );

                              if (isDateField && !isAlreadyRendered) {
                                return (
                                  <View style={styles.dateText} key={key}>
                                    <Text>Date:{dateOnly}</Text>
                                  </View>
                                );
                              }
                              return null;
                            })}
                          </View>
                        </View>
                      );
                    }
                  )}
                </View>
                <View
                  style={[
                    styles.footer,
                    {
                      position: 'absolute',
                      bottom: 0,
                      left: 0,
                      right: 0,
                      height: pdf?.details?.footerHeight,
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      paddingHorizontal: 10,
                      paddingVertical: 5
                    }
                  ]}
                  fixed
                >
                  {pdf?.details?.footerText && (
                    <Text style={[styles.footerText, { marginRight: 0 }]}>
                      {typeof pdf.details.footerText === 'string' &&
                      pdf.details.footerText.length > 50
                        ? `${pdf.details.footerText.slice(0, 50)}...`
                        : pdf.details.footerText}
                    </Text>
                  )}
                  <Text
                    style={[styles.pageNumberStyles]}
                    render={({ pageNumber, totalPages }) =>
                      `${pdf?.details?.footerText ? ' - ' : ''}Page ${pageNumber} of ${totalPages}`
                    }
                  />
                </View>
              </Page>
            );
          });
        }
        return (
          <Page
            style={commonStyles}
            orientation={pdf?.details?.orientation.toLowerCase()}
            size={pdf?.details?.size}
            wrap={pdf?.details?.pageBreaks === 'TextWrap'}
          >
            <View
              style={[
                styles.header,
                {
                  height: pdf?.details?.headerHeight,
                  flexDirection: 'row',
                  alignItems: 'center', // Aligns items vertically in the center
                  paddingHorizontal: 10 // Adds spacing on both sides
                }
              ]}
              fixed
            >
              {orgLogo && (
                <Image
                  src={orgLogo}
                  style={[styles.logo, { width: 50, height: 50 }]}
                />
              )}
              <Text
                fixed
                style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: 'black',
                  marginBottom: 10
                }}
              >
                {pdf?.details?.headerTitle || orgName}
              </Text>
            </View>

            {pdf?.details?.watermarkText && (
              <Text style={[styles.watermark]} fixed>
                {pdf?.details?.watermarkText}
              </Text>
            )}
            <View
              style={{
                height: 2,
                backgroundColor: 'black',
                width: '100%',
                marginTop: 5,
                marginBottom: 10
              }}
            />

            {
              /* working lables */
              <View style={styles.container}>
                {Array.isArray(formResponses) &&
                  formResponses?.map((formResponse: any, index: any) => {
                    const formData = formResponse?.form;
                    const formResponseValues = formResponse?.values;
                    return (
                      <View key={formData.id || index}>
                        <View style={styles.formName}>
                          <Text style={styles.formNameText}>
                            {formResponse?.form?.name}
                          </Text>
                        </View>

                        {/* Iterate through form sections */}
                        {Object.values(formData?.fields).map(
                          (sectionDetails: any, sectionIndex: any) => {
                            const signatures: any[] = [];

                            // Handle iterative sections (i.e., repeating groups of fields)
                            if (sectionDetails?.is_iterative_or_not) {
                              const sectionValues =
                                formResponseValues?.[sectionDetails.group_key];

                              // Check if data exists and is an array
                              if (
                                sectionValues &&
                                Array.isArray(sectionValues) &&
                                sectionValues.length > 0
                              ) {
                                return sectionValues.map(
                                  (
                                    sectionValuesData: any,
                                    sectionValueIndex: any
                                  ) => {
                                    const sectionKey = `${sectionDetails.group_key}-${sectionValueIndex}`;
                                    return (
                                      <View
                                        style={[
                                          styles.section,
                                          { marginBottom: 20 }
                                        ]}
                                        key={sectionKey}
                                      >
                                        {/* Section title with index number */}
                                        <Text style={styles.sectionTitle}>
                                          {`${sectionDetails.group_title} #${sectionValueIndex + 1}`}
                                        </Text>

                                        {/* Render fields for each iteration */}
                                        {sectionDetails.fields.map(
                                          (field: any, fieldIndex: any) => {
                                            const value =
                                              sectionValuesData?.[field.name] ||
                                              'N/A';

                                            if (field.type === 'image') {
                                              return renderFieldValue(
                                                field,
                                                value,
                                                ''
                                              );
                                            }

                                            // Render signature fields in pairs
                                            if (field.type === 'signature') {
                                              return renderFieldValue(
                                                field,
                                                value,
                                                signatures
                                              );
                                            }

                                            // Render checkbox options
                                            if (
                                              field.input_type === 'checkbox'
                                            ) {
                                              return renderFieldValue(
                                                field,
                                                value,
                                                ''
                                              );
                                            }

                                            // Render other field types
                                            return (
                                              <View
                                                style={[
                                                  styles.field,
                                                  { marginBottom: 25 }
                                                ]}
                                                key={`${fieldIndex + 1}`}
                                              >
                                                <Text style={styles.label}>
                                                  {field.label}
                                                </Text>

                                                <Text style={styles.value}>
                                                  {value}
                                                </Text>
                                              </View>
                                            );
                                          }
                                        )}

                                        {/* Render any remaining unpaired signature */}
                                        {signatures.length > 0 && (
                                          <SignatureRow
                                            signatures={signatures}
                                          />
                                        )}
                                        {/* Render extra _date fields */}
                                        <View style={styles.dateRowContainer}>
                                          {Object.entries(
                                            sectionValuesData || {}
                                          ).map(([key, val]: any) => {
                                            const isDateField =
                                              key.endsWith('_date');
                                            const dateOnly =
                                              typeof val === 'string'
                                                ? val.slice(0, 10)
                                                : val;
                                            const isAlreadyRendered =
                                              sectionDetails.fields.some(
                                                (field: any) =>
                                                  field.name === key
                                              );

                                            if (
                                              isDateField &&
                                              !isAlreadyRendered
                                            ) {
                                              return (
                                                <View
                                                  style={styles.dateText}
                                                  key={key}
                                                >
                                                  <Text>Date:{dateOnly}</Text>
                                                </View>
                                              );
                                            }
                                            return null;
                                          })}
                                        </View>
                                      </View>
                                    );
                                  }
                                );
                              }

                              // No data available for iterative section
                              return null;
                            }

                            // Handle non-iterative (single instance) sections
                            return (
                              <View
                                style={[styles.section, { marginBottom: 20 }]}
                                key={`${sectionIndex + 1}`}
                              >
                                {/* Section title without index */}
                                <Text style={styles.sectionTitle}>
                                  {sectionDetails.group_title}
                                </Text>

                                {/* Render fields */}
                                {sectionDetails.fields.map(
                                  (field: any, fieldIndex: any) => {
                                    const value =
                                      formResponseValues?.[
                                        sectionDetails.group_key
                                      ]?.[field.name] || 'N/A';

                                    if (field.type === 'image') {
                                      return renderFieldValue(field, value, '');
                                    }

                                    // Render signature fields in pairs
                                    if (field.type === 'signature') {
                                      return renderFieldValue(
                                        field,
                                        value,
                                        signatures
                                      );
                                    }

                                    // Render checkbox options
                                    if (field.input_type === 'checkbox') {
                                      return renderFieldValue(field, value, '');
                                    }

                                    // Render other field types
                                    return (
                                      <View
                                        style={[
                                          styles.field,
                                          { marginBottom: 25 }
                                        ]}
                                        key={`${fieldIndex + 1}`}
                                      >
                                        <Text style={styles.label}>
                                          {field.label}
                                        </Text>

                                        <Text style={styles.value}>
                                          {value}
                                        </Text>
                                      </View>
                                    );
                                  }
                                )}

                                {/* Render any remaining unpaired signature */}
                                {signatures.length > 0 && (
                                  <SignatureRow signatures={signatures} />
                                )}
                                {/* Render extra _date fields */}
                                <View style={styles.dateRowContainer}>
                                  {Object.entries(
                                    formResponseValues?.[
                                      sectionDetails.group_key
                                    ] || {}
                                  ).map(([key, val]: any) => {
                                    // Only render if key ends with _date and is not part of defined fields
                                    const isDateField = key.endsWith('_date');
                                    const dateOnly =
                                      typeof val === 'string'
                                        ? val.slice(0, 10)
                                        : val;
                                    const isAlreadyRendered =
                                      sectionDetails.fields.some(
                                        (field: any) => field.name === key
                                      );

                                    if (isDateField && !isAlreadyRendered) {
                                      return (
                                        <View style={styles.dateText} key={key}>
                                          <Text>Date:{dateOnly}</Text>
                                        </View>
                                      );
                                    }
                                    return null;
                                  })}
                                </View>
                              </View>
                            );
                          }
                        )}
                      </View>
                    );
                  })}
              </View>
            }
            <View
              style={[
                styles.footer,
                {
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  height: pdf?.details?.footerHeight,
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  paddingHorizontal: 10,
                  paddingVertical: 5
                }
              ]}
              fixed
            >
              {pdf?.details?.footerText && (
                <Text style={[styles.footerText, { marginRight: 0 }]}>
                  {typeof pdf.details.footerText === 'string' &&
                  pdf.details.footerText.length > 50
                    ? `${pdf.details.footerText.slice(0, 50)}...`
                    : pdf.details.footerText}
                </Text>
              )}
              <Text
                style={[styles.pageNumberStyles]}
                render={({ pageNumber, totalPages }) =>
                  `${pdf?.details?.footerText ? ' - ' : ''}Page ${pageNumber} of ${totalPages}`
                }
              />
            </View>
          </Page>
        );

      default:
        return (
          <Page
            style={commonStyles}
            orientation={pdf?.details?.orientation.toLowerCase()}
            size={pdf?.details?.size}
            // wrap={pdf?.details?.pageBreaks === 'TextWrap'}
            wrap={false}
          >
            <View
              style={[
                styles.header,
                {
                  height: pdf?.details?.headerHeight,
                  flexDirection: 'row',
                  alignItems: 'center', // Aligns items vertically in the center
                  paddingHorizontal: 10 // Adds spacing on both sides
                }
              ]}
              fixed
            >
              {orgLogo && (
                <Image
                  src={orgLogo}
                  style={[styles.logo, { width: 50, height: 50 }]}
                />
              )}
              <Text
                fixed
                style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: 'black',
                  marginBottom: 10
                }}
              >
                {pdf?.details?.headerTitle || orgName}
              </Text>
            </View>

            {pdf?.details?.watermarkText && (
              <Text style={[styles.watermark]} fixed>
                {pdf?.details?.watermarkText}
              </Text>
            )}
            <View
              style={{
                height: 2,
                backgroundColor: 'black',
                width: '100%',
                marginTop: 5,
                marginBottom: 10
              }}
            />

            <View style={styles.formName}>
              <Text style={styles.formNameText}>
                {formResponses?.form?.name}
              </Text>
            </View>

            <View>
              <View style={styles.container}>
                {formResponses?.form?.fields &&
                  Object.values(formResponses?.form?.fields).map(
                    (sectionDetails: any, sectionIndex: any) => {
                      // Handle iterative sections
                      if (sectionDetails?.is_iterative_or_not) {
                        const sectionValues =
                          formResponses?.values[sectionDetails.group_key];

                        if (
                          Array.isArray(sectionValues) &&
                          sectionValues.length > 0
                        ) {
                          return sectionValues.map(
                            (
                              sectionValuesData: any,
                              sectionValueIndex: any
                            ) => {
                              const sectionKey = `${sectionDetails.group_key}-${sectionValueIndex}`;
                              const signatures: any[] = []; // signatures array moved *inside* the map!

                              return (
                                <View
                                  style={[styles.section, { marginBottom: 20 }]}
                                  key={sectionKey}
                                >
                                  <Text style={styles.sectionTitle}>
                                    {`${sectionDetails.group_title} #${
                                      sectionValueIndex + 1
                                    }`}
                                  </Text>

                                  {sectionDetails.fields.map(
                                    (field: any, fieldIndex: any) => {
                                      const value =
                                        sectionValuesData?.[field.name] ||
                                        'N/A';

                                      if (field.type === 'image') {
                                        return renderFieldValue(
                                          field,
                                          value,
                                          ''
                                        );
                                      }

                                      // Signature fields (render in pairs)
                                      if (field.type === 'signature') {
                                        return renderFieldValue(
                                          field,
                                          value,
                                          signatures
                                        );
                                      }

                                      // Checkbox fields
                                      if (field.input_type === 'checkbox') {
                                        return renderFieldValue(
                                          field,
                                          value,
                                          ''
                                        );
                                      }

                                      // Other field types
                                      return (
                                        <View
                                          style={[
                                            styles.field,
                                            { marginBottom: 25 }
                                          ]}
                                          key={`${fieldIndex + 1}`}
                                        >
                                          <Text style={styles.label}>
                                            {field.label}
                                          </Text>

                                          <Text style={styles.value}>
                                            {value}
                                          </Text>
                                        </View>
                                      );
                                    }
                                  )}

                                  {/* After mapping fields, render any leftover 1 signature */}
                                  {signatures.length > 0 && (
                                    <SignatureRow signatures={signatures} />
                                  )}
                                  <View style={styles.dateRowContainer}>
                                    {Object.entries(
                                      sectionValuesData || {}
                                    ).map(([key, val]: any) => {
                                      const isDateField = key.endsWith('_date');
                                      const dateOnly =
                                        typeof val === 'string'
                                          ? val.slice(0, 10)
                                          : val;
                                      const isAlreadyRendered =
                                        sectionDetails.fields.some(
                                          (field: any) => field.name === key
                                        );

                                      if (isDateField && !isAlreadyRendered) {
                                        return (
                                          <View
                                            style={styles.dateText}
                                            key={key}
                                          >
                                            <Text>Date:{dateOnly}</Text>
                                          </View>
                                        );
                                      }
                                      return null;
                                    })}
                                  </View>
                                </View>
                              );
                            }
                          );
                        }

                        return null; // No data for iterative section
                      }

                      // Non-iterative sections
                      const signatures: any[] = []; // Signatures array here
                      return (
                        <View
                          style={[styles.section, { marginBottom: 20 }]}
                          key={`${sectionIndex + 1}`}
                        >
                          <Text style={styles.sectionTitle}>
                            {sectionDetails.group_title}
                          </Text>

                          {sectionDetails.fields.map(
                            (field: any, fieldIndex: any) => {
                              const value =
                                formResponses?.values[
                                  sectionDetails.group_key
                                ]?.[field.name] || 'N/A';

                              if (field.type === 'image') {
                                return renderFieldValue(field, value, '');
                              }

                              // Signature fields (render in pairs)
                              if (field.type === 'signature') {
                                return renderFieldValue(
                                  field,
                                  value,
                                  signatures
                                );
                              }

                              // Checkbox fields
                              if (field.input_type === 'checkbox') {
                                return renderFieldValue(field, value, '');
                              }

                              // Other field types
                              return (
                                <View
                                  style={[styles.field, { marginBottom: 25 }]}
                                  key={`${fieldIndex + 1}`}
                                >
                                  <Text style={styles.label}>
                                    {field.label}
                                  </Text>
                                  <Text style={styles.value}>{value}</Text>
                                </View>
                              );
                            }
                          )}

                          {/* After mapping fields, render any leftover 1 signature */}
                          {signatures.length > 0 && (
                            <SignatureRow signatures={signatures} />
                          )}
                          <View style={styles.dateRowContainer}>
                            {Object.entries(
                              formResponses?.values[sectionDetails.group_key] ||
                                {}
                            ).map(([key, val]: any) => {
                              // Only render if key ends with _date and is not part of defined fields
                              const isDateField = key.endsWith('_date');
                              const dateOnly =
                                typeof val === 'string'
                                  ? val.slice(0, 10)
                                  : val;
                              const isAlreadyRendered =
                                sectionDetails.fields.some(
                                  (field: any) => field.name === key
                                );

                              if (isDateField && !isAlreadyRendered) {
                                return (
                                  <View style={styles.dateText} key={key}>
                                    <Text>Date:{dateOnly}</Text>
                                  </View>
                                );
                              }
                              return null;
                            })}
                          </View>
                        </View>
                      );
                    }
                  )}
              </View>
            </View>
            <View
              style={[
                styles.footer,
                {
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  height: pdf?.details?.footerHeight,
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  paddingHorizontal: 10,
                  paddingVertical: 5
                }
              ]}
              fixed
            >
              {pdf?.details?.footerText && (
                <Text style={[styles.footerText, { marginRight: 0 }]}>
                  {typeof pdf.details.footerText === 'string' &&
                  pdf.details.footerText.length > 50
                    ? `${pdf.details.footerText.slice(0, 50)}...`
                    : pdf.details.footerText}
                </Text>
              )}
              <Text
                style={[styles.pageNumberStyles]}
                render={({ pageNumber, totalPages }) =>
                  `${pdf?.details?.footerText ? ' - ' : ''}Page ${pageNumber} of ${totalPages}`
                }
              />
            </View>
          </Page>
        );
    }
  };

  return <Document>{renderPage()}</Document>;
};
export default MyDocument;
