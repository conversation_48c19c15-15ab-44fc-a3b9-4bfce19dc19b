import {
  LoaderFunctionArgs,
  RouteObject,
  createBrowserRouter,
  redirect
} from 'react-router-dom';

import { Route } from './types';
import { getStorageItem } from './utils';
import Layout from './components/layout/layOut';
import AsyncPage from './components/route/AsyncPage';
import { Loader } from './components/form.elements';

const mapRoutes = (routes: Route[]): RouteObject[] => {
  return routes.map((routeItem) => {
    const mappedRoute: RouteObject = {
      index: routeItem.index || false,
      path: routeItem.index ? undefined : routeItem.path,
      element: <AsyncPage page={routeItem.page} />
    };

    if (routeItem.children) {
      mappedRoute.children = mapRoutes(routeItem.children);
    }
    return mappedRoute;
  });
};

const privateRoutes: Route[] = [
  {
    index: true,
    path: '/',
    page: 'dashboard'
  },
  {
    index: false,
    path: '/dashboard',
    page: 'dashboard'
  },
  {
    path: '/apps',
    page: 'apps'
  },
  {
    path: '/apps/app-registration',
    page: 'apps/app-registration'
  },
  {
    path: '/apps/edit-app-registration/:id',
    page: 'apps/app-registration'
  },
  {
    path: '/apps/app-details/:id',
    page: 'apps/app-details'
  },
  {
    path: '/apps/app-details/sub-forms/:appId/:formId',
    page: 'apps/sub-forms'
  },
  {
    path: '/apps/form-builder/edit-form/:id',
    page: 'form-builder/create-form'
  },
  {
    path: '/apps/dashboard-configuration/:appId',
    page: 'apps/dashboard-configuration'
  },
  {
    path: '/form-builder/preview-form/:id',
    page: 'form-builder/preview-form'
  },
  {
    path: '/add-section-template',
    page: 'form-builder/suggestions/add-section-template'
  },
  {
    path: '/edit-section-template/:id',
    page: 'form-builder/suggestions/add-section-template'
  },
  {
    path: '/org-setup',
    page: 'org-setup'
  },
  {
    path: '/org-setup/organization-registration',
    page: 'org-setup/organization-registration'
  },
  {
    path: '/org-setup/organization-registration/:id',
    page: 'org-setup/organization-registration'
  },
  {
    path: '/org-setup/organization-profile/:id',
    page: 'org-setup/organization-profile'
  },
  {
    path: '/org-setup/organization-profile/app-forms/:id',
    page: 'org-setup/app-forms'
  },
  {
    path: '/users',
    page: 'users'
  },
  {
    path: '/users/user-registration',
    page: 'users/user-registration'
  },
  {
    path: '/users/update-user/:id',
    page: 'users/user-registration'
  },
  {
    path: '/users/employee-onboarding',
    page: 'users/employee-onboarding'
  },
  {
    path: '/users/employee-onboarding/checklist/:id',
    page: 'users/employee-onboarding/checklist'
  },
  {
    path: '/users/employee-onboarding/create-checklist',
    page: 'users/employee-onboarding/create-checklist'
  },
  {
    path: '/users/employee-onboarding/documents/:id',
    page: 'users/employee-onboarding/documents'
  },
  {
    path: '/users/employee-onboarding/applicant-details/:id',
    page: 'users/employee-onboarding/form-display'
  },
  {
    path: '/users/employee-onboarding/create-applicant',
    page: 'users/employee-onboarding/create-applicant'
  },
  {
    path: '/users/employee-onboarding/create-onboarding-process',
    page: 'users/employee-onboarding/create-onboarding-process'
  },
  {
    path: '/users/employee-onboarding/create-job-post',
    page: 'users/employee-onboarding/create-job-post'
  },
  {
    path: '/users/employee-onboarding/edit-job-post/:id',
    page: 'users/employee-onboarding/create-job-post'
  },
  {
    path: '/users/employee-onboarding/create-stages',
    page: 'users/employee-onboarding/create-stages'
  },
  {
    path: '/users/employee-onboarding/edit-stage/:id',
    page: 'users/employee-onboarding/create-stages'
  },
  {
    path: '/users/employee-onboarding/stages-list',
    page: 'users/employee-onboarding/stages-list'
  },
  {
    path: '/users/employee-onboarding/edit-applicant/:id',
    page: 'users/employee-onboarding/create-applicant'
  },
  {
    path: '/configurations/address-configuration',
    page: 'configurations/address-configuration'
  },
  {
    path: '/configurations/storage-configuration',
    page: 'configurations/storage-configuration'
  },
  {
    path: '/configurations/email-configuration',
    page: 'configurations/email-configuration'
  },
  {
    path: '/configurations/pdf-configuration',
    page: 'configurations/pdf-configuration'
  },
  {
    path: '/configurations/view-pdf/:clientId/:appId',
    page: 'configurations/view-pdf'
  },
  {
    path: '/users/client-assessment',
    page: 'users/client-assessment'
  },
  {
    path: '/users/caregivers',
    page: 'users/caregivers'
  },
  {
    path: '/users/caregivers/:id',
    page: 'users/caregivers/details'
  },
  {
    path: '/users/caregivers/create-update/:id?',
    page: 'users/caregivers/create-update'
  },
  {
    path: '/users/client-assessment/form-values/:id/:appId',
    page: 'users/client-assessment/form-display'
  },
  {
    path: '/users/client-assessment/view-pdf-form/:clientId/:appId',
    page: 'users/client-assessment/view-pdf-form'
  },
  {
    path: '/form-builder',
    page: 'form-builder'
  },
  {
    path: '/form-builder/form-details/:id',
    page: 'form-builder/form-details'
  },
  {
    path: '/form-builder/custom-form-fill-order/:id',
    page: 'form-builder/custom-form-fill-order'
  },
  {
    path: '/form-builder/edit-form/:id',
    page: 'form-builder/create-form'
  },
  {
    path: '/form-builder/form-details/sub-forms/:appId/:formId',
    page: 'apps/sub-forms'
  },
  {
    path: '/google-drive/callback',
    page: 'configurations/get-link'
  },
  {
    path: '/one-drive/redirect',
    page: 'configurations/get-link'
  }
];

export const webRoutes = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    loader: ({ request }: LoaderFunctionArgs) => {
      const isAuthenticated = !!getStorageItem('access_token');
      if (!isAuthenticated) {
        const params = new URLSearchParams();
        params.set('from', new URL(request.url).pathname);
        return redirect(
          localStorage.getItem('user_type') === 'super_admin'
            ? '/login'
            : '/organization/login'
        );
        // return redirect('/login?' + params.toString());
      }
      return null;
    },

    children: mapRoutes(privateRoutes)
    // errorElement: <AsyncPage page="error/404" />,
  },
  {
    path: '/login',
    element: <AsyncPage page="auth/login" />,
    loader: () => {
      const isAuthenticated = !!getStorageItem('access_token');
      if (isAuthenticated) {
        <Loader status />;
        return redirect('/dashboard');
      }
      return null;
    }
  },
  {
    path: '/organization/login',
    element: <AsyncPage page="auth/organization-login" />,
    loader: () => {
      const isAuthenticated = !!getStorageItem('access_token');
      if (isAuthenticated) {
        <Loader status />;
        return redirect('/dashboard');
      }
      return null;
    }
  }
]);

export default webRoutes;
