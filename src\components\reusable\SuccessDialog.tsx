import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography
} from '@mui/material';

const SuccessDialog = ({
  open,
  onClose,
  title = 'Success',
  message = 'Operation completed successfully.'
}: any) => {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>
        {/* <CheckCircleOutlineIcon color="success" style={{ marginRight: 8 }} /> */}
        {title}
      </DialogTitle>
      <DialogContent>
        <Typography>{message}</Typography>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={onClose}
          variant="contained"
          sx={{
            backgroundColor: 'primaryBlue.main',
            color: 'white2.main',
            padding: '10px 35px',
            boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
            '&:hover': {
              color: 'white2.main',
              backgroundColor: '#08366b',
              boxShadow: '0px 8px 10px 4px rgba(0,0,0,0.2)'
            }
          }}
        >
          OK
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SuccessDialog;
