import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  CircularProgress
} from '@mui/material';

// Simulated API call
const fetchItems = (page: number, limit: number = 10): Promise<string[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const data = Array.from(
        { length: limit },
        (_, i) => `Item ${(page - 1) * limit + i + 1}`
      );
      resolve(data);
    }, 1000);
  });
};

const InfiniteScrollList: React.FC = () => {
  const [items, setItems] = useState<string[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loader = useRef(null);

  const loadItems = useCallback(async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    const newItems = await fetchItems(page);
    setItems((prev) => [...prev, ...newItems]);
    setPage((prev) => prev + 1);
    if (newItems.length === 0) setHasMore(false);
    setLoading(false);
  }, [page, loading, hasMore]);

  useEffect(() => {
    loadItems();
  }, []);

  useEffect(() => {
    if (observerRef.current) observerRef.current.disconnect();

    observerRef.current = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting && hasMore && !loading) {
        loadItems();
      }
    });

    if (loader.current) {
      observerRef.current.observe(loader.current);
    }
  }, [loadItems, hasMore, loading]);

  return (
    <Box
      sx={{
        maxWidth: 600,
        margin: 'auto',
        padding: 2,
        overflow: 'auto'
      }}
    >
      {items.map((item) => (
        <Card key={item} sx={{ mb: 2 }}>
          <CardContent>
            <Typography variant="h6">{item}</Typography>
          </CardContent>
        </Card>
      ))}

      {loading && (
        <Box display="flex" justifyContent="center" my={2}>
          <CircularProgress />
        </Box>
      )}

      <div ref={loader} />
      {!hasMore && (
        <Typography
          align="center"
          variant="body2"
          color="text.secondary"
          mt={2}
        >
          No more items to load.
        </Typography>
      )}
    </Box>
  );
};

export default InfiniteScrollList;
