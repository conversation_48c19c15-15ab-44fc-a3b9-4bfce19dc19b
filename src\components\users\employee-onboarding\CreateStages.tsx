/* eslint-disable @typescript-eslint/no-unused-vars */
// Package Imports
import {
  Autocomplete,
  Box,
  Button,
  Chip,
  Paper,
  TextField,
  Typography
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import ReactQuill from 'react-quill';
import { useFormikContext } from 'formik';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
import {
  AppForm,
  FormInput,
  FormRadio,
  SubmitButton
} from '../../form.elements';
import { CurrencyOption } from '../../../utils/currency_codes';
import { CreateStageInitForm } from '../../../types';
import { AppDispatch, AppState } from '../../../redux/app.store';
import {
  createstage,
  getstagewithid,
  updatestage
} from '../../../redux/reducers/stages.reducer';
import { getapp, getapps } from '../../../redux/reducers/apps.reducer';

interface Props {
  name: string;
  label: string;
}

const FormQuill: React.FC<Props> = ({ name, label }) => {
  const { values, setFieldValue } = useFormikContext<any>();
  return (
    <div style={{ height: '140px' }}>
      <Typography>{label}</Typography>
      <ReactQuill
        theme="snow"
        value={(values as any)[name] || ''}
        onChange={(val) => setFieldValue(name, val)}
        style={{ height: '100%' }}
      />
    </div>
  );
};

const CreateStages = ({
  page,
  onClose,
  stageId,
  fetchStagesList
}: {
  page: string;
  onClose?: any;
  stageId?: any;
  fetchStagesList?: any;
}) => {
  const [initialValues, setinitialValues] = useState<CreateStageInitForm>({
    title: '',
    description: '',
    appId: '',
    formIds: []
  });
  const [stageData, setStageData] = useState<any>({});
  const [selectedApp, setSelectedApp] = useState<any>();
  const [selectedForms, setSelectedForms] = useState<any[]>([]);
  const [appOptions, setAppOptions] = useState<any[]>([]);
  const [formOptions, setFormOptions] = useState<any[]>([]);
  const navigate = useNavigate();
  const { id }: any = useParams();
  const dispatch = useDispatch<AppDispatch>();
  const { apps } = useSelector((state: AppState) => state.app);

  const getData = async (appId: string, radioChange?: string) => {
    const res = await dispatch(getapp(appId));
    if (res?.meta.requestStatus === 'rejected') {
      toast.error(
        res?.payload?.message ||
          ' Something Went Wrong, Please Try Again Later.'
      );
    } else {
      const { data } = res.payload;
      setFormOptions(
        data?.forms?.map((form: any) => {
          return {
            label: form?.name,
            value: form?.form_id
          };
        })
      );
      if (radioChange) {
        setSelectedForms([]);
      }
    }
  };

  const getAppsAlongWithForms = async (appId?: any) => {
    try {
      const response = await dispatch(getapps(null));
      const { data } = response.payload;
      setAppOptions(
        data?.map((app: any) => {
          return {
            label: app?.orgAppConfiguration?.app_name
              ? app?.orgAppConfiguration?.app_name
              : app?.name,
            value: app?.app_id
          };
        })
      );

      if (response.meta.requestStatus === 'fulfilled') {
        // const appId = stageData?.app?.app_id || data[0]?.app_id;
        getData(appId || data[0]?.app_id);
      }
      if (!response.payload.status || response?.payload?.statusCode) {
        toast.error(
          response?.payload?.message ||
            'Something Went Wrong Please try again later'
        );
      }
    } catch (error: any) {
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  const handleRadioChange = async (e: any, values: any) => {
    const appId = e.target.value;
    setSelectedApp(appId);
    // Fetch forms for the selected app
    await getData(appId, 'radioChange');
  };

  const handleFormsSelect = async (_e: any, value: CurrencyOption[]) => {
    setSelectedForms(value);
  };

  const handleSubmit = async (val: any) => {
    const data = {
      title: val.title,
      description: val.description,
      app_id: selectedApp || val.appId,
      forms_list: selectedForms?.map((form: any) => form?.value),
      action_by: 'user'
    };

    if (stageId || id) {
      const res = await dispatch(updatestage({ id: stageId || id, data }));
      if (res.meta.requestStatus === 'rejected') {
        toast.error(
          res.payload?.message ? res.payload?.message : (res.payload as string)
        );
      } else {
        toast.success('Stage Updated Successfully');
        if (page === 'JobPost') {
          onClose();
        } else {
          navigate('/users/employee-onboarding/stages-list');
        }
      }
    } else {
      const res = await dispatch(createstage(data));
      if (res.meta.requestStatus === 'rejected') {
        toast.error(
          res.payload?.message ? res.payload?.message : (res.payload as string)
        );
      } else {
        toast.success('Stage Created Successfully');
        if (page === 'JobPost') {
          onClose();
        } else {
          navigate('/users/employee-onboarding/stages-list');
        }
      }
      fetchStagesList(res.payload.stage);
      console.log('Stage Created: ', res);
    }
  };

  const handleCancel = () => {
    if (page === 'JobPost') {
      onClose();
    } else {
      navigate(-1);
    }
  };

  const getStageDataWithId = async () => {
    const res = await dispatch(getstagewithid(stageId || id));
    if (res?.meta.requestStatus === 'rejected') {
      toast.error(
        res?.payload?.message ||
          ' Something Went Wrong, Please Try Again Later.'
      );
    } else {
      const { stage } = res.payload;
      setStageData(stage);
      getAppsAlongWithForms(stage?.app?.app_id);
      const initValues: CreateStageInitForm = {
        title: stage?.title,
        description: stage?.description || '',
        appId: stage?.app?.app_id,
        // formIds: stage?.forms_list
        // description: 'This is Description',
        // appId: '7b0a3b32-8ce6-407e-821d-90bbafa967a9',
        formIds: stage?.forms_list
      };
      setinitialValues(initValues);
      // if (stage?.forms_list && formOptions.length > 0) {
      // ✅ If your Formik stores only IDs (like ['form1', 'form2'])
      const selectedFormIds = stage?.forms_list?.map((form: any) => {
        return {
          value: form?.form_id,
          label: form?.name
        };
      });
      setSelectedForms(selectedFormIds || []);
      // }
    }
  };
  useEffect(() => {
    // getAppsAlongWithForms();

    if (stageId || id) {
      getStageDataWithId();
    } else {
      getAppsAlongWithForms();
    }
  }, []);
  return (
    <Box
      sx={{
        // minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: '#f9f9f9',
        p: 2
      }}
    >
      <Paper
        elevation={3}
        sx={{
          borderRadius: 4,
          padding: { xs: 2, sm: 3, md: 4 },
          width: '100%',
          maxWidth: { xs: '100%', sm: 500, lg: 600 },
          boxSizing: 'border-box'
        }}
      >
        <Typography
          variant="h5"
          fontWeight={700}
          textAlign="center"
          gutterBottom
        >
          Stage Creation
        </Typography>
        <AppForm initialValues={initialValues} onSubmit={handleSubmit}>
          <Box mt={3} display="flex" flexDirection="column" gap={2}>
            <FormInput name="title" type="text" label="Title" />
            <Box sx={{ height: { xs: 140, sm: 180, md: 220 } }}>
              <FormQuill name="description" label="Description" />
            </Box>
            <FormRadio
              data={appOptions}
              name="appId"
              label="Select App"
              handleRadioChange={handleRadioChange}
              disabled={!!stageId || !!id}
            />
            <Typography sx={{ position: 'relative', top: '10px' }}>
              Select Forms
            </Typography>
            <Autocomplete
              multiple
              options={formOptions}
              getOptionLabel={(option) => `${option.label}`}
              value={Array.isArray(selectedForms) ? selectedForms : []}
              onChange={handleFormsSelect}
              isOptionEqualToValue={(option, value) =>
                option.value === value.value
              }
              renderTags={(value: any, getTagProps) =>
                value.map((option: any, index: number) => (
                  <Chip
                    {...getTagProps({ index })}
                    label={`${option.label}`}
                    key={`${index + 1}`}
                  />
                ))
              }
              renderInput={(params: any) => (
                <TextField
                  {...params}
                  placeholder="Select Forms..."
                  error={!selectedForms || selectedForms.length === 0}
                  helperText={
                    !selectedForms || selectedForms.length === 0
                      ? 'Select at least one form is required'
                      : ''
                  }
                />
              )}
            />
            <Box
              sx={{
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                alignItems: 'center',
                justifyContent: 'flex-end',
                gap: '10px',
                marginTop: '20px'
              }}
            >
              <Button
                variant="outlined"
                onClick={handleCancel}
                sx={{ width: { xs: '100%', sm: 'auto' } }}
              >
                Cancel
              </Button>
              <SubmitButton
                title={stageId || id ? 'Update' : 'Create'}
                sx={{ width: { xs: '100%', sm: 'auto' } }}
              />
            </Box>
          </Box>
        </AppForm>
      </Paper>
      {/* <Box
        sx={{
          padding: '20px',
          paddingBottom: '70px'
        }}
      >
        <Typography
          sx={{
            fontWeight: 600,
            color: '#27292D',
            fontSize: 'clamp(1rem, 1.5vw, 1.25rem)'
          }}
        >
          Stage Creation
        </Typography>

        <Box>
          <AppForm initialValues={initialValues} onSubmit={handleSubmit}>
            <Box>
              <Box
                sx={{
                  position: 'relative'
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '20px',
                    background: '#FFFFFF',
                    border: 1,
                    borderColor: '#A3A3A3',
                    padding: '50px 20px',
                    marginTop: '20px',
                    width: '650px'
                  }}
                >
                  <FormInput name="title" type="text" label="Title" />
                  <Box sx={{ height: '220px' }}>
                    <FormQuill name="description" label="Description" />
                  </Box>
                  <FormRadio
                    data={appOptions}
                    name="appId"
                    label="Select App"
                    handleRadioChange={handleRadioChange}
                  />
                  <Typography sx={{ position: 'relative', top: '10px' }}>
                    Select Forms
                  </Typography>
                  <Autocomplete
                    multiple
                    options={formOptions}
                    getOptionLabel={(option) => `${option.label}`}
                    value={Array.isArray(selectedForms) ? selectedForms : []}
                    onChange={handleFormsSelect}
                    isOptionEqualToValue={(option, value) =>
                      option.value === value.value
                    }
                    renderTags={(value: any, getTagProps) =>
                      value.map((option: any, index: number) => (
                        <Chip
                          {...getTagProps({ index })}
                          label={`${option.label}`}
                          key={`${index + 1}`}
                        />
                      ))
                    }
                    renderInput={(params: any) => (
                      <TextField
                        {...params}
                        placeholder="Select Forms..."
                        error={!selectedForms || selectedForms.length === 0}
                        helperText={
                          !selectedForms || selectedForms.length === 0
                            ? 'Select at least one form is required'
                            : ''
                        }
                      />
                    )}
                  />
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'flex-end',
                      gap: '10px',
                      marginTop: '20px'
                    }}
                  >
                    <Button variant="outlined" onClick={handleCancel}>
                      Cancel
                    </Button>
                    <SubmitButton title={stageId || id ? 'Update' : 'Create'} />
                  </Box>
                </Box>
              </Box>
            </Box>
          </AppForm>
        </Box>
      </Box> */}
    </Box>
  );
};

export default CreateStages;
