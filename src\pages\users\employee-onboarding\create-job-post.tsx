// Package Imports
import React, { useEffect } from 'react';
// import { useNavigate } from 'react-router-dom';

// Local Imports
import Shell from '../../../components/layout/Shell';
import LoaderUI from '../../../components/reusable/loaderUI';
import { SubMenu } from '../../../components/form.elements';
import Sidebar from '../../../components/reusable/Sidebar';
import { UserNavBarModules } from '../../../components/users/UsersList';
import CreateJobPost from '../../../components/users/employee-onboarding/CreateJobPost';

const CreateJobPostPage: React.FC = () => {
  //   const navigate = useNavigate();
  const [isLoading, setIsLoading] = React.useState(false);
  const getSubMenu = () => {
    return <SubMenu backNavigation />;
  };

  const menuItems = UserNavBarModules();

  const getDrawer = () => {
    return <Sidebar menuItems={menuItems} userType={undefined} />;
  };

  useEffect(() => {
    setTimeout(() => {
      setIsLoading(false);
    }, 2200);
  }, []);

  return (
    <Shell subMenu={getSubMenu()} showDrawer drawerData={getDrawer()}>
      {isLoading ? <LoaderUI /> : <CreateJobPost />}
    </Shell>
  );
};

export default CreateJobPostPage;
