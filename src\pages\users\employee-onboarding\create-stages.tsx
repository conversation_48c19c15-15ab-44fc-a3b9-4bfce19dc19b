import React, { useEffect } from 'react';
import { SubMenu } from '../../../components/form.elements';
import { UserNavBarModules } from '../../../components/users/UsersList';
import Sidebar from '../../../components/reusable/Sidebar';
import Shell from '../../../components/layout/Shell';
import LoaderUI from '../../../components/reusable/loaderUI';
import CreateStages from '../../../components/users/employee-onboarding/CreateStages';

const CreateStagesPage = () => {
  const [isLoading, setIsLoading] = React.useState(false);
  const getSubMenu = () => {
    return <SubMenu backNavigation />;
  };

  const menuItems = UserNavBarModules();

  const getDrawer = () => {
    return <Sidebar menuItems={menuItems} userType={undefined} />;
  };

  useEffect(() => {
    setTimeout(() => {
      setIsLoading(false);
    }, 2200);
  }, []);

  return (
    <Shell subMenu={getSubMenu()} showDrawer drawerData={getDrawer()}>
      {isLoading ? <LoaderUI /> : <CreateStages />}
    </Shell>
  );
};

export default CreateStagesPage;
