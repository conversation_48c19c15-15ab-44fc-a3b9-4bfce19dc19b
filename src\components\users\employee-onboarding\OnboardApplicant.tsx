import { useState, useEffect } from 'react';
import {
  Box,
  Checkbox,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  //   TablePagination,
  TableRow,
  Typography,
  FormControlLabel,
  Badge,
  Pagination
} from '@mui/material';
import { Edit, Visibility } from '@mui/icons-material';
import Shell from '../../layout/Shell';
import { SubMenu } from '../../form.elements';
import Sidebar from '../../reusable/Sidebar';
import { UserNavBarModules } from '../UsersList';
import { SearchWithFilters } from '../../reusable/SearchWithFilters';

const globalStages = [
  'Stage 01',
  'Stage 02',
  'Stage 03',
  'Stage 04',
  'Stage 05'
];

const ApplicantsTable = () => {
  const menuItems = UserNavBarModules();

  const getSubMenu = () => (
    <SubMenu backNavigation createChecklist createApplicant />
  );

  const getDrawer = () => (
    <Sidebar menuItems={menuItems} userType={undefined} />
  );

  const [jobrole] = useState([
    {
      title: 'HR Manager',
      table: [
        {
          id: 1,
          name: '<PERSON>',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        },
        {
          id: 2,
          name: 'Lucy',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        },
        {
          id: 3,
          name: 'Jimmy',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        },
        {
          id: 4,
          name: 'Peter',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        },
        {
          id: 5,
          name: 'Sarah',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        },
        {
          id: 6,
          name: 'Megan',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        }
      ],
      stagesChecked: ['Stage 01', 'Stage 02', 'Stage 03']
    },
    {
      title: 'Care Giver',
      table: [
        {
          id: 7,
          name: 'Cathy',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        },
        {
          id: 8,
          name: 'Lucy',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        },
        {
          id: 9,
          name: 'Jimmy',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        },
        {
          id: 10,
          name: 'Peter',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        },
        {
          id: 11,
          name: 'Sarah',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        }
      ],
      stagesChecked: ['Stage 01', 'Stage 02']
    },
    {
      title: 'Team Lead',
      table: [
        {
          id: 7,
          name: 'Cathy',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        },
        {
          id: 8,
          name: 'Lucy',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        },
        {
          id: 9,
          name: 'Jimmy',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        },
        {
          id: 10,
          name: 'Peter',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        },
        {
          id: 11,
          name: 'Sarah',
          phone: '+01 124 236 111',
          email: '<EMAIL>'
        }
      ],
      stagesChecked: ['Stage 01', 'Stage 02']
    }
  ]);

  const rowsPerPage = 5;

  const [pages, setPages] = useState<{ [index: number]: number }>({});
  const [selectedStages, setSelectedStages] = useState<{
    [index: number]: string[];
  }>({});
  const [selectedRows, setSelectedRows] = useState<{
    [roleIndex: number]: number[];
  }>({});

  useEffect(() => {
    const initialPages: { [index: number]: number } = {};
    const initialSelectedStages: { [index: number]: string[] } = {};
    const initialSelectedRows: { [index: number]: number[] } = {};

    jobrole.forEach((role: any, i) => {
      initialPages[i] = 0;
      initialSelectedStages[i] = role?.stagesChecked || [];
      initialSelectedRows[i] = [];
    });

    setSelectedStages(initialSelectedStages);
    setSelectedRows(initialSelectedRows);
    setPages(initialPages);
  }, [jobrole]);

  const handlePageChange = (roleIndex: number, newPage: number) => {
    setPages((prev) => ({
      ...prev,
      [roleIndex]: newPage - 1 // MUI <Pagination> is 1-based, we need 0-based
    }));
  };

  const handleRowSelect = (roleIndex: number, rowId: number) => {
    setSelectedRows((prev) => {
      const selected = prev[roleIndex] || [];
      const isSelected = selected.includes(rowId);
      return {
        ...prev,
        [roleIndex]: isSelected
          ? selected.filter((id) => id !== rowId)
          : [...selected, rowId]
      };
    });
  };

  const handleSelectAll = (roleIndex: number, currentPageRows: any[]) => {
    const currentIds = currentPageRows.map((row) => row.id);
    const allSelected = currentIds.every((id) =>
      selectedRows[roleIndex]?.includes(id)
    );

    setSelectedRows((prev) => ({
      ...prev,
      [roleIndex]: allSelected
        ? prev[roleIndex].filter((id) => !currentIds.includes(id))
        : [...new Set([...prev[roleIndex], ...currentIds])]
    }));
  };

  const handleStageToggle = (roleIndex: number, stage: string) => {
    setSelectedStages((prev) => {
      const existing = prev[roleIndex] || [];
      const updated = existing.includes(stage)
        ? existing.filter((s) => s !== stage)
        : [...existing, stage];
      return {
        ...prev,
        [roleIndex]: updated
      };
    });
  };

  const getPage = (index: number) => pages[index] || 0;

  return (
    <Shell subMenu={getSubMenu()} showDrawer drawerData={getDrawer()}>
      <Box p={2} sx={{ height: '100%', overflow: 'auto' }}>
        <Box mb={15}>
          {jobrole.map((role, index) => {
            const currentPageRows = role.table.slice(
              getPage(index) * rowsPerPage,
              getPage(index) * rowsPerPage + rowsPerPage
            );

            const allSelected = currentPageRows.every((row) =>
              selectedRows[index]?.includes(row.id)
            );
            const someSelected =
              currentPageRows.some((row) =>
                selectedRows[index]?.includes(row.id)
              ) && !allSelected;

            return (
              <Box key={role.title} mb={4}>
                <Box display="flex" gap={2} mb={2}>
                  {globalStages.map((stage) => (
                    <FormControlLabel
                      key={stage}
                      control={
                        <Checkbox
                          checked={
                            selectedStages[index]?.includes(stage) || false
                          }
                          onChange={() => handleStageToggle(index, stage)}
                        />
                      }
                      label={stage}
                    />
                  ))}
                </Box>
                <Box
                  display="flex"
                  justifyContent="flex-start"
                  alignItems="center"
                  mb={2}
                >
                  <Typography
                    variant="h6"
                    sx={{
                      color: '#27292D',
                      fontWeight: '500',
                      fontFamily: 'Montserrat',
                      fontSize: '22px',
                      marginRight: '50px'
                    }}
                  >
                    {role.title}
                  </Typography>
                  <Badge
                    badgeContent={role.table.length}
                    color="primary"
                    sx={{
                      '& .MuiBadge-badge': {
                        backgroundColor: '#8080802e',
                        color: '#242424',
                        fontSize: '18px',
                        fontWeight: 400,
                        fontFamily: 'Montserrat',
                        borderRadius: '50%',
                        minWidth: '46px',
                        height: '46px',
                        padding: '0 8px'
                      },
                      marginRight: '30px'
                    }}
                  />
                  <SearchWithFilters />
                </Box>

                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell padding="checkbox">
                          <Checkbox
                            indeterminate={someSelected}
                            checked={allSelected}
                            onChange={() =>
                              handleSelectAll(index, currentPageRows)
                            }
                          />
                        </TableCell>
                        <TableCell>Applicant</TableCell>
                        <TableCell>Phone number</TableCell>
                        <TableCell>Email Address</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {currentPageRows.map((row) => (
                        <TableRow
                          key={row.id}
                          hover
                          selected={selectedRows[index]?.includes(row.id)}
                          onClick={() => handleRowSelect(index, row.id)}
                          sx={{ cursor: 'pointer' }}
                        >
                          <TableCell padding="checkbox">
                            <Checkbox
                              checked={
                                selectedRows[index]?.includes(row.id) || false
                              }
                              onChange={() => handleRowSelect(index, row.id)}
                              onClick={(e) => e.stopPropagation()}
                            />
                          </TableCell>
                          <TableCell>{row.name}</TableCell>
                          <TableCell>{row.phone}</TableCell>
                          <TableCell>{row.email}</TableCell>
                          <TableCell>
                            <IconButton>
                              <Edit fontSize="small" />
                            </IconButton>
                            <IconButton>
                              <Visibility fontSize="small" color="primary" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* <TablePagination
                    component="div"
                    count={role.table.length}
                    page={getPage(index)}
                    onPageChange={(_, newPage) =>
                      handlePageChange(index, newPage)
                    }
                    rowsPerPage={rowsPerPage}
                    rowsPerPageOptions={[]}
                    labelDisplayedRows={({ from, to, count }) =>
                      `${from}-${to} of ${count} items`
                    }
                  /> */}
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      py: '10px',
                      px: '10px'
                    }}
                  >
                    <Pagination
                      count={Math.ceil(role.table.length / rowsPerPage)} // total pages
                      page={getPage(index) + 1} // 1-based for UI
                      onChange={(_, newPage) =>
                        handlePageChange(index, newPage)
                      }
                      showFirstButton
                      showLastButton
                      shape="rounded"
                      color="primary"
                    />
                    <Box
                      sx={{
                        fontSize: '14px',
                        fontFamily: 'Montserrat',
                        color: '#444'
                      }}
                    >
                      {(() => {
                        const from = getPage(index) * rowsPerPage + 1;
                        const to = Math.min(
                          (getPage(index) + 1) * rowsPerPage,
                          role.table.length
                        );
                        const count = role.table.length;
                        return `${from} - ${to} of ${count} items`;
                      })()}
                    </Box>
                  </Box>
                </TableContainer>
              </Box>
            );
          })}
        </Box>
      </Box>
    </Shell>
  );
};

export default ApplicantsTable;
