/* eslint-disable no-await-in-loop */
import { useEffect, useState } from 'react';
import { pdf } from '@react-pdf/renderer';
import MyDocument from './MyDocument';
import FormResponseData from './form_response.json';

const TestingDocument = () => {
  const formResponseData: any = FormResponseData?.data;
  const [pdfDataUrls, setPdfDataUrls] = useState<string[]>([]);

  useEffect(() => {
    console.log('formResponseData', formResponseData);
    const generatePDFs = async () => {
      if (formResponseData.length === 0) return;

      const urls: string[] = [];
      // eslint-disable-next-line no-restricted-syntax
      for (const user of formResponseData) {
        const blob = await pdf(<MyDocument formResponse={user} />).toBlob();

        const dataUrl = await new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onloadend = () => resolve(reader.result as string);
          reader.readAsDataURL(blob);
        });

        urls.push(dataUrl);
      }

      setPdfDataUrls(urls);
    };

    generatePDFs();
  }, [formResponseData]);
  console.log(pdfDataUrls);
  console.log(formResponseData?.data);

  return (
    <>
      <p>React-pdf</p>
      {pdfDataUrls && <p>PDF Rendered Successfully</p>}
    </>
  );
};

export default TestingDocument;
