import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';

import { AppDispatch } from '../../../redux/app.store';
import { getemployees } from '../../../redux/reducers/user.reducer';
// import AccordionStyleEmployeeOnboarding from '../../../components/users/employee-onboarding/AccordionStyleEmployeeOnboarding';
import OnboardApplicant from '../../../components/users/employee-onboarding/OnboardApplicant';

const EmployeeOnboardingPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();

  const getApplicantsList = async () => {
    try {
      const response = await await dispatch(getemployees(null));

      if (!response.payload.status || response?.payload?.statusCode) {
        toast.error(
          response?.payload?.message ||
            'Something Went Wrong Please try again later'
        );
      }
    } catch (error: any) {
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  useEffect(() => {
    getApplicantsList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  // return <AccordionStyleEmployeeOnboarding />;
  return <OnboardApplicant />;
};

export default EmployeeOnboardingPage;
