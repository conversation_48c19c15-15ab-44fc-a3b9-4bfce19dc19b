// Global Imports
import React, { useEffect, useState } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Grid,
  Checkbox,
  Link,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormGroup
} from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import TagsInput from 'react-tagsinput';
import { toast } from 'react-toastify';
import moment from 'moment';

// Local Imports
import { SubMenu } from '../../form.elements';
import { AppDispatch } from '../../../redux/app.store';
import {
  checkfolder,
  exportToCSV,
  getclientforms,
  sendpdf
} from '../../../redux/reducers/clients.reducer';
import { RootState } from '../../../redux/reducers';
import '../../../css/form-values-styles.scss';
import '../../../css/form-display.scss';
import LoaderUI from '../../reusable/loaderUI';
import { capitalizeFirstLetter } from '../../../utils';
import Shell from '../../layout/Shell';

const FormResponses = ({ module }: any) => {
  const navigate = useNavigate();
  const { id, appId } = useParams();
  const dispatch = useDispatch<AppDispatch>();
  const { clientForms, isLoading } = useSelector(
    (state: RootState) => state.clients
  );
  const getData = async () => {
    try {
      const response = await dispatch(getclientforms({ id, type: module }));
      if (response.payload.error) {
        toast.error(response.payload?.error);
      } else if (response.payload.status) {
        // toast.success(response.payload?.message);
      }
    } catch (error: any) {
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };
  useEffect(() => {
    if (id) {
      getData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const [open, setOpen] = useState(false);
  const [path, setPath]: any = useState();
  const [type, setType]: any = useState();
  const [tags, setTags] = useState<any>([]);
  const [pdfType, setPdfType] = useState('');
  const [selectAll, setSelectAll] = useState(false);
  const [selectedForms, setSelectedForms] = useState<string[]>([]);

  const formNames = clientForms?.map((data: any) => data.form?.name);
  console.log('formNames', formNames);
  console.log(clientForms);

  const handleSendMailOpen = () => {
    setType('share');
    setOpen(true);
  };

  const handleSendPdfOpen = () => {
    setType('pdf');
    // setOpen(true);
    navigate(`/configurations/view-pdf/${id}/${appId}`);
  };

  const handleClickOpen = async () => {
    setType('upload');
    try {
      const response = await dispatch(
        checkfolder({
          clientId: id
        })
      );
      if (response.payload.error) {
        // setSnackbarOpen({
        //   status: true,
        //   message: response.payload?.error,
        //   type: 'error'
        // });
        toast.error(response.payload?.error);
      } else if (response.payload.status) {
        // setSnackbarOpen({
        //   status: true,
        //   message: response.payload?.message,
        //   type: 'success'
        // });
        toast.success(response.payload?.message);
        if (response.payload.isExisted) {
          setOpen(true);
        } else {
          try {
            const res = await dispatch(
              sendpdf({
                clientId: id,
                data: {
                  request_type: 'upload',
                  app_id: appId
                }
              })
            );
            if (res.payload.error) {
              // setSnackbarOpen({
              //   status: true,
              //   message: res.payload?.error,
              //   type: 'error'
              // });
              toast.error(res.payload?.error);
            } else if (res.payload.status) {
              // setSnackbarOpen({
              //   status: true,
              //   message: res.payload?.message,
              //   type: 'success'
              // });
              // toast.success(res.payload?.message);
            }
          } catch (error: any) {
            // setSnackbarOpen({
            //   status: true,
            //   message:
            //     error?.message || 'Something Went Wrong Please try again later',
            //   type: 'error'
            // });
            toast.error(
              error?.message || 'Something Went Wrong Please try again later'
            );
          }
        }
      }
    } catch (error: any) {
      // setSnackbarOpen({
      //   status: true,
      //   message:
      //     error?.message || 'Something Went Wrong Please try again later',
      //   type: 'error'
      // });
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  const handleExportCSVClick = async () => {
    try {
      const request: any = {
        exportToExcel: true
      };
      if (module === 'client-assessment') {
        request.client_id = id;
      } else {
        request.user_id = id;
      }

      const response: any = await dispatch(exportToCSV(request));

      // Extract the Blob from the response
      const blob = new Blob([response.payload], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Create an anchor element
      const link = document.createElement('a');
      link.href = url;

      // Set the filename for the download
      link.setAttribute('download', 'form_values.xlsx');

      // Append the anchor to the body
      document.body.appendChild(link);

      // Trigger the download
      link.click();

      // Remove the anchor from the body
      document.body.removeChild(link);

      // Clean up the URL object
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.log(error);
      // add snackbar to show error
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  const [checked, setChecked] = useState(false);
  const [input, setInput]: any = useState();

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChecked(event.target.checked);
  };
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInput(event.target.value);
  };

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  };
  const handleTagsChange = (newTags: any) => {
    if (isValidEmail(newTags[newTags.length - 1])) {
      setTags(newTags);
    }
  };

  const isObject = (value: any) =>
    typeof value === 'object' && !Array.isArray(value);
  const isArray = (value: any) => Array.isArray(value);
  // const getObjectKeys = (obj: any) => Object.keys(obj);

  const stripHtmlTags = (html: any) => {
    const doc = new DOMParser().parseFromString(html, 'text/html');
    return doc.body.textContent || '';
  };

  const openAttachment = (fileData: string, fileType: string): void => {
    try {
      const base64WithoutPrefix = fileData.split(',')[1];
      if (!base64WithoutPrefix) {
        throw new Error('Invalid base64 string');
      }

      const byteCharacters = atob(base64WithoutPrefix);
      const byteArray = Uint8Array.from(byteCharacters, (char) =>
        char.charCodeAt(0)
      );

      const mimeType = (() => {
        switch (fileType.toLowerCase()) {
          case 'application/pdf':
            return 'application/pdf';
          case 'jpg':
          case 'image/jpeg':
            return 'image/jpeg';
          case 'image/png':
            return 'image/png';
          case 'image/gif':
            return 'image/gif';
          default:
            throw new Error('Unsupported file type');
        }
      })();

      const blob = new Blob([byteArray], { type: mimeType });
      const blobUrl = URL.createObjectURL(blob);
      window.open(blobUrl, '_blank');

      // Revoke blob URL after some time to prevent memory leak
      setTimeout(() => URL.revokeObjectURL(blobUrl), 5000);
    } catch (error) {
      alert('Failed to open attachment. Please try again.');
    }
  };

  const renderField = (
    fieldDetails: any,
    fieldValue: any,
    fieldValueIndex?: number,
    signatureDate?: string
  ) => {
    const renderFieldValue = (field: any, value: any) => {
      switch (field.input_type || field.type) {
        case 'signature':
        case 'image':
        case 'scribble':
          return (
            <Box
              role="button"
              className="signature-field"
              onClick={() => openAttachment(value, 'jpg')}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  openAttachment(value, 'jpg');
                }
              }}
              tabIndex={0}
              sx={{
                display: 'inline-block',
                color: '#1976d2',
                // textDecoration: 'underline',
                cursor: 'pointer',
                '&:hover': { textDecoration: 'none' },
                '&:focus': {
                  outline: 'none',
                  textDecoration: 'none',
                  color: '#125699'
                }
              }}
            >
              {field.type !== 'signature' ? (
                <Box>Image</Box>
              ) : (
                <img src={value} height="100px" width="200px" alt="signature" />
              )}
              {!value && <Typography>N/A</Typography>}
            </Box>
          );
        case 'file':
          return isObject(value) ? (
            <Box
              role="button"
              className="signature-field"
              onClick={() =>
                openAttachment(value.file, value.mimetype || 'pdf')
              }
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  openAttachment(value.file, value.mimetype || 'pdf');
                }
              }}
              tabIndex={0}
              sx={{
                display: 'inline-block',
                color: '#1976d2',
                // textDecoration: 'underline',
                cursor: 'pointer',
                '&:hover': { textDecoration: 'none' },
                '&:focus': {
                  outline: 'none',
                  textDecoration: 'none',
                  color: '#125699'
                }
              }}
            >
              <Box>{value.name}</Box>
              {!value && <Typography>N/A</Typography>}
            </Box>
          ) : (
            (!value && <Typography>N/A</Typography>) ||
              (value && (
                <Box
                  role="button"
                  className="signature-field"
                  onClick={() => openAttachment(value, 'jpg')}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      openAttachment(value, 'jpg');
                    }
                  }}
                  tabIndex={0}
                  sx={{
                    display: 'inline-block',
                    color: '#1976d2',
                    // textDecoration: 'underline',
                    cursor: 'pointer',
                    '&:hover': { textDecoration: 'none' },
                    '&:focus': {
                      outline: 'none',
                      textDecoration: 'none',
                      color: '#125699'
                    }
                  }}
                >
                  {field.type !== 'signature' ? (
                    <Box>Image</Box>
                  ) : (
                    <img
                      src={value}
                      height="100px"
                      width="200px"
                      alt="signature"
                    />
                  )}
                  {!value && <Typography>N/A</Typography>}
                </Box>
              ))

            // (value &&
            //   value.map((file: any, index: number) => (
            //     <Box
            //       key={`${index + 1}`}
            //       role="button"
            //       className="signature-field"
            //       onClick={() =>
            //         openAttachment(file.file, file.mimetype || 'pdf')
            //       }
            //       onKeyDown={(e) => {
            //         if (e.key === 'Enter' || e.key === ' ') {
            //           openAttachment(file.file, file.mimetype || 'pdf');
            //         }
            //       }}
            //       tabIndex={0}
            //       sx={{
            //         display: 'inline-block',
            //         color: '#1976d2',
            //         // textDecoration: 'underline',
            //         cursor: 'pointer',
            //         '&:hover': { textDecoration: 'none' },
            //         '&:focus': {
            //           outline: 'none',
            //           textDecoration: 'none',
            //           color: '#125699'
            //         }
            //       }}
            //     >
            //       <Box>{file.name}</Box>
            //     </Box>
            //   )))
          );
        case 'download':
          return isObject(field?.value) ? (
            <Box
              role="button"
              className="signature-field"
              onClick={() =>
                openAttachment(
                  field?.value?.file,
                  field?.value?.mimetype || 'pdf'
                )
              }
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  openAttachment(
                    field?.value?.file,
                    field?.value?.mimetype || 'pdf'
                  );
                }
              }}
              tabIndex={0}
              sx={{
                display: 'inline-block',
                color: '#1976d2',
                // textDecoration: 'underline',
                cursor: 'pointer',
                '&:hover': { textDecoration: 'none' },
                '&:focus': {
                  outline: 'none',
                  textDecoration: 'none',
                  color: '#125699'
                }
              }}
            >
              <Box>{field?.value?.name || field?.value?.file_name}</Box>
              {!field?.value && <Typography>N/A</Typography>}
            </Box>
          ) : (
            (!field?.value && <Typography>N/A</Typography>) ||
              (field?.value && (
                <Box
                  role="button"
                  className="signature-field"
                  onClick={() => openAttachment(field?.value, 'jpg')}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      openAttachment(field?.value, 'jpg');
                    }
                  }}
                  tabIndex={0}
                  sx={{
                    display: 'inline-block',
                    color: '#1976d2',
                    // textDecoration: 'underline',
                    cursor: 'pointer',
                    '&:hover': { textDecoration: 'none' },
                    '&:focus': {
                      outline: 'none',
                      textDecoration: 'none',
                      color: '#125699'
                    }
                  }}
                >
                  {field.type !== 'signature' ? (
                    <Box>Image</Box>
                  ) : (
                    <img
                      src={value}
                      height="100px"
                      width="200px"
                      alt="signature"
                    />
                  )}
                  {!value && <Typography>N/A</Typography>}
                </Box>
              ))

            // (value &&
            //   value.map((file: any, index: number) => (
            //     <Box
            //       key={`${index + 1}`}
            //       role="button"
            //       className="signature-field"
            //       onClick={() =>
            //         openAttachment(file.file, file.mimetype || 'pdf')
            //       }
            //       onKeyDown={(e) => {
            //         if (e.key === 'Enter' || e.key === ' ') {
            //           openAttachment(file.file, file.mimetype || 'pdf');
            //         }
            //       }}
            //       tabIndex={0}
            //       sx={{
            //         display: 'inline-block',
            //         color: '#1976d2',
            //         // textDecoration: 'underline',
            //         cursor: 'pointer',
            //         '&:hover': { textDecoration: 'none' },
            //         '&:focus': {
            //           outline: 'none',
            //           textDecoration: 'none',
            //           color: '#125699'
            //         }
            //       }}
            //     >
            //       <Box>{file.name}</Box>
            //     </Box>
            //   )))
          );
        case 'url':
          return field?.value ? (
            <Link
              className="primary"
              href={field?.value}
              target="_blank"
              rel="noopener noreferrer"
            >
              {field?.value}
            </Link>
          ) : (
            <Typography>N/A</Typography>
          );
        case 'checkbox':
          return isArray(value) && value?.length > 0 ? (
            value.map((data: any, index: number) => (
              <Box key={`${index + 1}`} className="text-capitalize d-flex">
                <Checkbox checked readOnly />
                <Typography sx={{ marginTop: '10px' }}>
                  {capitalizeFirstLetter(data)}
                </Typography>
              </Box>
            ))
          ) : (
            <Typography>N/A</Typography>
          );
        case 'paragraph':
          return '';

        default:
          return (
            <Typography
              dangerouslySetInnerHTML={{
                __html: capitalizeFirstLetter(value) || 'N/A'
              }}
            />
          );
      }
    };

    return (
      <React.Fragment key={fieldDetails?.name}>
        {fieldDetails.type === 'signature' && (
          <Grid item xs={12} sx={{ marginLeft: '80px' }}>
            <Checkbox checked disabled />I agree and understand that all
            electronic signatures are the legal equivalent of my
            manual/handwritten signature and I consent to be legally bound to
            this agreement. I further agree my signature on this document is as
            valid as if I signed the document in writing. This is to be used in
            conjunction with the use of electronic signatures on all forms
            regarding any and all future documentation with a signature
            requirement, should I elect to have signed electronically. Under
            penalty of perjury, I herewith affirm that my electronic signature,
            and all future electronic signatures, were signed by myself with
            full knowledge and consent and am legally bound to these terms and
            conditions
          </Grid>
        )}
        <Grid
          item
          xs={
            (fieldDetails?.type || fieldDetails?.input_type) === 'paragraph'
              ? 12
              : 6
          }
        >
          {/* {fieldDetails?.type !== 'paragraph' && ( */}
          <Typography variant="body1" className="field-label">
            {`${stripHtmlTags(fieldDetails?.label)} ${fieldValueIndex !== undefined && fieldValueIndex >= 0 ? `#${fieldValueIndex + 1}` : ''}`}
          </Typography>
          {/* )} */}
          {fieldDetails?.description && (
            <Typography
              variant="body2"
              // color="textSecondary"
              sx={{ padding: '10px 100px' }}
              dangerouslySetInnerHTML={{ __html: fieldDetails?.description }}
            >
              {/* {stripHtmlTags(fieldDetails?.description)} */}
            </Typography>
          )}
        </Grid>
        {fieldDetails.type !== 'paragraph' && (
          <Grid
            item
            xs={
              (fieldDetails.type || fieldDetails.input_type) === 'paragraph'
                ? 12
                : 6
            }
          >
            {(() => {
              let displayData = null;

              displayData = renderFieldValue(fieldDetails, fieldValue);

              return displayData ?? <Box>N/A</Box>;
            })()}
          </Grid>
        )}

        {fieldDetails.type === 'signature' && (
          <>
            <Grid item xs={6}>
              <Typography variant="body1" className="field-label">
                Date
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography>
                {signatureDate
                  ? moment(signatureDate).format('MM-DD-YYYY ')
                  : 'N/A'}
              </Typography>
            </Grid>
          </>
        )}
      </React.Fragment>
    );
  };

  const renderSection = (
    sectionDetails: any,
    sectionKey: any,
    sectionValues?: any,
    index?: number
  ) => {
    return (
      <Box key={sectionKey} className="group-box">
        <Typography variant="h4" className="group-title">
          {/* {`${sectionDetails?.group_title} ${index !== undefined && index >= 0 ? index + 1 : ''}`} */}
          {`${sectionDetails?.group_title} ${index !== undefined && index >= 0 ? `#${index + 1}` : ''}`}
        </Typography>
        {sectionDetails?.sectionCaption && (
          <Typography variant="body1" color="textSecondary">
            {sectionDetails?.sectionCaption}
          </Typography>
        )}
        {sectionDetails?.group_description && (
          <Box sx={{ paddingTop: 2 }}>
            <Typography variant="body1" sx={{ marginLeft: '45px' }}>
              {sectionDetails?.group_description}
            </Typography>
          </Box>
        )}
        <Grid container spacing={2} sx={{ paddingTop: 2 }}>
          {[...(sectionDetails?.fields || [])] // Clone to avoid modifying the original array
            .sort((a: any, b: any) => a.field_index - b.field_index) // Sorting based on field_index
            .map((fieldDetails: any) =>
              fieldDetails?.is_iterative_or_not &&
              sectionValues?.[fieldDetails?.name]
                ? sectionValues?.[fieldDetails?.name]?.map(
                    (fieldValue: any, fieldValueIndex: number) =>
                      renderField(fieldDetails, fieldValue, fieldValueIndex)
                  )
                : renderField(
                    fieldDetails,
                    sectionValues?.[fieldDetails?.name] || '',
                    undefined,
                    sectionValues?.[`${fieldDetails?.name}_date`] || null
                  )
            )}
        </Grid>
      </Box>
    );
  };

  const getSubMenu = () => {
    return (
      <SubMenu
        backNavigation
        uploadForms={module === 'client-assessment'}
        exportCSV={module === 'client-assessment'}
        sendMail={module === 'client-assessment'}
        sendPdf={module === 'client-assessment'}
        handleUploadClick={handleClickOpen}
        handleSendMailClick={handleSendMailOpen}
        handleExportCSVClick={handleExportCSVClick}
        handleSendPdfClick={handleSendPdfOpen}
      />
    );
  };

  return (
    <Shell subMenu={getSubMenu()}>
      {isLoading ? (
        <LoaderUI />
      ) : (
        <Box className="main-container">
          <Typography className="title" sx={{ marginLeft: '20px' }}>
            Form Responses
          </Typography>
          {clientForms?.length > 0 ? (
            <Box sx={{ padding: '20px' }}>
              <Dialog
                open={open}
                onClose={handleClose}
                maxWidth="sm"
                fullWidth
                PaperProps={{
                  component: 'form',
                  onSubmit: async (event: React.FormEvent<HTMLFormElement>) => {
                    event.preventDefault();
                    const formData = new FormData(event.currentTarget);
                    const formJson = Object.fromEntries(
                      (formData as any).entries()
                    );

                    let response;

                    if (type === 'share') {
                      const email = formJson?.email;
                      if (email) {
                        response = await dispatch(
                          sendpdf({
                            clientId: id,
                            data: {
                              request_type: 'share',
                              app_id: appId,
                              email,
                              cc_emails: tags
                            }
                          })
                        );
                        if (response.payload.status) {
                          handleClose();
                        }
                      }
                    } else {
                      const folderName = formJson?.folderName;
                      const checkbox = formJson?.checkbox;

                      if (folderName && !checkbox) {
                        response = await dispatch(
                          checkfolder({
                            clientId: id,
                            folderName
                          })
                        );
                        if (response.payload.status) {
                          if (response.payload.isExisted) {
                            setPath(folderName);
                            setInput('');
                          } else {
                            response = await dispatch(
                              sendpdf({
                                clientId: id,
                                data: {
                                  request_type: 'upload',
                                  app_id: appId,
                                  path: folderName
                                }
                              })
                            );
                            if (response.payload.status) {
                              setChecked(false);
                              setPath('');
                              setInput('');
                              handleClose();
                            }
                          }
                        }
                      } else if (checkbox) {
                        response = await dispatch(
                          sendpdf({
                            clientId: id,
                            data: {
                              request_type: 'upload',
                              app_id: appId
                            }
                          })
                        );
                        if (response.payload.status) {
                          handleClose();
                          setChecked(false);
                          setPath('');
                          setInput('');
                        }
                      }
                    }
                  }
                }}
              >
                <DialogContent sx={{ paddingLeft: 7, paddingRight: 7 }}>
                  {type === 'share' ? (
                    <>
                      <TextField
                        autoFocus
                        required
                        margin="dense"
                        id="email"
                        name="email"
                        label="Please enter email"
                        placeholder="Enter Email"
                        type="email"
                        fullWidth
                        variant="standard"
                        InputLabelProps={{
                          shrink: true,
                          style: { color: 'black', fontSize: '25px' }
                        }}
                        InputProps={{
                          style: { marginTop: '40px' }
                        }}
                      />
                      <TagsInput value={tags} onChange={handleTagsChange} />
                      <FormControl component="fieldset" sx={{ mt: 4 }}>
                        <Typography sx={{ fontWeight: 500, mb: 1 }}>
                          PDF Format:
                        </Typography>
                        <RadioGroup
                          row
                          aria-label="pdfType"
                          name="pdfType"
                          value={pdfType}
                          onChange={(e) => setPdfType(e.target.value)}
                        >
                          <FormControlLabel
                            value="single"
                            control={<Radio />}
                            label="Single PDF"
                          />
                          <FormControlLabel
                            value="multiple"
                            control={<Radio />}
                            label="Multiple PDF"
                          />
                        </RadioGroup>

                        <Box sx={{ mt: 4 }}>
                          <Typography sx={{ fontWeight: 500, mb: 1 }}>
                            Choose Forms:
                          </Typography>
                          <FormGroup>
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={selectAll}
                                  onChange={({
                                    target: { checked: isChecked }
                                  }) => {
                                    setSelectAll(isChecked);
                                    setSelectedForms(
                                      isChecked ? formNames : []
                                    );
                                  }}
                                />
                              }
                              label="Select All Forms"
                            />
                            {formNames.map((form) => (
                              <FormControlLabel
                                key={form}
                                control={
                                  <Checkbox
                                    checked={selectedForms.includes(form)}
                                    onChange={({
                                      target: { checked: isChecked }
                                    }) => {
                                      const updated = isChecked
                                        ? [...selectedForms, form]
                                        : selectedForms.filter(
                                            (f) => f !== form
                                          );
                                      setSelectedForms(updated);
                                      setSelectAll(
                                        updated.length === formNames.length
                                      );
                                    }}
                                  />
                                }
                                label={form}
                              />
                            ))}
                          </FormGroup>
                        </Box>
                      </FormControl>
                    </>
                  ) : (
                    <>
                      {path && (
                        <DialogContentText
                          sx={{
                            fontSize: '20px',
                            fontWeight: '500',
                            color: 'black'
                          }}
                        >
                          <Typography
                            sx={{
                              fontSize: '24px',
                              fontWeight: '600',
                              paddingTop: 4
                            }}
                          >
                            Folder name already exists!
                          </Typography>
                          <Checkbox
                            disabled={input !== ''}
                            checked={checked}
                            onChange={handleChange}
                            inputProps={{ 'aria-label': 'controlled' }}
                            sx={{ paddingLeft: 0 }}
                            id="checkbox"
                            name="checkbox"
                          />
                          Use same folder
                        </DialogContentText>
                      )}
                      {path && (
                        <Typography
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          (or)
                        </Typography>
                      )}
                      <TextField
                        autoFocus
                        disabled={!!checked}
                        onChange={handleInputChange}
                        margin="dense"
                        id="name"
                        name="folderName"
                        label="Please enter new folder"
                        placeholder="Enter Folder Name"
                        type="text"
                        fullWidth
                        variant="standard"
                        InputLabelProps={{
                          shrink: true,
                          style: { color: 'black', fontSize: '25px' }
                        }}
                        InputProps={{
                          style: { marginTop: '40px' }
                        }}
                      />
                    </>
                  )}
                </DialogContent>
                <DialogActions
                  sx={{ paddingBottom: 5, paddingLeft: 6, paddingRight: 5 }}
                >
                  <Button onClick={handleClose}>Cancel</Button>
                  <Button type="submit">Ok</Button>
                </DialogActions>
              </Dialog>

              {clientForms.map((client: any, index: number) => (
                <Box
                  key={client.form.id || `client-${index}`}
                  className="client-form-box"
                >
                  <Box className="client-name-box">
                    <Typography
                      className="client-name-typo"
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        width: '100%'
                      }}
                    >
                      <span>{client?.form?.name}</span>
                      {client?.form?.is_quiz_form && (
                        <span>
                          Scored {client?.scored_points} out of{' '}
                          {client?.total_points}
                        </span>
                      )}
                    </Typography>
                  </Box>

                  {Object.keys(client?.form?.fields).map((sectionKey: any) =>
                    client?.form?.fields?.[sectionKey]?.is_iterative_or_not
                      ? client?.values?.[sectionKey]?.length &&
                        client?.values?.[sectionKey]?.map(
                          (value: any, idx: number) =>
                            renderSection(
                              client?.form?.fields?.[sectionKey],
                              sectionKey,
                              value,
                              idx
                            )
                        )
                      : renderSection(
                          client?.form?.fields?.[sectionKey],
                          sectionKey,
                          client?.values?.[sectionKey]
                        )
                  )}
                </Box>
              ))}
            </Box>
          ) : (
            <Box sx={{ padding: '20px' }}>
              <Typography sx={{ textAlign: 'center' }}>
                Applicant has not started filling the form/forms.
              </Typography>
            </Box>
          )}
        </Box>
      )}
      {/* <SnackbarElement
        message={snackbarOpen?.message}
        statusType={snackbarOpen?.type || 'success'}
        snackbarOpen={snackbarOpen?.status}
        setSnackbarOpen={setSnackbarOpen}
      /> */}
    </Shell>
  );
};

export default FormResponses;
