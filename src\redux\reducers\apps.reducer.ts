import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import {
  autoCreate,
  autoCreateWithPdf,
  ChangeFormStatus,
  createApp,
  createDashboardConfig,
  getApp,
  getAppData,
  getAppForms,
  getApps,
  getDashboardConfig,
  getDashboardConfigPreviewData,
  getFormData,
  getIndustryAppProcess,
  getPrimaryForm,
  getSubForms,
  updateApp,
  updateDashboardConfig
} from '../../apis/apps';

const initialState = {
  errors: {},
  isLoading: false,
  loadingError: {},
  isFormSubmiting: false,
  industryAppProcess: [],
  apps: [],
  app: {},
  appId: '',
  formsList: [],
  subForms: [],
  formData: {},
  appData: {
    name: '',
    description: '',
    industry_type_id: '',
    industry_app_process_id: '',
    logo: '',
    app_name: ''
  },
  primaryForm: {},
  dashboardConfig: {},
  dashboardConfigData: {},
  getApp: {},
  updateApp: {}
};

export const getapps = createAsyncThunk('getapps', getApps);
export const getapp = createAsyncThunk('getapp', getApp);
export const getappforms = createAsyncThunk('getappforms', getAppForms);
export const createapp = createAsyncThunk('createapp', createApp);
export const updateapp = createAsyncThunk('updateapp', updateApp);
export const autocreate = createAsyncThunk('autocreate', autoCreate);
export const autocreatewithpdf = createAsyncThunk(
  'autocreatewithpdf',
  autoCreateWithPdf
);
export const getindustryappprocess = createAsyncThunk(
  'getindustryappprocess',
  getIndustryAppProcess
);
export const getsubforms = createAsyncThunk('getsubforms', getSubForms);
export const getformdata = createAsyncThunk('getformdata', getFormData);
export const getappdata = createAsyncThunk('getappdata', getAppData);
export const changeformstatus = createAsyncThunk(
  'changeformstatus',
  ChangeFormStatus
);
export const getprimaryform = createAsyncThunk(
  'getprimaryform',
  getPrimaryForm
);
export const getdashboardconfig = createAsyncThunk(
  'getdashboardconfig',
  getDashboardConfig
);
export const createdashboardconfig = createAsyncThunk(
  'createdashboardconfig',
  createDashboardConfig
);
export const updatedashboardconfig = createAsyncThunk(
  'updatedashboardconfig',
  updateDashboardConfig
);
export const getdashboardconfigpreviewdata = createAsyncThunk(
  'getdashboardconfigpreviewdata',
  getDashboardConfigPreviewData
);

const appSlice = createSlice({
  name: 'org',
  initialState,
  reducers: {
    updateAppId: (state, action) => {
      state.appId = action.payload;
    },
    clearGetAppState: (state) => {
      state.getApp = initialState.getApp;
    },
    clearAppState: (state) => {
      state.app = initialState.app;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getapps.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getapps.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.apps = data;
      })
      .addCase(getapps.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });

    builder
      .addCase(getapp.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getapp.fulfilled, (state, action) => {
        state.isLoading = false;
        state.getApp = action.payload.data;
      })
      .addCase(getapp.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(createapp.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createapp.fulfilled, (state) => {
        state.isLoading = false;
        // const data = action.payload.data;
      })
      .addCase(createapp.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(updateapp.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateapp.fulfilled, (state, action) => {
        state.isLoading = false;
        state.updateApp = action.payload.data;
        // const data = action.payload.data;
      })
      .addCase(updateapp.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getindustryappprocess.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getindustryappprocess.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.industryAppProcess = data;
      })
      .addCase(getindustryappprocess.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(autocreate.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(autocreate.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        const userType = localStorage.getItem('user_type');

        if (data?.has_sub_forms) {
          if (userType === 'super_admin') {
            window.location.assign(
              `sub-forms/${data?.app_id}/${data?.form_id}`
            );
          } else {
            window.location.assign(
              `sub-forms/${data?.app_id}/${data?.form_id}`
            );
          }
        } else {
          if (userType === 'super_admin') {
            window.location.assign(
              `/apps/form-builder/edit-form/${data?.form_id}`
            );
          }
          window.location.assign(`/form-builder/edit-form/${data?.form_id}`);
        }
      })
      .addCase(autocreate.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(autocreatewithpdf.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(autocreatewithpdf.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(autocreatewithpdf.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getsubforms.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getsubforms.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.subForms = data;
        state.formData = data;
      })
      .addCase(getsubforms.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getformdata.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getformdata.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.formData = data;
      })
      .addCase(getformdata.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(getappdata.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getappdata.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data } = action.payload;
        const userType = localStorage.getItem('user_type');
        if (userType === 'organization') {
          state.appData = data;
          state.appData.app_name = data?.orgAppConfiguration?.app_name;
          state.appData.logo = data?.orgAppConfiguration?.logo;
        } else {
          state.appData = data;
        }
      })
      .addCase(getappdata.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
    builder
      .addCase(changeformstatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(changeformstatus.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(changeformstatus.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(getprimaryform.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getprimaryform.fulfilled, (state, action) => {
        state.isLoading = false;
        state.primaryForm = action.payload.data;
      })
      .addCase(getprimaryform.rejected, (state) => {
        state.isLoading = false;
      });

    builder
      .addCase(getdashboardconfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getdashboardconfig.fulfilled, (state, action) => {
        state.isLoading = false;
        state.dashboardConfig = action.payload.data;
      })
      .addCase(getdashboardconfig.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(createdashboardconfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createdashboardconfig.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(createdashboardconfig.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(updatedashboardconfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updatedashboardconfig.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(updatedashboardconfig.rejected, (state) => {
        state.isLoading = false;
      });
    builder
      .addCase(getdashboardconfigpreviewdata.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getdashboardconfigpreviewdata.fulfilled, (state, action) => {
        state.isLoading = false;
        state.dashboardConfigData = action.payload.data;
      })
      .addCase(getdashboardconfigpreviewdata.rejected, (state) => {
        state.isLoading = false;
      });
  }
});

export default appSlice.reducer;

export const { updateAppId, clearGetAppState, clearAppState } =
  appSlice.actions;
