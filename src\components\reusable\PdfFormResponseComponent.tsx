/* eslint-disable react/destructuring-assignment */
import { toast } from 'react-toastify';
import LoadingButton from '@mui/lab/LoadingButton';

import { useSelector, useDispatch } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { PDFViewer, Font } from '@react-pdf/renderer';
import { Box } from '@mui/material';
import { RootState } from '../../redux/reducers';
import MyNewPDFComponentone from './MyNewPdfComponentone';
import FormResponseData from './form_response.json';
import { AppDispatch, AppState } from '../../redux/app.store';
import {
  postpdfconfig,
  updatepdfconfig
} from '../../redux/reducers/addressconfig.reducer';

Font.registerEmojiSource({
  format: 'png',
  url: 'https://cdnjs.cloudflare.com/ajax/libs/twemoji/14.0.2/72x72/%27'
});

export default function PdfFormResponse(props: {
  orientationValue: string;
  sizeValue: string;
  breakValue: string;
  marginValue: string;
  // logoAlignment: any;
  currentColor: any;
  // colorScheme: any;
  textColor: any;
  fontFamily: string;
  fontSize: string;
  insertLogo: any;
  headerTitle: any;
  watermarkText: any;
  watermarkImage: any;
  alignPageNumberText: string;
  headerHeight: any;
  // headerWidth: any;
  footerHeight: any;
  footerText: string;
}) {
  const { clientForms } = useSelector((state: RootState) => state.clients);
  const formResponseData: any = FormResponseData;
  const { pdfConfig }: any = useSelector(
    (state: AppState) => state.addressconfig
  );

  const location = useLocation();
  const dispatch = useDispatch<AppDispatch>();

  const handleSave = async () => {
    const data = {
      configuration_id: pdfConfig ? pdfConfig?.configuration_id : '',
      name: 'print',
      type: 'pdf',
      details: {
        orientation: props.orientationValue,
        size: props.sizeValue,
        pageBreaks: props.breakValue,
        margin: props.marginValue,
        uploadLogo: props.insertLogo,
        backgroundColor: props.currentColor,
        textColor: props.textColor,
        headerHeight: props.headerHeight,
        footerHeight: props.footerHeight,
        fontFamily: props.fontFamily,
        fontSize: props.fontSize,
        watermarkText: props.watermarkText,
        // headerTitle: props.headerTitle,
        footerText: props.footerText
      }
    };

    if (pdfConfig?.configuration_id) {
      try {
        const response = await dispatch(
          updatepdfconfig({
            id: pdfConfig?.configuration_id,
            data
          })
        );
        if (response.payload.error) {
          toast.error(response.payload?.error);
        }
      } catch (error: any) {
        toast.error(
          error?.message || 'Something Went Wrong Please try again later'
        );
      }
    } else {
      try {
        const response = await dispatch(postpdfconfig(data));
        if (response.payload.status) {
          toast.success(response.payload?.message);
        } else {
          toast.error(response.payload?.message || 'Something went wrong!');
        }
      } catch (error: any) {
        toast.error(
          error?.message || 'Something Went Wrong Please try again later'
        );
      }
    }
  };
  return (
    // <Paper sx={{ width: '100%', overflow: 'hidden', overflowX: 'hidden' }}>
    <Box sx={{ width: '100%', overflow: 'hidden', overflowX: 'hidden' }}>
      <PDFViewer
        style={{
          width: '100%', // Take full container width
          height: '100vh',
          overflow: 'hidden'
        }}
      >
        <MyNewPDFComponentone
          formResponses={
            location.pathname?.includes('/view-pdf')
              ? clientForms
              : formResponseData.data
          }
          orientationValue={props.orientationValue}
          sizeValue={props.sizeValue}
          breakValue={props.breakValue}
          marginValue={props.marginValue}
          // logoAlignment={props.logoAlignment}
          // colorScheme={props.colorScheme}
          // backgroundColor={props.currentColor}
          fontFamily={props.fontFamily}
          fontSize={props.fontSize}
          watermarkText={props.watermarkText}
          // alignText={props.alignText}
          alignPageNumberText={props.alignPageNumberText}
          insertLogo={props.insertLogo}
          headerTitle={
            location.pathname?.includes('/pdf-configuration')
              ? `${props.headerTitle} - Sample Data`
              : props.headerTitle
          }
          textColor={props.textColor}
          watermarkImage={props.watermarkImage}
          headerHeight={props.headerHeight}
          // headerWidth={props.headerWidth}
          footerHeight={props.footerHeight}
          footerText={props.footerText}
        />
      </PDFViewer>
      {location.pathname?.includes('/pdf-configuration') && (
        <LoadingButton
          variant="contained"
          color="primary"
          title={pdfConfig?.configuration_id ? 'Update' : 'Save'}
          onClick={handleSave}
          sx={{
            backgroundColor: 'primaryBlue.main',
            color: 'white2.main',
            padding: '10px 30px',
            boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
            '&:hover': {
              color: 'white2.main',
              backgroundColor: 'primaryBlue.main'
            },
            alignItems: 'flex-end'
          }}
        >
          {pdfConfig?.configuration_id ? 'Update' : 'Save'}
        </LoadingButton>
      )}
    </Box>
    // </Paper>
  );
}
