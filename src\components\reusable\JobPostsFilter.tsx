import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Button,
  Menu,
  MenuItem,
  IconButton,
  Typography,
  Chip,
  Paper,
  Divider
} from '@mui/material';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import CloseIcon from '@mui/icons-material/Close';

type JobPostsFilterProps = {
  onFilterChange?: (filters: Record<string, any>) => void;
};

const JobPostsFilter: React.FC<JobPostsFilterProps> = ({ onFilterChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState<Record<string, any>>(
    {}
  );
  const [menuAnchorEl, setMenuAnchorEl] = useState<HTMLElement | null>(null);
  const [currentFilterType, setCurrentFilterType] = useState<string | null>(
    null
  );
  const popupRef = useRef<HTMLDivElement>(null);

  const filterOptions = {
    employment_type: ['Full-time', 'Part-time', 'Contract', 'Internship'],
    status: ['Active', 'Inactive', 'Draft', 'Closed'],
    location: [
      'Remote',
      'New York',
      'San Francisco',
      'London',
      'Berlin',
      'Toronto'
    ],
    department: [
      'Engineering',
      'Marketing',
      'Sales',
      'HR',
      'Finance',
      'Product'
    ],
    salary_range: ['$30k-50k', '$50k-80k', '$80k-120k', '$120k-150k', '$150k+'],
    stages: ['Screening', 'Interview', 'Technical', 'Final Round', 'Offer']
  };

  // Close popup when clicking outside (but not on dropdown menus)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      // If click is inside popup panel, do nothing
      if (popupRef.current && popupRef.current.contains(target)) return;

      // If click is inside any MUI Menu (which renders outside via portal), do nothing
      if (target.closest('.MuiPopover-root')) return;

      // Otherwise, close the popup
      setIsOpen(false);
      setCurrentFilterType(null);
      setMenuAnchorEl(null);
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleFilterSelect = (filterType: string, value: any) => {
    const newFilters = { ...selectedFilters, [filterType]: value };
    setSelectedFilters(newFilters);
    onFilterChange?.(newFilters);
  };

  const removeFilter = (filterType: string) => {
    const newFilters = { ...selectedFilters };
    delete newFilters[filterType];
    setSelectedFilters(newFilters);
    onFilterChange?.(newFilters);
  };

  const clearAllFilters = () => {
    setSelectedFilters({});
    onFilterChange?.({});
  };

  const toggleDropdown = (
    filterType: string,
    event: React.MouseEvent<HTMLElement>
  ) => {
    if (currentFilterType === filterType) {
      setCurrentFilterType(null);
      setMenuAnchorEl(null);
    } else {
      setCurrentFilterType(filterType);
      setMenuAnchorEl(event.currentTarget);
    }
  };

  const getFilterLabel = (key: string) => {
    return key
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const activeFiltersCount = Object.keys(selectedFilters).length;

  return (
    <Box position="relative" ref={popupRef}>
      {/* Filter Button */}
      <Button
        variant="outlined"
        startIcon={<FilterAltIcon />}
        endIcon={
          <ChevronRightIcon
            sx={{ transform: isOpen ? 'rotate(90deg)' : 'none' }}
          />
        }
        onClick={() => {
          setIsOpen(!isOpen);
          setCurrentFilterType(null);
          setMenuAnchorEl(null);
        }}
        sx={{
          borderRadius: 2,
          bgcolor: 'white',
          boxShadow: 1,
          textTransform: 'none',
          color: 'text.primary'
        }}
      >
        Filters {activeFiltersCount > 0 && `(${activeFiltersCount})`}
      </Button>

      {/* Active Filter Chips */}
      {activeFiltersCount > 0 && (
        <Box display="flex" flexWrap="wrap" gap={1} mt={1}>
          {Object.entries(selectedFilters).map(([key, value]) => (
            <Chip
              key={key}
              label={`${getFilterLabel(key)}: ${value}`}
              onDelete={() => removeFilter(key)}
              size="small"
              color="primary"
              sx={{ fontSize: 12 }}
            />
          ))}
          <Button size="small" color="secondary" onClick={clearAllFilters}>
            Clear all
          </Button>
        </Box>
      )}

      {/* Popup Panel */}
      {isOpen && (
        <Paper
          elevation={3}
          sx={{
            position: 'absolute',
            top: 'calc(100% + 8px)',
            width: 300,
            zIndex: 10,
            borderRadius: 2,
            p: 2
          }}
        >
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            mb={2}
          >
            <Typography variant="h6" fontSize={16}>
              Filter Jobs
            </Typography>
            <IconButton size="small" onClick={() => setIsOpen(false)}>
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>

          {/* Filter Fields */}
          <Box display="flex" flexDirection="column" gap={2}>
            {Object.entries(filterOptions).map(([filterType, options]) => (
              <Box key={filterType}>
                <Typography variant="body2" fontWeight={500} mb={0.5}>
                  {getFilterLabel(filterType)}
                </Typography>

                <Button
                  variant="outlined"
                  fullWidth
                  size="small"
                  onClick={(e) => toggleDropdown(filterType, e)}
                  endIcon={
                    <ChevronRightIcon
                      sx={{
                        transform:
                          currentFilterType === filterType
                            ? 'rotate(90deg)'
                            : 'none',
                        transition: 'transform 0.2s'
                      }}
                    />
                  }
                  sx={{
                    justifyContent: 'space-between',
                    textTransform: 'none',
                    fontSize: 13,
                    bgcolor: 'white'
                  }}
                >
                  {selectedFilters[filterType] || 'Select option...'}
                </Button>

                {/* Dropdown Menu */}
                <Menu
                  anchorEl={menuAnchorEl}
                  open={currentFilterType === filterType}
                  onClose={() => {
                    setCurrentFilterType(null);
                    setMenuAnchorEl(null);
                  }}
                  anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                  transformOrigin={{ vertical: 'top', horizontal: 'left' }}
                  PaperProps={{
                    sx: { maxHeight: 200 }
                  }}
                >
                  {selectedFilters[filterType] && (
                    <MenuItem
                      onClick={() => {
                        removeFilter(filterType);
                        setCurrentFilterType(null);
                        setMenuAnchorEl(null);
                      }}
                      sx={{ color: 'error.main', fontSize: 13 }}
                    >
                      Clear selection
                    </MenuItem>
                  )}
                  {options.map((option) => (
                    <MenuItem
                      key={option}
                      onClick={() => {
                        handleFilterSelect(filterType, option);
                        setCurrentFilterType(null);
                        setMenuAnchorEl(null);
                      }}
                      selected={selectedFilters[filterType] === option}
                      sx={{ fontSize: 13 }}
                    >
                      {option}
                    </MenuItem>
                  ))}
                </Menu>
              </Box>
            ))}
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Footer Buttons */}
          <Box display="flex" gap={1}>
            <Button
              fullWidth
              size="small"
              variant="outlined"
              color="secondary"
              onClick={clearAllFilters}
            >
              Clear All
            </Button>
            <Button
              fullWidth
              size="small"
              variant="contained"
              onClick={() => setIsOpen(false)}
            >
              Apply Filters
            </Button>
          </Box>
        </Paper>
      )}
    </Box>
  );
};

export default JobPostsFilter;
