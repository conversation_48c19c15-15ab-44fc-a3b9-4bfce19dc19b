/* eslint-disable @typescript-eslint/no-unused-vars */
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import {
  createsJobPost,
  getJobPosts,
  getJobPostWithId,
  updateJobPost
} from '../../apis/jobposts';

interface Organization {
  id: number;
  organization_id: string;
  name: string;
  email: string;
  mobile_number: string;
  pass_key: string;
  password: string;
  logo: string;
  status: boolean;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

interface JobStage {
  id: number;
  job_stage_id: string;
  sequence_order: number;
  is_required: boolean;
  is_active: boolean;
  title: string;
  description: string;
  forms_list: string[];
  action_by: string;
  stage_version: number;
  cloned_at: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

interface JobPost {
  id?: string;
  job_id: string;
  title: string;
  department: string;
  description: string;
  requirements: string[];
  salary_range: string;
  employment_type: string;
  location: string;
  status: string;
  version: number;
  application_start_date: string;
  application_end_date: string;
  organization: Organization;
  job_stages: JobStage[];
}

interface InitialState {
  errors: Record<string, string>;
  isLoading: boolean;
  loadingError: Record<string, string>;
  loadingSpinner: boolean;

  jobPost: JobPost;
  jobPostsList: JobPost[];
}

const initialState: InitialState = {
  errors: {},
  isLoading: false,
  loadingError: {},
  loadingSpinner: false,

  jobPost: {
    job_id: '',
    title: '',
    department: '',
    description: '',
    requirements: [],
    salary_range: '',
    employment_type: '',
    location: '',
    status: '',
    version: 0,
    application_start_date: '',
    application_end_date: '',
    organization: {
      id: 0,
      organization_id: '',
      name: '',
      email: '',
      mobile_number: '',
      pass_key: '',
      password: '',
      logo: '',
      status: false,
      created_at: '',
      updated_at: '',
      deleted_at: null
    },
    job_stages: []
  },
  jobPostsList: []
};

export const createsjobpost = createAsyncThunk(
  'createsjobpost',
  createsJobPost
);
export const updatejobpost = createAsyncThunk('updatejobpost', updateJobPost);
export const getjobposts = createAsyncThunk('getjobposts', getJobPosts);
export const getjobpostwithid = createAsyncThunk(
  'getjobpostwithid',
  getJobPostWithId
);

const jobpostSlice = createSlice({
  name: 'jobpost',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(createsjobpost.pending, (state) => {
        state.isLoading = true;
        state.loadingSpinner = true;
      })
      .addCase(createsjobpost.fulfilled, (state, action: any) => {
        state.isLoading = false;
        // const { stage } = action.payload;
        // state.stage = stage;
        state.loadingSpinner = false;
      })
      .addCase(createsjobpost.rejected, (state) => {
        state.isLoading = false;
        state.loadingSpinner = false;

        // state.loadingError = action.payload;
      });

    builder
      .addCase(updatejobpost.pending, (state) => {
        state.isLoading = true;
        state.loadingSpinner = true;
      })
      .addCase(updatejobpost.fulfilled, (state, action: any) => {
        state.isLoading = false;
        // const { data } = action.payload;
        // state.stage = data;
        state.loadingSpinner = false;
      })
      .addCase(updatejobpost.rejected, (state) => {
        state.isLoading = false;
        state.loadingSpinner = false;

        // state.loadingError = action.payload;
      });

    builder
      .addCase(getjobposts.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getjobposts.fulfilled, (state, action: any) => {
        state.isLoading = false;
        const { data, meta } = action.payload.data;
        console.log('action.payload', action.payload, data, meta);
        state.jobPostsList = data;
        // state.stagesList = action.payload.stages;
      })
      .addCase(getjobposts.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });

    builder
      .addCase(getjobpostwithid.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getjobpostwithid.fulfilled, (state, action: any) => {
        state.isLoading = false;
        const { data } = action.payload;
        state.jobPost = data;
        // const { stage } = action.payload;
        // state.stage = stage;
      })
      .addCase(getjobpostwithid.rejected, (state) => {
        state.isLoading = false;
        // state.loadingError = action.payload;
      });
  }
});

export default jobpostSlice.reducer;
