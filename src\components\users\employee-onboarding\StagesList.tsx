/* eslint-disable react/no-array-index-key */
import {
  Box,
  Typography,
  Button,
  IconButton,
  Stack,
  Paper,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody
} from '@mui/material';
import React from 'react';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { useNavigate } from 'react-router-dom';

const StagesList = ({ stagesListData }: any) => {
  const navigate = useNavigate();
  // const stagesListData = [
  //   {
  //     title: 'Stage-01',
  //     description: 'Description for Stage-01'
  //   },
  //   {
  //     title: 'Stage-02',
  //     description: 'Description for Stage-02'
  //   },
  //   {
  //     title: 'Stage-03',
  //     description: 'Description for Stage-03'
  //   },
  //   {
  //     title: 'Stage-04',
  //     description: 'Description for Stage-04'
  //   },
  //   {
  //     title: 'Stage-05',
  //     description: 'Description for Stage-05'
  //   },
  //   {
  //     title: 'Stage-06',
  //     description: 'Description for Stage-06'
  //   }
  // ];

  return (
    <Box sx={{ padding: '20px' }}>
      <Box mb={2}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          mb={2}
        >
          <Box>
            <Typography
              variant="h2"
              sx={{
                fontWeight: 'bold',
                background: 'linear-gradient(to right, #2563eb, #9333ea)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1,
                fontSize: { xs: '1.5rem', md: '1.75rem', lg: '2rem' }
              }}
            >
              Stages
            </Typography>
            <Typography variant="h6" color="text.secondary">
              Track your stage management
            </Typography>
          </Box>

          <Box
            sx={{
              transition: 'all 0.3s',
              '&:hover': {
                transform: 'scale(1.05)'
              }
            }}
          >
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              sx={{
                px: 4,
                py: 1.5,
                borderRadius: 3,
                // background: 'linear-gradient(to right, #0483ba, #09376B)',
                background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
                boxShadow: 3,
                textTransform: 'none',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 6,
                  background: 'linear-gradient(to right, #2563eb, #7c3aed)'
                }
              }}
              href="/users/employee-onboarding/create-stages"
            >
              CREATE STAGE
            </Button>
          </Box>
        </Stack>
      </Box>

      <Paper
        elevation={4}
        sx={{
          borderRadius: 4,
          overflow: 'hidden'
        }}
      >
        <Box sx={{ overflowX: 'auto' }}>
          <Table>
            <TableHead>
              <TableRow
                sx={{
                  background: 'linear-gradient(to right, #f9fafb, #f3f4f6)'
                }}
              >
                <TableCell
                  sx={{
                    fontSize: 12,
                    fontWeight: 500,
                    color: 'gray',
                    textTransform: 'uppercase'
                  }}
                >
                  Name
                </TableCell>
                <TableCell
                  sx={{
                    fontSize: 12,
                    fontWeight: 500,
                    color: 'gray',
                    textTransform: 'uppercase'
                  }}
                >
                  Description
                </TableCell>
                <TableCell
                  sx={{
                    fontSize: 12,
                    fontWeight: 500,
                    color: 'gray',
                    textTransform: 'uppercase'
                  }}
                >
                  Actions
                </TableCell>
              </TableRow>
            </TableHead>

            <TableBody>
              {[stagesListData]?.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={3} align="center">
                    <Typography variant="body2" color="text.secondary">
                      No stages found.
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                stagesListData?.map((stage: any, index: number) => (
                  <TableRow
                    key={stage.id}
                    sx={{
                      '&:hover': {
                        backgroundColor: '#f9fafb',
                        transition: 'background-color 0.3s ease-in-out'
                      },
                      animation: 'fadeInUp 0.6s ease-out forwards',
                      animationDelay: `${index * 100}ms`
                    }}
                  >
                    <TableCell>
                      <Typography
                        variant="body2"
                        fontWeight={500}
                        color="text.primary"
                      >
                        {stage.title}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        maxWidth="40ch"
                        dangerouslySetInnerHTML={{
                          __html: stage?.description
                        }}
                        sx={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical'
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        sx={{
                          color: '#2563eb', // text-blue-600
                          '&:hover': {
                            color: '#1e40af',
                            backgroundColor: '#eff6ff' // blue-50
                          }
                        }}
                        onClick={() =>
                          navigate(
                            `/users/employee-onboarding/edit-stage/${stage?.stage_id}`
                          )
                        }
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                      {/* <IconButton
                      color="primary"
                      onClick={() =>
                        navigate(
                          `/users/employee-onboarding/edit-stage/${stage?.stage_id}`
                        )
                      }
                    >
                      <EditIcon />
                    </IconButton> */}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </Box>
      </Paper>

      {/* <Box
        sx={{
          background: '#FFFFFF',
          border: 1,
          borderColor: '#A3A3A3',
          padding: '30px 20px'
          // marginTop: '20px'
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            margin: '10px 0px'
          }}
        >
          <Typography
            sx={{
              fontWeight: 600,
              color: '#27292D',
              fontSize: 'clamp(1rem, 1.5vw, 1.25rem)'
            }}
          >
            Stages
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            color="primary"
            href="/users/employee-onboarding/create-stages"
          >
            Create Stage
          </Button>
        </Box>
        {stagesListData?.map((stage: any, index: number) => (
          <Box
            key={index}
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '10px 0',
              borderBottom: '1px solid #E0E0E0'
            }}
          >
            <Box>
              <Typography sx={{ fontWeight: 600 }}>{stage.title}</Typography>
              <Typography
                component="div"
                dangerouslySetInnerHTML={{ __html: stage?.description }}
                sx={{ fontSize: '14px', color: '#555' }}
              />
            </Box>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton
                color="primary"
                onClick={() =>
                  navigate(
                    `/users/employee-onboarding/edit-stage/${stage?.stage_id}`
                  )
                }
              >
                <EditIcon />
              </IconButton>
            </Box>
          </Box>
        ))}
        {stagesListData?.length === 0 && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100px',
              width: '100%',
              backgroundColor: 'lightgray',
              borderRadius: '6px'
            }}
          >
            <Typography
              sx={{
                fontSize: '16px',
                fontWeight: '500',
                color: 'text.secondary'
              }}
            >
              Currently there are no stages available.
            </Typography>
          </Box>
        )}
      </Box> */}
    </Box>
  );
};

export default StagesList;
