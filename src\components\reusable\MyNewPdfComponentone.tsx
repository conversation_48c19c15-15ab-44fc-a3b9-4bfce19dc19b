/* eslint-disable no-param-reassign */
import {
  Page,
  Text,
  View,
  Document,
  Image,
  Font,
  StyleSheet
} from '@react-pdf/renderer';
import SignatureRow from './Signature';
import Checkbox from './Checkbox';
import montserratFont from '../../assets/fonts.ttf';

// Styles for the PDF
const styles = StyleSheet.create({
  body: {
    paddingTop: 35,
    paddingBottom: 65,
    paddingHorizontal: 35,
    backgroundColor: '#FFFFFF'
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    textDecoration: 'underline',
    marginLeft: 10
  },
  header: {
    textAlign: 'center',
    color: 'grey'
  },
  logoContainer: {
    marginBottom: 10,
    alignItems: 'flex-end'
  },
  logo: {
    width: 100,
    height: 30,
    objectFit: 'contain',
    marginBottom: 12
  },
  value: {
    marginLeft: '15px'
  },
  footer: {
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10
  },
  footerText: {
    fontSize: 14,
    textAlign: 'center',
    color: 'black',
    fontWeight: 600,
    fontFamily: 'Montserrat'
  },
  page: {
    position: 'relative'
  },
  background: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0
  },
  text: {
    margin: 10,
    fontSize: 12
  },
  section: {
    marginBottom: 30
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    marginLeft: '10px'
  },
  field: {
    marginBottom: 4,
    flexDirection: 'row',
    lineHeight: 0.7
  },
  label: {
    fontWeight: 'bold',
    width: 200,
    marginLeft: '35px'
  },
  container: {
    padding: 10
  },
  image: {
    width: 200,
    height: 200,
    borderRadius: 4,
    marginTop: 8
  },
  signature: {
    width: 150,
    height: 50,
    borderWidth: 1,
    borderColor: '#ccc',
    marginTop: 8
  },
  watermark: {
    position: 'absolute',
    top: '40%',
    left: '25%',
    fontSize: 50,
    color: 'rgba(0, 0, 0, 0.1)',
    opacity: 0.1,
    transform: 'rotate(-45deg)',
    zIndex: -1
  },
  pageNumberStyles: {
    fontSize: 11,
    color: 'black',
    fontFamily: 'Montserrat'
  },
  formName: {
    fontSize: 20,
    fontWeight: 600,
    marginBottom: 10,
    backgroundColor: '#cee4f4',
    height: '50px'
  },
  formNameText: {
    marginLeft: '10px',
    marginTop: '10px'
  },
  dateRowContainer: {
    display: 'flex',
    flexDirection: 'row', // horizontal side by side
    justifyContent: 'space-between', // space between the dates
    marginBottom: 10,
    marginRight: '130px'
  },
  dateText: {
    display: 'flex',
    fontSize: 16,
    fontWeight: 'bold'
  }
});
Font.registerEmojiSource({
  format: 'png',
  url: 'https://cdnjs.cloudflare.com/ajax/libs/twemoji/14.0.2/72x72/%27'
});

Font.register({
  family: 'Montserrat',
  src: montserratFont
});

const MyNewPDFComponent = ({
  formResponses,
  orientationValue,
  sizeValue,
  breakValue,
  marginValue,
  // logoAlignment,
  // colorScheme,
  watermarkText,
  alignPageNumberText,
  insertLogo,
  headerTitle,
  fontFamily, // Add these new props
  fontSize,
  textColor,
  watermarkImage,
  headerHeight,
  // headerWidth,
  footerHeight,
  footerText
}: {
  formResponses: any;
  orientationValue: any;
  sizeValue: any;
  breakValue: string;
  marginValue: any;
  // logoAlignment: any;
  // colorScheme: any;
  // backgroundColor: string;
  watermarkText: string;
  // alignText: any;
  alignPageNumberText: any;
  insertLogo: any;
  headerTitle: any;
  fontFamily: string;
  fontSize: string;
  textColor: string;
  watermarkImage: any;
  headerHeight: any;
  footerHeight: any;
  footerText: any;
}) => {
  const marginStyles: any = {
    Normal: {
      marginTop: 0,
      marginBottom: 0,
      marginLeft: 0,
      marginRight: 0
    },
    Narrow: {
      marginTop: 20,
      marginBottom: 20,
      marginLeft: 20,
      marginRight: 20
    },
    Wide: { marginTop: 60, marginBottom: 60, marginLeft: 60, marginRight: 60 }
  };

  // const alignmentStyles: any = {
  //   center: { alignItems: 'center' },
  //   start: { alignItems: 'flex-start' },
  //   end: { alignItems: 'flex-end' }
  // };

  const alignPageTextStyles: any = {
    center: { textAlign: 'center' },
    left: { textAlign: 'left' },
    right: { textAlign: 'right' }
  };

  // const colorSchemes: any = {
  //   White: { backgroundColor: '#FFFFFF', color: '#000000' },
  //   Black: { backgroundColor: '#000000', color: '#FFFFFF' },
  //   Green: { backgroundColor: 'green', color: '#000000' }
  // };

  const renderFieldValue = (field: any, value: any, signatures: any) => {
    let normalizedType = '';
    if (field.input_type === 'checkbox') {
      normalizedType = 'checkbox';
    } else if (field.type === 'image') {
      normalizedType = 'image';
    } else if (field.type === 'signature') {
      normalizedType = 'signature';
    }
    // switch(field.input_type || field.type)
    switch (normalizedType) {
      case 'image': {
        const cleanBase64 = value?.file?.replace(
          /^data:image\/\w+;base64,/,
          ''
        );
        const changedMineType = `data:${value?.mimetype};base64,${cleanBase64}`;
        return (
          <View style={[styles.field, { marginBottom: 25 }]}>
            <Text style={styles.label}>{field.label}</Text>
            <Image src={changedMineType} style={styles.image} />
          </View>
        );
      }
      case 'signature': {
        signatures.push({
          label: field.label,
          value: value || null
        });

        if (signatures.length === 2) {
          const tempSignatures = [...signatures];
          signatures.length = 0;
          return (
            <View wrap={false} style={{ marginBottom: 25 }}>
              <SignatureRow signatures={tempSignatures} />
            </View>
          );
        }
        // If not returning a SignatureRow, return null
        return null;
      }
      case 'checkbox': {
        return (
          <View wrap={false} style={{ marginBottom: 30 }}>
            <Text style={styles.label}>{field.label}</Text>
            <View
              style={{
                flexDirection: field.label ? 'column' : 'row',
                flexWrap: field.label ? 'nowrap' : 'wrap',
                marginLeft: field.label ? '43.5%' : 29,
                gap: 5
              }}
            >
              {field.options.map((option: any, optionIndex: any) => (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'flex-start',
                    marginBottom: 18,
                    width: field.label ? 'auto' : '30%',
                    marginRight: field.label ? 0 : 10
                  }}
                  key={`${optionIndex + 1}`}
                >
                  <Checkbox checked={value?.includes(option.value)} />
                  <Text style={styles.value}>{option.label}</Text>
                </View>
              ))}
            </View>
          </View>
        );
      }
      default:
        return <Text>Image not rendered</Text>;
    }
  };

  const renderPage = () => {
    const commonStyles = {
      ...styles.body,
      marginTop: marginStyles[marginValue]?.marginTop,
      marginBottom: marginStyles[marginValue]?.marginBottom,
      marginLeft: marginStyles[marginValue]?.marginLeft,
      marginRight: marginStyles[marginValue]?.marginRight,
      fontFamily,
      fontSize,
      color: textColor,
      padding: 10,
      width: '100%'
    };

    switch (breakValue) {
      case 'TextWrap':
        return formResponses?.map((formResponse: any, index: number) => {
          const formData = formResponse?.form;
          const formResponseValues = formResponse?.values;
          return (
            <Page
              key={formResponse?.form_value_id || index}
              style={commonStyles}
              orientation={orientationValue.toLowerCase()}
              size={sizeValue}
              wrap
            >
              <View
                style={[
                  styles.header,
                  // alignmentStyles[logoAlignment],
                  {
                    height: headerHeight,
                    flexDirection: 'row',
                    alignItems: 'center', // Aligns items vertically in the center
                    paddingHorizontal: 10 // Adds spacing on both sides
                  }
                ]}
                fixed
              >
                <Image
                  src={insertLogo}
                  style={[styles.logo, { width: 50, height: 50 }]}
                />
                <Text
                  fixed
                  style={{
                    fontSize: 20,
                    color: '#595959',
                    marginBottom: 10,
                    fontFamily: 'Montserrat',
                    fontWeight: 'semibold'
                  }}
                >
                  {headerTitle}
                </Text>
              </View>

              <Text style={[styles.watermark]} fixed>
                {watermarkText?.trim() || ''}
              </Text>
              {/* Watermark Image */}
              {watermarkImage && (
                <Image style={styles.watermark} src={watermarkImage} />
              )}

              <View style={styles.container}>
                <View style={styles.formName}>
                  <Text style={styles.formNameText}>
                    {formResponse?.form?.name}
                  </Text>
                </View>
                {/* Iterate through form sections */}
                {Object.values(formData?.fields).map(
                  (sectionDetails: any, sectionIndex: number) => {
                    const signatures: any[] = [];

                    if (sectionDetails?.is_iterative_or_not) {
                      const sectionValues =
                        formResponseValues?.[sectionDetails.group_key];

                      if (
                        sectionValues &&
                        Array.isArray(sectionValues) &&
                        sectionValues.length > 0
                      ) {
                        return sectionValues.map(
                          (
                            sectionValuesData: any,
                            sectionValueIndex: number
                          ) => {
                            const sectionKey = `${sectionDetails.group_key}-${sectionValueIndex}`;
                            return (
                              <View style={[styles.section]} key={sectionKey}>
                                <Text style={styles.sectionTitle}>
                                  {`${sectionDetails.group_title} #${sectionValueIndex + 1}`}
                                </Text>

                                {sectionDetails.fields.map(
                                  (field: any, fieldIndex: number) => {
                                    const value =
                                      sectionValuesData?.[field.name] || 'N/A';

                                    if (field.type === 'image') {
                                      return renderFieldValue(field, value, '');
                                    }

                                    if (field.type === 'signature') {
                                      return renderFieldValue(
                                        field,
                                        value,
                                        signatures
                                      );
                                    }

                                    if (field.input_type === 'checkbox') {
                                      return renderFieldValue(field, value, '');
                                    }
                                    // Render other field types
                                    return (
                                      <View
                                        style={[
                                          styles.field,
                                          { marginBottom: 25 }
                                        ]}
                                        key={`${fieldIndex + 1}`}
                                      >
                                        <Text style={styles.label}>
                                          {field.label}
                                        </Text>

                                        <Text style={styles.value}>
                                          {value}
                                        </Text>
                                      </View>
                                    );
                                  }
                                )}

                                {/* Render any remaining unpaired signature */}
                                {signatures.length > 0 && (
                                  <View
                                    wrap={false}
                                    style={{ marginBottom: 25 }}
                                  >
                                    <SignatureRow signatures={signatures} />
                                  </View>
                                )}

                                {/* Render any remaining unpaired signature */}
                                <View style={styles.dateRowContainer}>
                                  {Object.entries(
                                    formResponseValues?.[
                                      sectionDetails.group_key
                                    ] || {}
                                  ).map(([key, val]: any) => {
                                    // Only render if key ends with _date and is not part of defined fields
                                    const isDateField = key.endsWith('_date');
                                    const dateOnly =
                                      typeof val === 'string'
                                        ? val.slice(0, 10)
                                        : val;
                                    const isAlreadyRendered =
                                      sectionDetails.fields.some(
                                        (field: any) => field.name === key
                                      );

                                    if (isDateField && !isAlreadyRendered) {
                                      return (
                                        <View style={styles.dateText} key={key}>
                                          <Text>Date:{dateOnly}</Text>
                                        </View>
                                      );
                                    }
                                    return null;
                                  })}
                                </View>
                              </View>
                            );
                          }
                        );
                      }
                    }
                    // Handle non-iterative (single instance) sections
                    return (
                      <View
                        style={[styles.section]}
                        key={`${sectionIndex + 1}`}
                      >
                        {/* Section title without index */}
                        <Text style={styles.sectionTitle}>
                          {sectionDetails.group_title}
                        </Text>

                        {/* Render fields */}
                        {sectionDetails.fields.map(
                          (field: any, fieldIndex: any) => {
                            const value =
                              formResponseValues?.[sectionDetails.group_key]?.[
                                field.name
                              ] || 'N/A';

                            if (field.type === 'image') {
                              return renderFieldValue(field, value, '');
                            }
                            if (field.type === 'signature') {
                              return renderFieldValue(field, value, signatures);
                            }

                            if (field.input_type === 'checkbox') {
                              return renderFieldValue(field, value, '');
                            }

                            // Render other field types
                            return (
                              <View
                                style={[styles.field, { marginBottom: 25 }]}
                                key={`${fieldIndex + 1}`}
                              >
                                <Text style={styles.label}>{field.label}</Text>

                                <Text style={styles.value}>{value}</Text>
                              </View>
                            );
                          }
                        )}

                        {/* Render any remaining unpaired signature */}
                        {signatures.length > 0 && (
                          <View wrap={false} style={{ marginBottom: 25 }}>
                            <SignatureRow signatures={signatures} />
                          </View>
                        )}

                        {/* Render extra _date fields */}
                        <View style={styles.dateRowContainer}>
                          {Object.entries(
                            formResponseValues?.[sectionDetails.group_key] || {}
                          ).map(([key, val]: any) => {
                            // Only render if key ends with _date and is not part of defined fields
                            const isDateField = key.endsWith('_date');
                            const dateOnly =
                              typeof val === 'string' ? val.slice(0, 10) : val;
                            const isAlreadyRendered =
                              sectionDetails.fields.some(
                                (field: any) => field.name === key
                              );

                            if (isDateField && !isAlreadyRendered) {
                              return (
                                <View style={styles.dateText} key={key}>
                                  <Text>Date:{dateOnly}</Text>
                                </View>
                              );
                            }
                            return null;
                          })}
                        </View>
                      </View>
                    );
                  }
                )}
              </View>
              <View
                style={[
                  styles.footer,
                  {
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    height: footerHeight,
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    paddingHorizontal: 10,
                    paddingVertical: 5
                  }
                ]}
                fixed
              >
                {footerText && (
                  <Text style={[styles.footerText, { marginRight: 0 }]}>
                    {typeof footerText === 'string' && footerText.length > 50
                      ? `${footerText.slice(0, 50)}`
                      : footerText}
                  </Text>
                )}
                <Text
                  style={[
                    styles.pageNumberStyles,
                    alignPageTextStyles[alignPageNumberText]
                  ]}
                  render={({ pageNumber, totalPages }) =>
                    `${footerText ? ' - ' : ''}Page ${pageNumber} of ${totalPages}`
                  }
                />
              </View>
            </Page>
          );
        });

      default:
        return (
          <Page
            style={commonStyles}
            orientation={orientationValue.toLowerCase()}
            size={sizeValue}
            wrap={breakValue === 'TextWrap'}
          >
            <View
              style={[
                styles.header,
                // alignmentStyles[logoAlignment],
                {
                  height: headerHeight,
                  flexDirection: 'row',
                  alignItems: 'center', // Aligns items vertically in the center
                  paddingHorizontal: 10 // Adds spacing on both sides
                }
              ]}
              fixed
            >
              <Image
                src={insertLogo}
                style={[styles.logo, { width: 60, height: 60 }]}
              />
              <Text
                fixed
                style={{
                  fontSize: 20,
                  color: '#595959',
                  marginBottom: 10,
                  fontFamily: 'Montserrat',
                  fontWeight: 'semibold'
                }}
              >
                {headerTitle}
              </Text>
            </View>
            <View
              style={{
                height: 2,
                backgroundColor: 'black',
                width: '100%',
                marginTop: 5,
                marginBottom: 10
              }}
            />

            <Text style={[styles.watermark]} fixed>
              {watermarkText?.trim() || ''}
            </Text>

            {watermarkImage && (
              <Image style={styles.watermark} src={watermarkImage} />
            )}

            {
              /* working lables */
              <View style={styles.container}>
                {formResponses?.map((formResponse: any, index: any) => {
                  const formData = formResponse?.form;
                  const formResponseValues = formResponse?.values;
                  return (
                    <View key={formData.id || index}>
                      <View style={styles.formName}>
                        <Text style={styles.formNameText}>
                          {formResponse?.form?.name}
                        </Text>
                      </View>

                      {/* Iterate through form sections */}
                      {Object.values(formData?.fields).map(
                        (sectionDetails: any, sectionIndex: any) => {
                          const signatures: any[] = [];

                          // Handle iterative sections (i.e., repeating groups of fields)
                          if (sectionDetails?.is_iterative_or_not) {
                            const sectionValues =
                              formResponseValues?.[sectionDetails.group_key];

                            // Check if data exists and is an array
                            if (
                              sectionValues &&
                              Array.isArray(sectionValues) &&
                              sectionValues.length > 0
                            ) {
                              return sectionValues.map(
                                (
                                  sectionValuesData: any,
                                  sectionValueIndex: any
                                ) => {
                                  const sectionKey = `${sectionDetails.group_key}-${sectionValueIndex}`;
                                  return (
                                    <View
                                      style={[styles.section]}
                                      key={sectionKey}
                                    >
                                      {/* Section title with index number */}
                                      <Text style={styles.sectionTitle}>
                                        {`${sectionDetails.group_title} #${sectionValueIndex + 1}`}
                                      </Text>

                                      {/* Render fields for each iteration */}
                                      {sectionDetails.fields.map(
                                        (field: any, fieldIndex: any) => {
                                          const value =
                                            sectionValuesData?.[field.name] ||
                                            'N/A';

                                          if (field.type === 'image') {
                                            return renderFieldValue(
                                              field,
                                              value,
                                              ''
                                            );
                                          }
                                          if (field.type === 'signature') {
                                            return renderFieldValue(
                                              field,
                                              value,
                                              signatures
                                            );
                                          }
                                          // Render checkbox options
                                          if (field.input_type === 'checkbox') {
                                            return renderFieldValue(
                                              field,
                                              value,
                                              ''
                                            );
                                          }

                                          // Render other field types
                                          return (
                                            <View
                                              style={[
                                                styles.field,
                                                { marginBottom: 25 }
                                              ]}
                                              key={`${fieldIndex + 1}`}
                                            >
                                              <Text style={styles.label}>
                                                {field.label}
                                              </Text>

                                              <Text style={styles.value}>
                                                {value}
                                              </Text>
                                            </View>
                                          );
                                        }
                                      )}

                                      {/* Render any remaining unpaired signature */}
                                      {signatures.length > 0 && (
                                        <View
                                          wrap={false}
                                          style={{ marginBottom: 25 }}
                                        >
                                          <SignatureRow
                                            signatures={signatures}
                                          />
                                        </View>
                                      )}
                                      {/* Render extra _date fields */}
                                      <View style={styles.dateRowContainer}>
                                        {Object.entries(
                                          sectionValuesData || {}
                                        ).map(([key, val]: any) => {
                                          const isDateField =
                                            key.endsWith('_date');
                                          const dateOnly =
                                            typeof val === 'string'
                                              ? val.slice(0, 10)
                                              : val;
                                          const isAlreadyRendered =
                                            sectionDetails.fields.some(
                                              (field: any) => field.name === key
                                            );

                                          if (
                                            isDateField &&
                                            !isAlreadyRendered
                                          ) {
                                            return (
                                              <View
                                                style={styles.dateText}
                                                key={key}
                                              >
                                                <Text>Date:{dateOnly}</Text>
                                              </View>
                                            );
                                          }
                                          return null;
                                        })}
                                      </View>
                                    </View>
                                  );
                                }
                              );
                            }
                            // No data available for iterative section
                            return null;
                          }

                          // Handle non-iterative (single instance) sections
                          return (
                            <View
                              style={[styles.section]}
                              key={`${sectionIndex + 1}`}
                            >
                              {/* Section title without index */}
                              <Text style={styles.sectionTitle}>
                                {sectionDetails.group_title}
                              </Text>

                              {/* Render fields */}
                              {sectionDetails.fields.map(
                                (field: any, fieldIndex: any) => {
                                  const value =
                                    formResponseValues?.[
                                      sectionDetails.group_key
                                    ]?.[field.name] || 'N/A';
                                  const isBase64Image =
                                    typeof value === 'string' &&
                                    value.startsWith('data:image/');
                                  const displayValue =
                                    typeof value === 'string' && !isBase64Image
                                      ? value
                                      : 'N/A';

                                  if (field.type === 'image') {
                                    return renderFieldValue(field, value, '');
                                  }
                                  if (field.type === 'signature') {
                                    return renderFieldValue(
                                      field,
                                      value,
                                      signatures
                                    );
                                  }

                                  // Render checkbox options
                                  if (field.input_type === 'checkbox') {
                                    return renderFieldValue(field, value, '');
                                  }

                                  // Render other field types
                                  return (
                                    <View
                                      style={[
                                        styles.field,
                                        { marginBottom: 25 }
                                      ]}
                                      key={`${fieldIndex + 1}`}
                                    >
                                      <Text style={styles.label}>
                                        {field.label}
                                      </Text>
                                      {isBase64Image ? (
                                        <Image
                                          src={value}
                                          style={styles.image}
                                        />
                                      ) : (
                                        <Text style={styles.value}>
                                          {displayValue}
                                        </Text>
                                      )}
                                    </View>
                                  );
                                }
                              )}

                              {/* Render any remaining unpaired signature */}
                              {signatures.length > 0 && (
                                <View wrap={false} style={{ marginBottom: 25 }}>
                                  <SignatureRow signatures={signatures} />
                                </View>
                              )}
                              {/* Render extra _date fields */}
                              <View style={styles.dateRowContainer}>
                                {Object.entries(
                                  formResponseValues?.[
                                    sectionDetails.group_key
                                  ] || {}
                                ).map(([key, val]: any) => {
                                  // Only render if key ends with _date and is not part of defined fields
                                  const isDateField = key.endsWith('_date');
                                  const dateOnly =
                                    typeof val === 'string'
                                      ? val.slice(0, 10)
                                      : val;
                                  const isAlreadyRendered =
                                    sectionDetails.fields.some(
                                      (field: any) => field.name === key
                                    );

                                  if (isDateField && !isAlreadyRendered) {
                                    return (
                                      <View style={styles.dateText} key={key}>
                                        <Text>Date:{dateOnly}</Text>
                                      </View>
                                    );
                                  }
                                  return null;
                                })}
                              </View>
                            </View>
                          );
                        }
                      )}
                    </View>
                  );
                })}
              </View>
            }
            <View
              style={[
                styles.footer,
                {
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  height: footerHeight,
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  paddingHorizontal: 10,
                  paddingVertical: 5
                }
              ]}
              fixed
            >
              {footerText && (
                <Text style={[styles.footerText, { marginRight: 0 }]}>
                  {footerText}
                </Text>
              )}
              <Text
                style={[
                  styles.pageNumberStyles,
                  alignPageTextStyles[alignPageNumberText]
                ]}
                render={({ pageNumber, totalPages }) =>
                  `${footerText ? ' - ' : ''}Page ${pageNumber} of ${totalPages}`
                }
              />
            </View>
          </Page>
        );
    }
  };
  return <Document>{renderPage()}</Document>;
};

export default MyNewPDFComponent;
