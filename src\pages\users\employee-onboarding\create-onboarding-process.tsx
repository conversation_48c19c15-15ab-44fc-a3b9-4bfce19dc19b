/* eslint-disable react/no-array-index-key */
// Package Imports
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Grid,
  IconButton,
  InputAdornment,
  Modal,
  Stack,
  TextField,
  Typography
} from '@mui/material';
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getIn, useFormikContext } from 'formik';
import SearchIcon from '@mui/icons-material/Search';
import TuneIcon from '@mui/icons-material/Tune';
import SwapVertIcon from '@mui/icons-material/SwapVert';

// Local Imports
import {
  AppForm,
  FormSelect,
  Icon,
  SubMenu,
  SubmitButton
} from '../../../components/form.elements';
import Shell from '../../../components/layout/Shell';
import LoaderUI from '../../../components/reusable/loaderUI';
import Sidebar from '../../../components/reusable/Sidebar';
import { UserNavBarModules } from '../../../components/users/UsersList';
import SearchWithFilters from '../../../components/reusable/SearchWithFilters';
import { toast } from 'react-toastify';
import { getjobposts } from '../../../redux/reducers/jobpost.reducer';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, AppState } from '../../../redux/app.store';
import JobPostsFilter from '../../../components/reusable/JobPostsFilter';
import {
  formatSalaryRange,
  getApplicationStatus,
  getTimeAgo
} from '../../../utils/functions';
import JobPostsFilterNew from '../../../components/reusable/JobPostsFilterNew';

const SelectForm = ({ jobPostsList, handleAlert }: any) => {
  const { values } = useFormikContext<any>();
  const [jobPostsOptions, setJobPostsOptions] = React.useState<any[]>([]);
  //   const jobPostsOptions = [
  //     { value: 'Job Post', label: 'Job Post' },
  //     { value: 'Caregiver', label: 'Caregiver' },
  //     { value: 'Onboarding', label: 'Onboarding' }
  //   ];

  // const jobPostsOptions = [
  //   { value: 'Job Post', label: 'Job Post' },
  //   {
  //     value: 'Caregiver - Elderly Support',
  //     label: 'Caregiver - Elderly Support'
  //   },
  //   {
  //     value: 'Caregiver - In-Home Patient Care',
  //     label: 'Caregiver - In-Home Patient Care'
  //   },
  //   { value: 'Live-in Caregiver', label: 'Live-in Caregiver' },
  //   { value: 'Caregiver - Dementia Care', label: 'Caregiver - Dementia Care' },
  //   {
  //     value: 'Caregiver - Child Special Needs',
  //     label: 'Caregiver - Child Special Needs'
  //   },
  //   {
  //     value: 'Caregiver - Post-Surgery Assistance',
  //     label: 'Caregiver - Post-Surgery Assistance'
  //   },
  //   {
  //     value: 'Caregiver - Parkinson’s Support',
  //     label: 'Caregiver - Parkinson’s Support'
  //   },
  //   {
  //     value: 'Caregiver - Elderly Couple',
  //     label: 'Caregiver - Elderly Couple'
  //   },
  //   { value: 'Part-Time Caregiver', label: 'Part-Time Caregiver' },
  //   {
  //     value: 'Caregiver - Stroke Recovery',
  //     label: 'Caregiver - Stroke Recovery'
  //   },
  //   {
  //     value: 'Caregiver - Senior Companion',
  //     label: 'Caregiver - Senior Companion'
  //   },
  //   {
  //     value: 'Caregiver - Alzheimer’s Care',
  //     label: 'Caregiver - Alzheimer’s Care'
  //   },
  //   {
  //     value: 'Caregiver - Female Patients',
  //     label: 'Caregiver - Female Patients'
  //   },
  //   {
  //     value: 'Caregiver - Overnight Shift',
  //     label: 'Caregiver - Overnight Shift'
  //   },
  //   {
  //     value: 'Caregiver - End of Life Support',
  //     label: 'Caregiver - End of Life Support'
  //   },
  //   {
  //     value: 'Caregiver - NRI Family Elder Support',
  //     label: 'Caregiver - NRI Family Elder Support'
  //   },
  //   {
  //     value: 'Caregiver - Hospital Discharge Support',
  //     label: 'Caregiver - Hospital Discharge Support'
  //   },
  //   {
  //     value: 'Caregiver - Physically Disabled Adults',
  //     label: 'Caregiver - Physically Disabled Adults'
  //   },
  //   {
  //     value: 'Caregiver - Temporary 1 Month',
  //     label: 'Caregiver - Temporary 1 Month'
  //   },
  //   {
  //     value: 'Caregiver - Wheelchair Assistance',
  //     label: 'Caregiver - Wheelchair Assistance'
  //   }
  // ];

  useEffect(() => {
    const options = [
      { value: 'Job Post', label: 'Job Post' },
      ...(jobPostsList?.map((job: any) => ({
        value: job?.job_id,
        label: job?.title
      })) || [])
    ];
    setJobPostsOptions(options || []);
  }, [jobPostsList]);

  return (
    <FormSelect
      name="jobPostType"
      label={
        getIn(values, 'jobPostType') === ''
          ? 'Select'
          : getIn(values, 'jobPostType')
      }
      data={jobPostsOptions}
      value="Select"
      onChange={(e: any) => {
        handleAlert(e);
      }}
      containerStyles={{
        // width: '500px',
        maxWidth: '600px',
        backgroundColor: 'red',
        marginTop: '20px',
        display: 'flex',
        alignItems: 'center',
        gap: '20px',
        flexDirection: 'row',
        border: '1px solid #A3A3A3',
        borderRadius: '300px',
        '& .MuiOutlinedInput-root .MuiSelect-outlined': {
          borderRadius: '300px',
          paddingLeft: '20px',
          backgroundColor: '#0483BA',
          color: '#fff',
          textTransform: 'uppercase',
          fontWeight: '500',
          '&:hover': {
            backgroundColor: '#0483BA97'
          }
        },
        '& .MuiOutlinedInput-root svg': {
          color: '#FFFFFF'
        }
      }}
      labelStyles={{
        // width: '200px',
        minWidth: '250px',
        paddingLeft: '20px',
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis'
      }}
    />
  );
};

const CreateOnboardingProcess: React.FC = () => {
  const navigate = useNavigate();
  const [showAlert, setShowAlert] = React.useState<{
    status: boolean;
    jobPost: string;
  }>({ status: false, jobPost: '' });
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading } = useSelector((state: AppState) => state.jobpost);
  const [jobData, setJobData] = React.useState<any>(null);
  const [showJobPost, setShowJobPost] = React.useState<{
    status: boolean;
    job: any;
  }>({ status: false, job: null });
  const { jobPostsList } = useSelector((state: AppState) => state.jobpost);

  const getSubMenu = () => {
    return <SubMenu backNavigation createStages />;
  };

  const menuItems = UserNavBarModules();

  const getDrawer = () => {
    return <Sidebar menuItems={menuItems} userType={undefined} />;
  };

  const handleSubmit = (values: any) => {
    navigate('/users/employee-onboarding/create-job-post');
    console.log('values; ', values);
  };

  const handleAlert = (e: any) => {
    const { value } = e.target;
    // setShowAlert({ status: true, jobPost: value });
    if (value !== 'Job Post') {
      setShowAlert({ status: true, jobPost: value });
    } else {
      setShowAlert({ status: false, jobPost: value });
    }
  };

  const getJobPostsList = async () => {
    const res = await dispatch(getjobposts({ page: 1, limit: 2 }));
    if (res.meta.requestStatus === 'rejected') {
      toast.error(res.payload?.message);
    } else {
      const {
        data: { data: jobPostsData },
        meta
      } = res.payload || {};
      console.log('LIst Data: ', res.payload, jobPostsData);
      if (meta?.totalCount === 0) {
        toast.error('No job posts found');
        return;
      }
      if (meta?.totalCount > 0) {
        toast.success(`${meta?.totalCount} job posts found`);
      }
      // Transform the data to match the expected format
      // Assuming res.payload.data is an array of job posts
      // If the API response structure is different, adjust accordingly
      // console.log('Job Posts Data: ', res.payload?.data);
      // console.log('Job Posts Data: ', res.payload?.data);
      // Transform the data to match the expected format
      // Assuming res.payload.data is an array of job posts
      // If the API response structure is different, adjust accordingly

      const mappedData = jobPostsData?.map((job: any) => {
        const jData = {
          id: job?.id,
          jobId: job?.job_id,
          title: job?.title,
          description: job?.description,
          location: job?.location,
          shift: job?.employment_type,
          salary: formatSalaryRange(job?.salary_range),
          requirements: job?.requirements,
          icon: 'FavoriteBorder',
          color: '#3b82f6',
          urgency: 'High',
          postedDate: getTimeAgo(job?.application_start_date),
          postEndDate: job?.application_end_date,
          applicants: 12
        };
        return jData;
      });
      setJobData(mappedData);
    }
  };
  useEffect(() => {
    if (jobPostsList?.length > 0) {
      setJobData(
        jobPostsList.map((job: any) => ({
          id: job.id,
          jobId: job.job_id,
          title: job.title,
          description: job.description,
          location: job.location,
          shift: job.employment_type,
          salary: formatSalaryRange(job.salary_range),
          requirements: job.requirements,
          icon: 'FavoriteBorder',
          color: '#3b82f6',
          urgency: getApplicationStatus(job.application_status),
          postedDate: getTimeAgo(job.application_start_date),
          postEndDate: job.application_end_date,
          applicants: 12
        }))
      );
    } else {
      getJobPostsList();
    }
  }, []);
  console.log('jobPostsList', jobPostsList);
  return (
    <Shell subMenu={getSubMenu()} showDrawer drawerData={getDrawer()}>
      {isLoading ? (
        <LoaderUI />
      ) : (
        <Box
          sx={{
            padding: '20px'
          }}
        >
          {/* UI: Model 1 */}
          {/* <Box
            sx={{
              background: '#FFFFFF',
              border: 1,
              borderColor: '#A3A3A3',
              padding: '50px 20px',
              marginTop: '20px',
              width: '100%'
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                gap: '20px',
                width: '100%'
              }}
            >
              <Box>
                <Typography
                  sx={{
                    fontWeight: 600,
                    color: '#27292D',
                    fontSize: 'clamp(1rem, 1.5vw, 1.25rem)'
                  }}
                >
                  Create Onboarding Process
                </Typography>
                <AppForm
                  initialValues={{ jobPostType: '' }}
                  onSubmit={handleSubmit}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '20px'
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: '20px'
                      }}
                    >
                      <Box
                        sx={{
                          position: 'relative',
                          minWidth: 600
                        }}
                      >
                        <SelectForm handleAlert={handleAlert} />
                        <Typography
                          sx={{
                            fontWeight: 600,
                            color: '#FFFFFF',
                            position: 'absolute',
                            left: '290px',
                            top: '33px'
                          }}
                        >
                          Select / Create New Job Post
                        </Typography>
                      </Box>
                      {showAlert.jobPost === 'Job Post' && (
                        <SubmitButton title="Create" sx={{ width: 100 }} />
                      )}
                    </Box>
                    <Modal
                      open={showAlert.status}
                      onClose={() =>
                        setShowAlert((prevState) => ({
                          ...prevState,
                          status: false
                        }))
                      }
                    >
                      <Box
                        sx={{
                          position: 'absolute',
                          top: '50%',
                          left: '50%',
                          transform: 'translate(-50%, -50%)',
                          boxShadow: 24,
                          p: 4,
                          borderRadius: '10px',
                          overflow: 'auto',
                          backgroundColor: '#FFFFFF'
                        }}
                      >
                        <Box sx={{ marginTop: '10px', maxWidth: '900px' }}>
                          <Typography
                            sx={{
                              fontSize: '18px',
                              fontWeight: '600',
                              color: '#0483BA',
                              textAlign: 'center'
                            }}
                          >
                            {' '}
                            ALERT!!!{' '}
                          </Typography>
                          <Box
                            sx={{
                              marginTop: '20px',
                              border: '1px solid #0483BA',
                              borderRadius: '10px',
                              backgroundColor: '#FAF9F8',
                              width: '450px',
                              padding: '20px'
                            }}
                          >
                            <Typography
                              sx={{
                                fontSize: '16px',
                                fontWeight: '600',
                                color: '#000000',
                                marginBottom: '16px',
                                textAlign: 'center'
                              }}
                            >
                              {' '}
                              {showAlert.jobPost === 'Job Post'
                                ? 'Are you sure, You want to create new Job Post ?'
                                : 'Are you sure, You want to clone this Job Post ?'}
                            </Typography>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '10px'
                              }}
                            >
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  gap: '10px'
                                }}
                              >
                                <Button
                                  variant="outlined"
                                  onClick={() =>
                                    setShowAlert((prevstate: any) => ({
                                      ...prevstate,
                                      status: false
                                    }))
                                  }
                                >
                                  Cancel
                                </Button>
                              </Box>
                              {showAlert.jobPost === 'Job Post' ? (
                                <Button
                                  variant="contained"
                                  onClick={() => {
                                    setShowAlert((prevstate: any) => ({
                                      ...prevstate,
                                      status: false
                                    }));
                                    navigate(
                                      '/users/employee-onboarding/create-job-post'
                                    );
                                  }}
                                >
                                  Create
                                </Button>
                              ) : (
                                <Button
                                  variant="contained"
                                  onClick={() => {
                                    setShowAlert((prevstate: any) => ({
                                      ...prevstate,
                                      status: false
                                    }));
                                    navigate(
                                      '/users/employee-onboarding/create-job-post'
                                    );
                                  }}
                                >
                                  Clone
                                </Button>
                              )}
                            </Box>
                          </Box>
                        </Box>
                      </Box>
                    </Modal>
                  </Box>
                </AppForm>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                <JobPostsFilter
                  onFilterChange={(filters) => console.log(filters)}
                />
                <TextField
                  variant="outlined"
                  placeholder="Search Job Posts"
                  size="small"
                  sx={{ width: '200px' }}
                  InputProps={{
                    style: {
                      fontSize: '14px',
                      color: '#2563eb',
                      // borderRadius: '8px',
                      // border: '1px solid #2563eb'
                      outline: '1px solid #2563eb'
                    },
                    startAdornment: (
                      <InputAdornment
                        position="start"
                        sx={{ color: '#2563eb' }}
                      >
                        <Icon name="Search" />
                      </InputAdornment>
                    )
                  }}
                  InputLabelProps={{
                    style: {
                      fontSize: '14px',
                      color: '#2563eb'
                    }
                  }}
                  onChange={(e) => handleAlert(e)}
                  value={showAlert.jobPost}
                />
              </Box>
            </Box>

            <Box
              sx={{
                marginTop: '20px'
              }}
            >
              <Box
                sx={{
                  background: 'linear-gradient(to right, #2563eb, #9333ea)', // from-blue-600 to-purple-600
                  borderRadius: '16px', // rounded-2xl
                  padding: '32px', // p-8
                  color: '#fff'
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  }}
                >
                  <Box>
                    <Typography variant="h5" fontWeight="bold" gutterBottom>
                      Job Posts
                    </Typography>
                    <Typography sx={{ opacity: 0.9 }}>
                      High-priority positions with immediate start dates
                    </Typography>
                  </Box>
                </Box>
              </Box>
              <Grid container spacing={3} mt={0}>
                {jobData?.map((job: any) => {
                  // const IconComponent = job.icon;
                  console.log('Jov Data: ', job);
                  return (
                    <Grid item xs={12} md={6} xl={4} key={job.id}>
                      <Box
                        sx={{
                          backgroundColor: '#fff',
                          borderRadius: '12px',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                          transition: 'all 0.3s ease',
                          p: 3,
                          border: '1px solid #f3f4f6',
                          position: 'relative',
                          overflow: 'hidden',
                          '&:hover': {
                            boxShadow: '0 8px 20px rgba(0,0,0,0.1)'
                          }
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            mb: 2
                          }}
                        >
                          <Box sx={{ display: 'flex', gap: 2 }}>
                            <Box
                              sx={{
                                backgroundColor: job.color,
                                p: 1.5,
                                borderRadius: '8px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            >
                              <Icon
                                name="Assignment"
                                style={{ color: '#fff', width: 24, height: 24 }}
                              />
                            </Box>
                            <Box>
                              <Typography
                                variant="subtitle1"
                                fontWeight={600}
                                color="text.primary"
                              >
                                {job.title}
                              </Typography>
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  color: '#6b7280',
                                  fontSize: 14,
                                  mt: 0.5
                                }}
                              >
                                <Icon
                                  name="LocationOn"
                                  style={{
                                    width: 16,
                                    height: 16,
                                    marginRight: 4
                                  }}
                                />
                                <span>{job.location}</span>
                              </Box>
                            </Box>
                          </Box>
                        </Box>
                        <Typography
                          component="div"
                          dangerouslySetInnerHTML={{
                            __html: job?.description || '<p></p>'
                          }}
                          sx={{
                            color: '#4b5563',
                            mb: 2,
                            fontSize: 14,
                            lineHeight: 1.6,
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            minHeight: 45,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis'
                            // '& p': {
                            //   display: '-webkit-box',
                            //   WebkitLineClamp: 2,
                            //   WebkitBoxOrient: 'vertical',
                            //   minHeight: 45,
                            //   overflow: 'hidden',
                            //   textOverflow: 'ellipsis'
                            // }
                          }}
                        />

                        <Box sx={{ mb: 2 }}>
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              fontSize: 14,
                              mb: 1
                            }}
                          >
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                color: '#6b7280'
                              }}
                            >
                              <Icon
                                name="AccessAlarm"
                                style={{
                                  width: 16,
                                  height: 16,
                                  marginRight: 6
                                }}
                              />
                              <span>{job.shift}</span>
                            </Box>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                color: '#16a34a',
                                fontWeight: 600
                              }}
                            >
                              <span>{job.salary}</span>
                            </Box>
                          </Box>
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              fontSize: 13,
                              color: '#6b7280'
                            }}
                          >
                            <span>{job.postedDate}</span>
                            <span>{job.applicants} applicants</span>
                          </Box>
                        </Box>

                        <Box
                          sx={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: 1,
                            mb: 2
                          }}
                        >
                          {job?.requirements?.length === 0 ||
                            (job?.requirements === null && (
                              <Typography
                                sx={{
                                  color: '#6b7280',
                                  fontSize: 12,
                                  width: '100%',
                                  height: 82,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  backgroundColor: '#f3f4f6',
                                  borderRadius: '8px'
                                  // marginBottom: '16px'
                                }}
                              >
                                No requirements specified
                              </Typography>
                            ))}
                          {job?.requirements
                            ?.slice(0, 2)
                            ?.map((req: any, index: number) => (
                              <Chip
                                key={index}
                                label={req}
                                size="small"
                                sx={{
                                  backgroundColor: '#eff6ff',
                                  color: '#1d4ed8',
                                  fontSize: 12
                                }}
                              />
                            ))}
                          {job?.requirements?.length > 2 && (
                            <Typography sx={{ color: '#2563eb', fontSize: 12 }}>
                              +{job.requirements.length - 2} more
                            </Typography>
                          )}
                        </Box>
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'flex-end',
                            gap: 1,
                            mt: 2
                          }}
                        >
                          <IconButton
                            aria-label="edit"
                            sx={{
                              backgroundColor: '#e0f2fe',
                              color: '#0284c7',
                              '&:hover': {
                                backgroundColor: '#bae6fd'
                              }
                            }}
                            onClick={() =>
                              navigate(
                                `/users/employee-onboarding/edit-job-post/${job?.jobId}`
                              )
                            }
                          >
                            <Icon name="Edit" />
                          </IconButton>

                          <IconButton
                            aria-label="view"
                            sx={{
                              backgroundColor: '#fee2e2',
                              color: '#dc2626',
                              '&:hover': {
                                backgroundColor: '#fecaca'
                              }
                            }}
                            onClick={() =>
                              setShowJobPost({ status: true, job })
                            }
                          >
                            <Icon name="Visibility" />
                          </IconButton>
                        </Box>
                      </Box>
                    </Grid>
                  );
                })}
              </Grid>
              <Modal
                open={showJobPost.status}
                onClose={() =>
                  setShowJobPost((prevState) => ({
                    ...prevState,
                    status: false
                  }))
                }
              >
                <Box
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    boxShadow: 24,
                    // p: 4,
                    borderRadius: '10px',
                    overflow: 'auto',
                    backgroundColor: '#FFFFFF'
                  }}
                >
                  <Box sx={{ marginTop: '0px', maxWidth: '1200px' }}>
                    <Box
                      sx={{
                        // marginTop: '20px',
                        border: '3px solid #0483BA',
                        borderRadius: '10px',
                        backgroundColor: '#FAF9F8',
                        width: '850px',
                        padding: '20px'
                      }}
                    >
                      <Box
                        sx={{
                          backgroundColor: '#fff',
                          borderRadius: '12px',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                          transition: 'all 0.3s ease',
                          p: 3,
                          border: '1px solid #f3f4f6',
                          position: 'relative',
                          overflow: 'hidden',
                          '&:hover': {
                            boxShadow: '0 8px 20px rgba(0,0,0,0.1)'
                          }
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            mb: 2
                          }}
                        >
                          <Box sx={{ display: 'flex', gap: 2 }}>
                            <Box
                              sx={{
                                backgroundColor: showJobPost?.job?.color,
                                p: 1.5,
                                borderRadius: '8px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            >
                              <Icon
                                name="Assignment"
                                style={{ color: '#fff', width: 24, height: 24 }}
                              />
                            </Box>
                            <Box>
                              <Typography
                                variant="subtitle1"
                                fontWeight={600}
                                color="text.primary"
                              >
                                {showJobPost?.job?.title}
                              </Typography>
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  color: '#6b7280',
                                  fontSize: 14,
                                  mt: 0.5
                                }}
                              >
                                <Icon
                                  name="LocationOn"
                                  style={{
                                    width: 16,
                                    height: 16,
                                    marginRight: 4
                                  }}
                                />
                                <span>{showJobPost?.job?.location}</span>
                              </Box>
                            </Box>
                          </Box>
                        </Box>
                        <Typography
                          component="div"
                          dangerouslySetInnerHTML={{
                            __html: showJobPost?.job?.description || '<p></p>'
                          }}
                          sx={{
                            color: '#4b5563',
                            mb: 2,
                            fontSize: 14,
                            lineHeight: 1.6
                            // display: '-webkit-box',
                            // WebkitLineClamp: 2,
                            // WebkitBoxOrient: 'vertical',
                            // minHeight: 45,
                            // overflow: 'hidden',
                            // textOverflow: 'ellipsis'
                          }}
                        />

                        <Box sx={{ mb: 2 }}>
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              fontSize: 14,
                              mb: 1
                            }}
                          >
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                color: '#6b7280'
                              }}
                            >
                              <Icon
                                name="AccessAlarm"
                                style={{
                                  width: 16,
                                  height: 16,
                                  marginRight: 6
                                }}
                              />
                              <span>{showJobPost?.job?.shift}</span>
                            </Box>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                color: '#16a34a',
                                fontWeight: 600
                              }}
                            >
                              <span>{showJobPost?.job?.salary}</span>
                            </Box>
                          </Box>
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              fontSize: 13,
                              color: '#6b7280'
                            }}
                          >
                            <span>{showJobPost?.job?.postedDate}</span>
                            <span>
                              {showJobPost?.job?.applicants} applicants
                            </span>
                          </Box>
                        </Box>

                        <Box
                          sx={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: 1,
                            mb: 2
                          }}
                        >
                          {showJobPost?.job?.requirements?.map(
                            (req: any, index: number) => (
                              <Chip
                                key={index}
                                label={req}
                                size="small"
                                sx={{
                                  backgroundColor: '#eff6ff',
                                  color: '#1d4ed8',
                                  fontSize: 12
                                }}
                              />
                            )
                          )}
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Modal>
            </Box>
          </Box> */}

          {/* UI: Model 2 */}
          <Box
            sx={{
              background: '#FFFFFF',
              border: 1,
              borderColor: '#A3A3A3',
              padding: '50px 20px',
              marginTop: '20px',
              width: '100%'
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                gap: '20px',
                width: '100%'
              }}
            >
              <Box>
                <Typography
                  sx={{
                    fontWeight: 600,
                    color: '#27292D',
                    fontSize: 'clamp(1rem, 1.5vw, 1.25rem)'
                  }}
                >
                  Create Onboarding Process
                </Typography>
                <AppForm
                  initialValues={{ jobPostType: '' }}
                  onSubmit={handleSubmit}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '20px'
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: '20px'
                      }}
                    >
                      <Box
                        sx={{
                          position: 'relative',
                          minWidth: 600
                        }}
                      >
                        <SelectForm
                          jobPostsList={jobPostsList}
                          handleAlert={handleAlert}
                        />
                        <Typography
                          sx={{
                            fontWeight: 600,
                            color: '#FFFFFF',
                            position: 'absolute',
                            left: '290px',
                            top: '33px'
                          }}
                        >
                          Select / Create New Job Post
                        </Typography>
                      </Box>
                      {showAlert.jobPost === 'Job Post' && (
                        <SubmitButton title="Create" sx={{ width: 100 }} />
                      )}
                    </Box>
                    <Modal
                      open={showAlert.status}
                      onClose={() =>
                        setShowAlert((prevState) => ({
                          ...prevState,
                          status: false
                        }))
                      }
                    >
                      <Box
                        sx={{
                          position: 'absolute',
                          top: '50%',
                          left: '50%',
                          transform: 'translate(-50%, -50%)',
                          boxShadow: 24,
                          p: 4,
                          borderRadius: '10px',
                          overflow: 'auto',
                          backgroundColor: '#FFFFFF'
                        }}
                      >
                        <Box sx={{ marginTop: '10px', maxWidth: '900px' }}>
                          <Typography
                            sx={{
                              fontSize: '18px',
                              fontWeight: '600',
                              color: '#0483BA',
                              textAlign: 'center'
                            }}
                          >
                            {' '}
                            ALERT!!!{' '}
                          </Typography>
                          <Box
                            sx={{
                              marginTop: '20px',
                              border: '1px solid #0483BA',
                              borderRadius: '10px',
                              backgroundColor: '#FAF9F8',
                              width: '450px',
                              padding: '20px'
                            }}
                          >
                            <Typography
                              sx={{
                                fontSize: '16px',
                                fontWeight: '600',
                                color: '#000000',
                                marginBottom: '16px',
                                textAlign: 'center'
                              }}
                            >
                              {' '}
                              {showAlert.jobPost === 'Job Post'
                                ? 'Are you sure, You want to create new Job Post ?'
                                : 'Are you sure, You want to clone this Job Post ?'}
                            </Typography>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '10px'
                              }}
                            >
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  gap: '10px'
                                }}
                              >
                                <Button
                                  variant="outlined"
                                  onClick={() =>
                                    setShowAlert((prevstate: any) => ({
                                      ...prevstate,
                                      status: false
                                    }))
                                  }
                                >
                                  Cancel
                                </Button>
                              </Box>
                              {showAlert.jobPost === 'Job Post' ? (
                                <Button
                                  variant="contained"
                                  onClick={() => {
                                    setShowAlert((prevstate: any) => ({
                                      ...prevstate,
                                      status: false
                                    }));
                                    navigate(
                                      '/users/employee-onboarding/create-job-post'
                                    );
                                  }}
                                >
                                  Create
                                </Button>
                              ) : (
                                <Button
                                  variant="contained"
                                  onClick={() => {
                                    setShowAlert((prevstate: any) => ({
                                      ...prevstate,
                                      status: false
                                    }));
                                    navigate(
                                      '/users/employee-onboarding/create-job-post'
                                    );
                                  }}
                                >
                                  Clone
                                </Button>
                              )}
                            </Box>
                          </Box>
                        </Box>
                      </Box>
                    </Modal>
                  </Box>
                </AppForm>
              </Box>
              {/* <Box sx={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                <JobPostsFilter
                  onFilterChange={(filters) => console.log(filters)}
                />
                <TextField
                  variant="outlined"
                  placeholder="Search Job Posts"
                  size="small"
                  sx={{ width: '200px' }}
                  InputProps={{
                    style: {
                      fontSize: '14px',
                      color: '#2563eb',
                      // borderRadius: '8px',
                      // border: '1px solid #2563eb'
                      outline: '1px solid #2563eb'
                    },
                    startAdornment: (
                      <InputAdornment
                        position="start"
                        sx={{ color: '#2563eb' }}
                      >
                        <Icon name="Search" />
                      </InputAdornment>
                    )
                  }}
                  InputLabelProps={{
                    style: {
                      fontSize: '14px',
                      color: '#2563eb'
                    }
                  }}
                  onChange={(e) => handleAlert(e)}
                  value={showAlert.jobPost}
                />
              </Box> */}
            </Box>

            <Box
              sx={{
                marginTop: '20px'
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
              >
                <Box>
                  <Typography variant="h5" fontWeight="bold" gutterBottom>
                    Job Posts Management
                  </Typography>
                  <Typography sx={{ opacity: 0.9 }}>
                    Manage your active job postings and track applications
                  </Typography>
                </Box>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  flexWrap: 'wrap',
                  marginTop: '16px'
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    border: '1px solid #e2e8f0',
                    // border: '1px solid #cbd5e1',
                    borderRadius: '8px',
                    // paddingLeft: 1.5,
                    backgroundColor: '#fff',
                    position: 'relative',
                    width: 300,
                    '& .MuiInputBase-root': {
                      fontSize: '14px'
                    },
                    '& .MuiInputBase-root.Mui-focused': {
                      borderColor: '#3b82f6',
                      boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.2)',
                      borderRadius: '8px'
                    },
                    '& .MuiInputBase-input:focus': {
                      outline: 'none'
                    }
                    // padding: '10px 16px 10px 40px'
                  }}
                >
                  <SearchIcon
                    sx={{
                      position: 'absolute',
                      left: '12px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      color: '#9ca3af', // same as Tailwind's text-gray-400
                      width: 16,
                      height: 16
                    }}
                  />
                  <TextField
                    variant="standard"
                    placeholder="Search job posts..."
                    InputProps={{
                      disableUnderline: true,
                      sx: {
                        fontSize: '14px',
                        // ml: 1,
                        padding: '10px 16px 10px 40px',
                        color: '#000000', // Tailwind's text-gray-700
                        '& ::placeholder': {
                          color: 'rgba(0, 0, 0, 0.6)',
                          fontWeight: 500,
                          opacity: 1 // Ensure it's visible (default is 0.5)
                        }
                      }
                    }}
                    fullWidth
                  />
                </Box>

                <JobPostsFilterNew
                  onFilterChange={(filters) => console.log(filters)}
                />
              </Box>
              <Grid container spacing={3} mt={0}>
                {jobData?.map((job: any) => {
                  // const IconComponent = job.icon;
                  console.log('Jov Data: ', job);
                  return (
                    <Grid item xs={12} md={6} xl={6} key={job.id}>
                      <Box
                        sx={{
                          backgroundColor: '#fff',
                          borderRadius: '12px',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                          transition: 'all 0.3s ease',
                          p: 3,
                          border: '1px solid #f3f4f6',
                          position: 'relative',
                          overflow: 'hidden',
                          '&:hover': {
                            boxShadow: '0 8px 20px rgba(0,0,0,0.1)'
                          }
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            mb: 2
                          }}
                        >
                          <Box sx={{ display: 'flex', gap: 2 }}>
                            <Box
                              sx={{
                                backgroundColor: job.color,
                                p: 1.5,
                                borderRadius: '8px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                position: 'relative'
                              }}
                            >
                              <Icon
                                name="Assignment"
                                style={{ color: '#fff', width: 24, height: 24 }}
                              />
                              <Box
                                sx={{
                                  position: 'absolute',
                                  top: '-12px',
                                  left: '-16px',
                                  backgroundColor:
                                    getApplicationStatus(job?.postEndDate) ===
                                    'Inactive'
                                      ? '#fee2e2'
                                      : '#dcfce7',
                                  color:
                                    getApplicationStatus(job?.postEndDate) ===
                                    'Inactive'
                                      ? '#991b1b'
                                      : '#166534',
                                  // backgroundColor: '#ef4444',
                                  // px: 1.5,
                                  // py: 0.5,
                                  padding: '4px 8px',
                                  borderRadius: '6px',
                                  fontSize: '10px',
                                  fontWeight: 600,
                                  textTransform: 'uppercase'
                                  // borderBottomLeftRadius: '8px'
                                }}
                              >
                                {getApplicationStatus(job?.postEndDate)}
                              </Box>
                            </Box>
                          </Box>

                          <Box>
                            <Box
                              sx={{
                                backgroundColor: '#fef3c7',
                                color: '#92400e',
                                // backgroundColor: '#ef4444',
                                // px: 1.5,
                                // py: 0.5,
                                padding: '8px',
                                borderRadius: '6px',
                                fontSize: '12px',
                                fontWeight: 600,
                                textTransform: 'uppercase'
                                // borderBottomLeftRadius: '8px'
                              }}
                            >
                              12 Applications
                            </Box>
                          </Box>
                        </Box>
                        <Box
                          sx={{
                            mb: 2
                          }}
                        >
                          <Typography
                            variant="subtitle1"
                            fontWeight={600}
                            color="text.primary"
                            fontSize={18}
                          >
                            {job.title}
                          </Typography>
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '8px',
                              mt: 0.5
                            }}
                          >
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                color: '#6b7280',
                                fontSize: 15,
                                mr: 0.2
                              }}
                            >
                              <Icon
                                name="LocationOn"
                                style={{
                                  width: 16,
                                  height: 16,
                                  marginRight: 4
                                }}
                              />
                              <span>{job.location}</span>
                            </Box>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                color: '#6b7280',
                                fontSize: 15,
                                mr: 0.2
                              }}
                            >
                              <Icon
                                name="Star"
                                style={{
                                  width: 16,
                                  height: 16,
                                  marginRight: 4
                                }}
                              />
                              <span>{job.shift}</span>
                            </Box>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                color: '#6b7280',
                                fontSize: 15,
                                mr: 0.2
                              }}
                            >
                              <Icon
                                name="CheckCircle"
                                style={{
                                  width: 16,
                                  height: 16,
                                  marginRight: 4
                                }}
                              />
                              <span>{job.postedDate}</span>
                            </Box>
                          </Box>
                        </Box>
                        <Typography
                          component="div"
                          dangerouslySetInnerHTML={{
                            __html: job?.description || '<p></p>'
                          }}
                          sx={{
                            color: '#4b5563',
                            mb: 2,
                            fontSize: 14,
                            lineHeight: 1.6,
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            minHeight: 45,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis'
                            // '& p': {
                            //   display: '-webkit-box',
                            //   WebkitLineClamp: 2,
                            //   WebkitBoxOrient: 'vertical',
                            //   minHeight: 45,
                            //   overflow: 'hidden',
                            //   textOverflow: 'ellipsis'
                            // }
                          }}
                        />

                        <Typography
                          variant="h4"
                          fontWeight={500}
                          color="text.primary"
                          fontSize={15}
                          mb={1}
                        >
                          Requirements
                        </Typography>
                        <Box
                          sx={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: 1,
                            mb: 3,
                            minHeight: 82
                          }}
                        >
                          {job?.requirements?.length === 0 ||
                            (job?.requirements === null && (
                              <Typography
                                sx={{
                                  color: '#6b7280',
                                  fontSize: 12,
                                  width: '100%',
                                  height: 82,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  backgroundColor: '#f3f4f6',
                                  borderRadius: '8px'
                                  // marginBottom: '16px'
                                }}
                              >
                                No requirements specified
                              </Typography>
                            ))}
                          {job?.requirements
                            ?.slice(0, 2)
                            ?.map((req: any, index: number) => (
                              <Chip
                                key={index}
                                label={req}
                                size="small"
                                sx={{
                                  backgroundColor: '#eff6ff',
                                  color: '#1d4ed8',
                                  fontSize: 12
                                }}
                              />
                            ))}
                          {job?.requirements?.length > 2 && (
                            <Typography sx={{ color: '#2563eb', fontSize: 12 }}>
                              +{job.requirements.length - 2} more
                            </Typography>
                          )}
                        </Box>

                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            borderTop: '1px solid #f1f5f9',
                            paddingTop: '16px'
                          }}
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              color: '#16a34a',
                              fontWeight: 600
                            }}
                          >
                            <span>{job.salary}</span>
                          </Box>
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'flex-end',
                              gap: 1
                            }}
                          >
                            <IconButton
                              aria-label="edit"
                              sx={{
                                backgroundColor: '#e0f2fe',
                                color: '#0284c7',
                                '&:hover': {
                                  backgroundColor: '#bae6fd'
                                }
                              }}
                              onClick={() =>
                                navigate(
                                  `/users/employee-onboarding/edit-job-post/${job?.jobId}`
                                )
                              }
                            >
                              <Icon name="Edit" />
                            </IconButton>

                            <IconButton
                              aria-label="view"
                              sx={{
                                backgroundColor: '#fef3c7',
                                color: '#c15412',
                                '&:hover': {
                                  backgroundColor: '#fde895'
                                }
                              }}
                              onClick={() =>
                                setShowJobPost({ status: true, job })
                              }
                            >
                              <Icon name="Visibility" />
                            </IconButton>
                          </Box>
                        </Box>
                      </Box>
                    </Grid>
                  );
                })}
              </Grid>
              <Modal
                open={showJobPost.status}
                onClose={() =>
                  setShowJobPost((prevState) => ({
                    ...prevState,
                    status: false
                  }))
                }
              >
                <Box
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    boxShadow: 24,
                    // p: 4,
                    borderRadius: '10px',
                    overflow: 'auto',
                    backgroundColor: '#FFFFFF'
                  }}
                >
                  <Box sx={{ marginTop: '0px', maxWidth: '1200px' }}>
                    <Box
                      sx={{
                        // marginTop: '20px',
                        border: '3px solid #0483BA',
                        borderRadius: '10px',
                        backgroundColor: '#FAF9F8',
                        width: '850px',
                        padding: '20px'
                      }}
                    >
                      <Box
                        sx={{
                          backgroundColor: '#fff',
                          borderRadius: '12px',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                          transition: 'all 0.3s ease',
                          p: 3,
                          border: '1px solid #f3f4f6',
                          position: 'relative',
                          overflow: 'hidden',
                          '&:hover': {
                            boxShadow: '0 8px 20px rgba(0,0,0,0.1)'
                          }
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            mb: 2
                          }}
                        >
                          <Box sx={{ display: 'flex', gap: 2 }}>
                            <Box
                              sx={{
                                backgroundColor: showJobPost?.job?.color,
                                p: 1.5,
                                borderRadius: '8px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            >
                              <Icon
                                name="Assignment"
                                style={{ color: '#fff', width: 24, height: 24 }}
                              />
                            </Box>
                            <Box>
                              <Typography
                                variant="subtitle1"
                                fontWeight={600}
                                color="text.primary"
                              >
                                {showJobPost?.job?.title}
                              </Typography>
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  color: '#6b7280',
                                  fontSize: 14,
                                  mt: 0.5
                                }}
                              >
                                <Icon
                                  name="LocationOn"
                                  style={{
                                    width: 16,
                                    height: 16,
                                    marginRight: 4
                                  }}
                                />
                                <span>{showJobPost?.job?.location}</span>
                              </Box>
                            </Box>
                          </Box>
                        </Box>
                        <Typography
                          component="div"
                          dangerouslySetInnerHTML={{
                            __html: showJobPost?.job?.description || '<p></p>'
                          }}
                          sx={{
                            color: '#4b5563',
                            mb: 2,
                            fontSize: 14,
                            lineHeight: 1.6
                            // display: '-webkit-box',
                            // WebkitLineClamp: 2,
                            // WebkitBoxOrient: 'vertical',
                            // minHeight: 45,
                            // overflow: 'hidden',
                            // textOverflow: 'ellipsis'
                          }}
                        />

                        <Box sx={{ mb: 2 }}>
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              fontSize: 14,
                              mb: 1
                            }}
                          >
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                color: '#6b7280'
                              }}
                            >
                              <Icon
                                name="AccessAlarm"
                                style={{
                                  width: 16,
                                  height: 16,
                                  marginRight: 6
                                }}
                              />
                              <span>{showJobPost?.job?.shift}</span>
                            </Box>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                color: '#16a34a',
                                fontWeight: 600
                              }}
                            >
                              <span>{showJobPost?.job?.salary}</span>
                            </Box>
                          </Box>
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              fontSize: 13,
                              color: '#6b7280'
                            }}
                          >
                            <span>{showJobPost?.job?.postedDate}</span>
                            <span>
                              {showJobPost?.job?.applicants} applicants
                            </span>
                          </Box>
                        </Box>

                        <Box
                          sx={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: 1,
                            mb: 2
                          }}
                        >
                          {showJobPost?.job?.requirements?.map(
                            (req: any, index: number) => (
                              <Chip
                                key={index}
                                label={req}
                                size="small"
                                sx={{
                                  backgroundColor: '#eff6ff',
                                  color: '#1d4ed8',
                                  fontSize: 12
                                }}
                              />
                            )
                          )}
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Modal>
            </Box>

            {/* <Box
              sx={{
                marginTop: '20px'
              }}
            >
              <Box
                sx={{
                  background: 'linear-gradient(to right, #2563eb, #9333ea)', // from-blue-600 to-purple-600
                  borderRadius: '16px', // rounded-2xl
                  padding: '32px', // p-8
                  color: '#fff'
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  }}
                >
                  <Box>
                    <Typography variant="h5" fontWeight="bold" gutterBottom>
                      Job Posts
                    </Typography>
                    <Typography sx={{ opacity: 0.9 }}>
                      High-priority positions with immediate start dates
                    </Typography>
                  </Box>
                </Box>
              </Box>

              <Grid container spacing={3} mt={0}>
                {jobData?.map((job: any) => {
                  // const IconComponent = job.icon;
                  console.log('Jov Data: ', job);
                  return (
                    <Grid item xs={12} md={6} xl={4} key={job.id}>
                      <Box
                        sx={{
                          backgroundColor: '#fff',
                          borderRadius: '12px',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                          transition: 'all 0.3s ease',
                          p: 3,
                          border: '1px solid #f3f4f6',
                          position: 'relative',
                          overflow: 'hidden',
                          '&:hover': {
                            boxShadow: '0 8px 20px rgba(0,0,0,0.1)'
                          }
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            mb: 2
                          }}
                        >
                          <Box sx={{ display: 'flex', gap: 2 }}>
                            <Box
                              sx={{
                                backgroundColor: job.color,
                                p: 1.5,
                                borderRadius: '8px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            >
                              <Icon
                                name="Assignment"
                                style={{ color: '#fff', width: 24, height: 24 }}
                              />
                            </Box>
                            <Box>
                              <Typography
                                variant="subtitle1"
                                fontWeight={600}
                                color="text.primary"
                              >
                                {job.title}
                              </Typography>
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  color: '#6b7280',
                                  fontSize: 14,
                                  mt: 0.5
                                }}
                              >
                                <Icon
                                  name="LocationOn"
                                  style={{
                                    width: 16,
                                    height: 16,
                                    marginRight: 4
                                  }}
                                />
                                <span>{job.location}</span>
                              </Box>
                            </Box>
                          </Box>
                        </Box>
                        <Typography
                          component="div"
                          dangerouslySetInnerHTML={{
                            __html: job?.description || '<p></p>'
                          }}
                          sx={{
                            color: '#4b5563',
                            mb: 2,
                            fontSize: 14,
                            lineHeight: 1.6,
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            minHeight: 45,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis'
                            // '& p': {
                            //   display: '-webkit-box',
                            //   WebkitLineClamp: 2,
                            //   WebkitBoxOrient: 'vertical',
                            //   minHeight: 45,
                            //   overflow: 'hidden',
                            //   textOverflow: 'ellipsis'
                            // }
                          }}
                        />

                        <Box sx={{ mb: 2 }}>
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              fontSize: 14,
                              mb: 1
                            }}
                          >
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                color: '#6b7280'
                              }}
                            >
                              <Icon
                                name="AccessAlarm"
                                style={{
                                  width: 16,
                                  height: 16,
                                  marginRight: 6
                                }}
                              />
                              <span>{job.shift}</span>
                            </Box>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                color: '#16a34a',
                                fontWeight: 600
                              }}
                            >
                              <span>{job.salary}</span>
                            </Box>
                          </Box>
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              fontSize: 13,
                              color: '#6b7280'
                            }}
                          >
                            <span>{job.postedDate}</span>
                            <span>{job.applicants} applicants</span>
                          </Box>
                        </Box>

                        <Box
                          sx={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: 1,
                            mb: 2
                          }}
                        >
                          {job?.requirements?.length === 0 ||
                            (job?.requirements === null && (
                              <Typography
                                sx={{
                                  color: '#6b7280',
                                  fontSize: 12,
                                  width: '100%',
                                  height: 82,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  backgroundColor: '#f3f4f6',
                                  borderRadius: '8px'
                                  // marginBottom: '16px'
                                }}
                              >
                                No requirements specified
                              </Typography>
                            ))}
                          {job?.requirements
                            ?.slice(0, 2)
                            ?.map((req: any, index: number) => (
                              <Chip
                                key={index}
                                label={req}
                                size="small"
                                sx={{
                                  backgroundColor: '#eff6ff',
                                  color: '#1d4ed8',
                                  fontSize: 12
                                }}
                              />
                            ))}
                          {job?.requirements?.length > 2 && (
                            <Typography sx={{ color: '#2563eb', fontSize: 12 }}>
                              +{job.requirements.length - 2} more
                            </Typography>
                          )}
                        </Box>

                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'flex-end',
                            gap: 1,
                            mt: 2
                          }}
                        >
                          <IconButton
                            aria-label="edit"
                            sx={{
                              backgroundColor: '#e0f2fe',
                              color: '#0284c7',
                              '&:hover': {
                                backgroundColor: '#bae6fd'
                              }
                            }}
                            onClick={() =>
                              navigate(
                                `/users/employee-onboarding/edit-job-post/${job?.jobId}`
                              )
                            }
                          >
                            <Icon name="Edit" />
                          </IconButton>

                          <IconButton
                            aria-label="view"
                            sx={{
                              backgroundColor: '#fee2e2',
                              color: '#dc2626',
                              '&:hover': {
                                backgroundColor: '#fecaca'
                              }
                            }}
                            onClick={() =>
                              setShowJobPost({ status: true, job })
                            }
                          >
                            <Icon name="Visibility" />
                          </IconButton>
                        </Box>
                      </Box>
                    </Grid>
                  );
                })}
              </Grid>
            </Box> */}
          </Box>
        </Box>
      )}
    </Shell>
  );
};
export default CreateOnboardingProcess;
