// Global Imports
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Grid,
  // Pagination,
  Tooltip,
  Typography
} from '@mui/material';
import Box from '@mui/material/Box';
import {
  // useEffect
  useState
} from 'react';
import { useNavigate } from 'react-router-dom';
import MailIcon from '@mui/icons-material/Mail';
import Accordion from '@mui/material/Accordion';
import IconButton from '@mui/material/IconButton';
import { useDispatch, useSelector } from 'react-redux';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ControlPointIcon from '@mui/icons-material/ControlPoint';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import PhoneAndroidIcon from '@mui/icons-material/PhoneAndroid';
import { Widgets } from '@mui/icons-material';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import DeleteForeverOutlinedIcon from '@mui/icons-material/DeleteForeverOutlined';
import { toast } from 'react-toastify';

// Local Imports
import '../../css/user-list-styles.scss';

import Shell from '../layout/Shell';
import Sidebar from '../reusable/Sidebar';
// import { SnabackBarState } from '../../types';
import { SubMenu } from '../form.elements';
// import { SnackbarElement } from '../reusable/SnackbarElement';
import { AppDispatch, AppState } from '../../redux/app.store';
import { deleteuser } from '../../redux/reducers/user.reducer';
import LoaderUI from '../reusable/loaderUI';

export const UserNavBarModules = () => {
  const { user } = useSelector((state: AppState) => state.auth);

  if (!user?.apps) return [{ name: 'Employees', url: '/users' }];

  const navItems = [{ name: 'Employees', url: '/users' }];

  const appDetails = user.apps.map((app: any) => ({
    app_id: app.app_id,
    process_code: app.industry_app_process?.process_code
  }));

  if (appDetails.find((app) => app.process_code === 'HC_OBRD')) {
    const appId = appDetails.find(
      (app) => app.process_code === 'HC_OBRD'
    )?.app_id;

    localStorage.setItem('onboard-app-id', appId);

    navItems.push({
      name: 'New Applicants',
      url: `/users/employee-onboarding`
    });
  }

  if (appDetails.find((app) => app.process_code === 'HC_CLIASS')) {
    const appId = appDetails.find(
      (app) => app.process_code === 'HC_CLIASS'
    )?.app_id;

    localStorage.setItem('assesst-app-id', appId);

    navItems.push({
      name: 'Clients',
      url: `/users/client-assessment`
    });
  }

  if (appDetails.find((app) => app.process_code === 'HC_CARE')) {
    navItems.push({
      name: 'Caregivers',
      url: `/users/caregivers`
    });
  }

  navItems.push({
    name: 'Create Onboarding Process',
    url: `/users/employee-onboarding/create-onboarding-process`
  });

  return navItems;
};

const UsersList = ({
  usersList,
  organizationsList,
  fetchOrganizationsList,
  getUsersData
}: any) => {
  const navigate = useNavigate();
  const [orgExpanded, setOrgExpanded] = useState<string | false>(false);
  const [userExpanded, setUserExpanded] = useState<string | false>(false);
  // const [snackbarOpen, setSnackbarOpen] = useState<SnabackBarState>({
  //   status: false,
  //   message: '',
  //   type: 'success'
  // });
  // const [usersList, setUsersList]: any = useState();
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [deleteUserId, setDeleteUserId] = useState('');
  // const [defaultText, setDefaultText] = useState('');
  const dispatch = useDispatch<AppDispatch>();

  const { isLoading } = useSelector((state: AppState) => state.user);

  // const [activePage, setActivePage] = useState(1);
  // const itemsPerPage = 10;

  // useEffect(() => {
  //   dispatch(userPagination({ limit: itemsPerPage, page: activePage }));
  // }, [dispatch, activePage]);

  // const users = userPaginationData || [];

  // const totalPages =
  //   userPaginationData.length > 0
  //     ? Math.ceil(userPaginationData.length / itemsPerPage)
  //     : 0;

  // Fetch userType from localStorage
  const userType = localStorage.getItem('user_type');

  const handleChange =
    (panel: string) => (_event: React.SyntheticEvent, isExpanded: boolean) => {
      setOrgExpanded(isExpanded ? panel : false);
    };

  const handleUserChange =
    (panel: string) => (_event: React.SyntheticEvent, isExpanded: boolean) => {
      setUserExpanded(isExpanded ? panel : false);
    };

  // useEffect(() => {
  //   setUsersList(userList);
  //   if (userType === 'organization') {
  //     setDefaultText('Currently there are no users in your organization.');
  //   } else {
  //     setDefaultText('Currently there are no users.');
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [userList]);

  const handleOpenDeleteDialog = (id: any) => {
    setOpenDeleteDialog(true); // Open the dialog
    setDeleteUserId(id);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false); // Close the dialog
  };
  // const handleSnackbarClose = () => {
  //   setSnackbarOpen({
  //     status: false,
  //     message: '',
  //     type: 'success'
  //   });
  // };

  const confirmDeleteSection = async () => {
    try {
      const response = await dispatch(deleteuser(deleteUserId));
      if (response?.payload?.error) {
        // setSnackbarOpen({
        //   status: true,
        //   message: response?.payload?.error,
        //   type: 'error'
        // });
        toast.error(response?.payload?.error);
      } else if (response?.payload?.status) {
        handleCloseDeleteDialog(); // Close the dialog after action
        // setSnackbarOpen({
        //   status: true,
        //   message: response?.payload?.message,
        //   type: 'success'
        // });
        // toast.success(response?.payload?.message);
      }
    } catch (error: any) {
      // setSnackbarOpen({
      //   status: true,
      //   message:
      //     error?.message || 'Something Went Wrong Please try again later',
      //   type: 'error'
      // });
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }

    if (userType === 'super_admin') {
      fetchOrganizationsList();
    } else if (userType === 'organization') {
      getUsersData();
    }
  };

  // useEffect(() => {
  //   if (snackbarOpen.status) {
  //     const timer = setTimeout(() => {
  //       handleSnackbarClose();
  //     }, 3000); // 3 seconds

  //     return () => clearTimeout(timer); // Cleanup the timer
  //   }
  //   return () => {};
  // }, [snackbarOpen]);

  // const getApplistGridLength = (appCount: number) => {
  //   switch (appCount) {
  //     case 2:
  //       return 4;
  //     case 1:
  //       return 12;
  //     case 3:
  //     default:
  //       return 3;
  //   }
  // };

  const getApplistGridLength = (appCount: number) => {
    if (appCount === 1) return 12; // Full-width for a single app
    if (appCount === 2) return 6; // Two apps in a row
    if (appCount <= 3) return 4; // Three apps in a row
    return 3; // More than three apps
  };

  const content = (
    <Box
      sx={{
        background: '#FBF8F8',
        mt: userType === 'organization' ? 0 : 2,
        p: userType === 'organization' ? '0px' : '30px 60px',
        height: userType === 'organization' ? '100vh' : ''
      }}
      className="main-container"
    >
      <Box sx={{ width: '100%', overflowX: 'auto' }}>
        <Box sx={{ backgroundColor: '#FAF9F8' }}>
          <div>
            <Typography
              sx={{
                fontSize: '22px',
                fontWeight: '600',
                // marginTop: '50px',
                marginBottom: '10px',
                padding: '2px',
                marginLeft: '20px'
              }}
            >
              {userType === 'super_admin' ? 'Organizations' : 'Employees'}
            </Typography>
          </div>
        </Box>

        <Box className="content">
          <Box sx={{ p: 2 }}>
            {userType === 'super_admin' && organizationsList?.length === 0 && (
              <Box className="no-applicants">
                <Typography sx={{ fontSize: '17px', fontWeight: '500' }}>
                  No organizations found.
                </Typography>
              </Box>
            )}

            {userType === 'organization' && usersList?.length === 0 && (
              <Box className="no-applicants">
                <Typography sx={{ fontSize: '17px', fontWeight: '500' }}>
                  No records found.
                </Typography>
              </Box>
            )}

            {userType === 'super_admin'
              ? organizationsList &&
                organizationsList?.length > 0 &&
                organizationsList
                  ?.filter((org: any) => org?.user?.length > 0)
                  ?.map((organization: any) => {
                    return (
                      <Box key={organization?.organization_id} sx={{ mb: 0.5 }}>
                        <Accordion
                          expanded={
                            orgExpanded === organization?.organization_id
                          }
                          onChange={handleChange(organization?.organization_id)}
                          className="accordion"
                        >
                          <AccordionSummary
                            aria-controls="panel1-content"
                            id="panel1-header"
                            className="accordion-summary"
                            sx={{
                              backgroundColor:
                                orgExpanded === organization?.organization_id
                                  ? '#36C0ED'
                                  : 'white',
                              color:
                                orgExpanded === organization?.organization_id
                                  ? 'white'
                                  : 'black'
                            }}
                          >
                            {/* Left Icon and Organization Name */}
                            <Box className="left-box">
                              <IconButton>
                                {orgExpanded !==
                                organization?.organization_id ? (
                                  <ControlPointIcon
                                    sx={{
                                      color:
                                        orgExpanded ===
                                        organization?.organization_id
                                          ? 'white'
                                          : '#36C0ED'
                                    }}
                                  />
                                ) : (
                                  <RemoveCircleOutlineIcon
                                    sx={{
                                      color:
                                        orgExpanded ===
                                        organization?.organization_id
                                          ? 'white'
                                          : '#36C0ED'
                                    }}
                                  />
                                )}
                              </IconButton>
                              <Box
                                sx={{
                                  ml: '1rem',
                                  fontWeight: 500,
                                  overflowWrap: 'anywhere'
                                }}
                              >
                                <span>{organization.name}</span>
                              </Box>
                            </Box>

                            {/* Centered User Count */}
                            <Box className="center-box">
                              {organization?.user?.length > 1
                                ? `${organization?.user?.length || 0} Users`
                                : `${organization?.user?.length || 0} User`}
                            </Box>

                            {/* Right Edit and Delete Icons */}
                            <Box className="right-box">
                              <IconButton
                                sx={{ marginLeft: 1 }}
                                onClick={() =>
                                  navigate(
                                    `/org-setup/organization-registration/${organization?.organization_id}`
                                  )
                                }
                              >
                                <EditOutlinedIcon
                                  sx={{
                                    color:
                                      orgExpanded ===
                                      organization?.organization_id
                                        ? 'white'
                                        : '#36C0ED'
                                  }}
                                />
                              </IconButton>
                            </Box>
                          </AccordionSummary>

                          <AccordionDetails sx={{ background: '#FAF9F8' }}>
                            {organization?.user &&
                            organization?.user?.length === 0 ? (
                              <div> </div>
                            ) : (
                              organization?.user?.map((user: any) => {
                                return (
                                  <Accordion
                                    id="active-user"
                                    key={user?.user_id}
                                    expanded={userExpanded === user?.user_id}
                                    onChange={handleUserChange(user?.user_id)}
                                    sx={{ mb: 1 }}
                                  >
                                    <AccordionSummary
                                      aria-controls="panel1-content"
                                      id="panel1-header"
                                      className="sub-accordian-summary"
                                      sx={{
                                        bgcolor:
                                          userExpanded === user?.user_id
                                            ? '#36C0ED'
                                            : 'white',
                                        color:
                                          userExpanded === user?.user_id
                                            ? 'white'
                                            : 'inherit'
                                      }}
                                    >
                                      <Box className="accordion-box">
                                        <Grid
                                          container
                                          alignItems="center"
                                          spacing={2}
                                        >
                                          <Grid item xs={3}>
                                            <IconButton>
                                              {userExpanded !==
                                              user?.user_id ? (
                                                <ControlPointIcon
                                                  sx={{
                                                    color:
                                                      userExpanded ===
                                                      user?.user_id
                                                        ? 'white'
                                                        : '#36C0ED'
                                                  }}
                                                />
                                              ) : (
                                                <RemoveCircleOutlineIcon
                                                  sx={{
                                                    color:
                                                      userExpanded ===
                                                      user?.user_id
                                                        ? 'white'
                                                        : '#36C0ED'
                                                  }}
                                                />
                                              )}
                                            </IconButton>
                                            <span
                                              style={{
                                                marginLeft: 8,
                                                overflowWrap: 'anywhere'
                                              }}
                                            >
                                              {user?.name}
                                            </span>
                                          </Grid>
                                          <Grid
                                            item
                                            xs={4}
                                            sx={{ overflowWrap: 'anywhere' }}
                                          >
                                            {user?.email}
                                          </Grid>
                                          <Grid item xs={3}>
                                            {user?.mobile_number}
                                          </Grid>
                                          <Grid item xs={2}>
                                            {user?.apps?.length > 1
                                              ? `${user?.apps?.length} Apps`
                                              : `${user?.apps?.length} App`}
                                          </Grid>
                                        </Grid>
                                      </Box>
                                      {/* Add Edit and Delete Icons */}
                                      <Box className="accordion-box2">
                                        <IconButton
                                          sx={{ marginLeft: 1 }}
                                          onClick={
                                            () =>
                                              handleOpenDeleteDialog(
                                                user?.user_id
                                              )
                                            // handleDelete(user?.user_id)
                                          }
                                        >
                                          <DeleteForeverOutlinedIcon
                                            color="primary"
                                            sx={{
                                              color:
                                                userExpanded === user?.user_id
                                                  ? 'white'
                                                  : '#36C0ED'
                                            }}
                                          />
                                        </IconButton>
                                        <IconButton
                                          sx={{ marginLeft: 1 }}
                                          onClick={() =>
                                            navigate(
                                              `/users/update-user/${user?.user_id}`
                                            )
                                          }
                                        >
                                          <EditOutlinedIcon
                                            color="primary"
                                            sx={{
                                              color:
                                                userExpanded === user?.user_id
                                                  ? 'white'
                                                  : '#36C0ED'
                                            }}
                                          />
                                        </IconButton>
                                      </Box>
                                    </AccordionSummary>

                                    <AccordionDetails className="userAccordionDetailsStyles">
                                      <Box className="user-ad-box">
                                        <Box
                                          className="card card-1"
                                          style={{
                                            padding: '16px',
                                            overflowWrap: 'anywhere'
                                          }}
                                        >
                                          <Typography className="user-name">
                                            {user?.name}
                                          </Typography>
                                          <Box className="mail-box">
                                            <MailIcon className="mail-icon" />
                                            <Typography className="user-email">
                                              {user?.email}
                                            </Typography>
                                          </Box>
                                          <Box className="number-box">
                                            <PhoneAndroidIcon className="phone-icon" />
                                            <Typography
                                              sx={{ fontSize: '16px' }}
                                            >
                                              {user?.mobile_number}
                                            </Typography>
                                          </Box>
                                        </Box>
                                        <Box
                                          className="card card-2"
                                          style={{ padding: '16px' }}
                                        >
                                          <Box className="apps-list">
                                            <Grid container spacing={2}>
                                              {user?.apps &&
                                                user?.apps?.map((app: any) => (
                                                  <Grid
                                                    item
                                                    xs={12}
                                                    sm={getApplistGridLength(
                                                      user.apps?.length
                                                    )}
                                                    key={app.id}
                                                  >
                                                    <Box
                                                      sx={{
                                                        width: '140px',
                                                        height: '140px',
                                                        borderRadius: '16px',
                                                        background: '#fff',
                                                        boxShadow:
                                                          '0px 4px 8px rgba(0,0,0,0.15)',
                                                        transition:
                                                          '0.3s ease-in-out',
                                                        cursor: 'pointer',
                                                        display: 'flex',
                                                        flexDirection: 'column',
                                                        alignItems: 'center',
                                                        overflow: 'hidden'
                                                      }}
                                                      // Add onClick handler if needed
                                                    >
                                                      <Box
                                                        sx={{
                                                          width: '100%',
                                                          height: '120px',
                                                          display: 'flex',
                                                          alignItems: 'center',
                                                          justifyContent:
                                                            'center',
                                                          backgroundColor:
                                                            '#e9feff'
                                                        }}
                                                      >
                                                        {app.orgAppConfiguration
                                                          ?.logo ? (
                                                          <Box
                                                            component="img"
                                                            src={
                                                              app
                                                                .orgAppConfiguration
                                                                ?.logo
                                                            }
                                                            alt={
                                                              app
                                                                .orgAppConfiguration
                                                                ?.app_name ||
                                                              app.name
                                                            }
                                                            sx={{
                                                              width: '80px',
                                                              height: '80px',
                                                              objectFit:
                                                                'contain',
                                                              borderRadius:
                                                                '50%',
                                                              backgroundColor:
                                                                '#ffffff'
                                                            }}
                                                          />
                                                        ) : (
                                                          <Box
                                                            sx={{
                                                              width: '80px',
                                                              height: '80px',
                                                              display: 'flex',
                                                              alignItems:
                                                                'center',
                                                              justifyContent:
                                                                'center',
                                                              borderRadius:
                                                                '50%',
                                                              backgroundColor:
                                                                '#ffffff'
                                                            }}
                                                          >
                                                            <Widgets
                                                              sx={{
                                                                color:
                                                                  '#616161',
                                                                fontSize: '30px'
                                                              }}
                                                            />
                                                          </Box>
                                                        )}
                                                      </Box>
                                                      <Tooltip
                                                        title={
                                                          app
                                                            ?.orgAppConfiguration
                                                            ?.app_name ||
                                                          app.name
                                                        }
                                                        arrow
                                                      >
                                                        <Typography
                                                          sx={{
                                                            fontWeight: 700,
                                                            textAlign: 'center',
                                                            padding: '10px',
                                                            overflow: 'hidden',
                                                            textOverflow:
                                                              'ellipsis',
                                                            whiteSpace:
                                                              'nowrap',
                                                            width: '100%',
                                                            color: '#595959'
                                                          }}
                                                        >
                                                          {app
                                                            ?.orgAppConfiguration
                                                            ?.app_name ||
                                                            app.name}
                                                        </Typography>
                                                      </Tooltip>
                                                    </Box>
                                                  </Grid>
                                                ))}
                                            </Grid>
                                          </Box>
                                        </Box>
                                      </Box>
                                    </AccordionDetails>
                                  </Accordion>
                                );
                              })
                            )}
                          </AccordionDetails>
                        </Accordion>
                      </Box>
                    );
                  })
              : usersList?.map((user: any) => {
                  return (
                    <Box key={user?.user_id} sx={{ mb: 0.5 }}>
                      <Accordion
                        id="active-user"
                        key={user?.user_id}
                        expanded={userExpanded === user?.user_id}
                        onChange={handleUserChange(user?.user_id)}
                        className="accordion"
                      >
                        <AccordionSummary
                          aria-controls="panel1-content"
                          id="panel1-header"
                          className="sub-accordian-summary"
                          sx={{
                            bgcolor:
                              userExpanded === user?.user_id
                                ? '#36C0ED'
                                : 'white',
                            color:
                              userExpanded === user?.user_id
                                ? 'white'
                                : 'inherit'
                          }}
                        >
                          <Box className="accordion-box">
                            <Grid container alignItems="center" spacing={2}>
                              <Grid item xs={3}>
                                <IconButton>
                                  {userExpanded !== user?.user_id ? (
                                    <ControlPointIcon
                                      sx={{
                                        color:
                                          userExpanded === user?.user_id
                                            ? 'white'
                                            : '#36C0ED'
                                      }}
                                    />
                                  ) : (
                                    <RemoveCircleOutlineIcon
                                      sx={{
                                        color:
                                          userExpanded === user?.user_id
                                            ? 'white'
                                            : '#36C0ED'
                                      }}
                                    />
                                  )}
                                </IconButton>
                                <span style={{ marginLeft: 8 }}>
                                  {user?.name}
                                </span>
                              </Grid>
                              <Grid
                                item
                                xs={4}
                                sx={{ overflowWrap: 'anywhere' }}
                              >
                                {user?.email}
                              </Grid>
                              <Grid item xs={3}>
                                {user?.mobile_number}
                              </Grid>
                              <Grid item xs={2}>
                                {user?.apps?.length > 1
                                  ? `${user?.apps?.length} Apps`
                                  : `${user?.apps?.length} App`}
                              </Grid>
                            </Grid>
                          </Box>
                          {/* Add Edit and Delete Icons */}
                          <Box className="accordion-box2">
                            <IconButton
                              sx={{ marginLeft: 1 }}
                              onClick={
                                () => handleOpenDeleteDialog(user?.user_id)
                                // handleDelete(user?.user_id)
                              }
                            >
                              <DeleteForeverOutlinedIcon
                                color="primary"
                                sx={{
                                  color:
                                    userExpanded === user?.user_id
                                      ? 'white'
                                      : '#36C0ED'
                                }}
                              />
                            </IconButton>
                            <IconButton
                              sx={{ marginLeft: 1 }}
                              onClick={() =>
                                navigate(`/users/update-user/${user?.user_id}`)
                              }
                            >
                              <EditOutlinedIcon
                                color="primary"
                                sx={{
                                  color:
                                    userExpanded === user?.user_id
                                      ? 'white'
                                      : '#36C0ED'
                                }}
                              />
                            </IconButton>
                          </Box>
                        </AccordionSummary>

                        <AccordionDetails className="userAccordionDetailsStyles">
                          <Box className="user-ad-box">
                            {/* User Info Card */}
                            <Box
                              className="card card-1"
                              style={{
                                padding: '16px',
                                overflowWrap: 'anywhere'
                              }}
                            >
                              <Typography className="user-name">
                                {user?.name}
                              </Typography>
                              <Box className="mail-box">
                                <MailIcon className="mail-icon" />
                                <Typography className="user-email">
                                  {user?.email}
                                </Typography>
                              </Box>
                              <Box className="number-box">
                                <PhoneAndroidIcon className="phone-icon" />
                                <Typography sx={{ fontSize: '16px' }}>
                                  {user?.mobile_number}
                                </Typography>
                              </Box>
                              {user?.onboarding_employee && (
                                <Button
                                  onClick={() => {
                                    navigate(
                                      `/users/employee-onboarding/checklist/${user?.onboarding_employee?.onboarding_employee_id}`
                                    );
                                  }}
                                >
                                  Applicant Details
                                </Button>
                              )}
                            </Box>

                            {/* Apps List Card */}
                            {user?.apps && user?.apps?.length > 0 && (
                              <Box
                                className="card card-2"
                                style={{ padding: '16px' }}
                              >
                                <Box className="apps-list">
                                  <Grid container spacing={2}>
                                    {user.apps.map((app: any) => (
                                      <Grid
                                        item
                                        xs={12}
                                        sm={getApplistGridLength(
                                          user?.apps?.length
                                        )}
                                        key={app.id}
                                      >
                                        <Box
                                          sx={{
                                            width: '140px',
                                            height: '140px',
                                            borderRadius: '16px',
                                            background: '#fff',
                                            boxShadow:
                                              '0px 4px 8px rgba(0,0,0,0.15)',
                                            transition: '0.3s ease-in-out',
                                            cursor: 'pointer',
                                            display: 'flex',
                                            flexDirection: 'column',
                                            alignItems: 'center',
                                            overflow: 'hidden'
                                          }}
                                        >
                                          <Box
                                            sx={{
                                              width: '100%',
                                              height: '120px',
                                              display: 'flex',
                                              alignItems: 'center',
                                              justifyContent: 'center',
                                              backgroundColor: '#e9feff'
                                            }}
                                          >
                                            {app.orgAppConfiguration?.logo ? (
                                              <Box
                                                component="img"
                                                src={
                                                  app.orgAppConfiguration.logo
                                                }
                                                alt={
                                                  app.orgAppConfiguration
                                                    ?.app_name || app.name
                                                }
                                                sx={{
                                                  width: '80px',
                                                  height: '80px',
                                                  objectFit: 'contain',
                                                  borderRadius: '50%',
                                                  backgroundColor: '#ffffff'
                                                }}
                                              />
                                            ) : (
                                              <Box
                                                sx={{
                                                  width: '80px',
                                                  height: '80px',
                                                  display: 'flex',
                                                  alignItems: 'center',
                                                  justifyContent: 'center',
                                                  borderRadius: '50%',
                                                  backgroundColor: '#ffffff'
                                                }}
                                              >
                                                <Widgets
                                                  sx={{
                                                    color: '#616161',
                                                    fontSize: '30px'
                                                  }}
                                                />
                                              </Box>
                                            )}
                                          </Box>
                                          <Tooltip
                                            title={
                                              app?.orgAppConfiguration
                                                ?.app_name || app.name
                                            }
                                            arrow
                                          >
                                            <Typography
                                              sx={{
                                                fontWeight: 700,
                                                textAlign: 'center',
                                                padding: '10px',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                whiteSpace: 'nowrap',
                                                width: '100%',
                                                color: '#595959'
                                              }}
                                            >
                                              {app?.orgAppConfiguration
                                                ?.app_name || app.name}
                                            </Typography>
                                          </Tooltip>
                                        </Box>
                                      </Grid>
                                    ))}
                                  </Grid>
                                </Box>
                              </Box>
                            )}
                          </Box>
                        </AccordionDetails>
                      </Accordion>
                    </Box>
                  );
                })}

            {/* <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <Pagination
              count={totalPages}
              shape="rounded"
              page={activePage}
              onChange={(_e, value) => setActivePage(value)}
              sx={{
                '& .MuiPaginationItem-root': {
                  '&.Mui-selected': {
                    backgroundColor: '#29abe2',
                    color: 'white',
                    '&:hover': { backgroundColor: '#29abe2' }
                  }
                },
                '& .MuiPaginationItem-previousNext': { color: '#29abe2' }
              }}
            />
          </Box> */}
          </Box>
        </Box>
      </Box>
      <Dialog
        open={openDeleteDialog}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <Box sx={{ padding: '20px', background: '#ffffff' }}>
          <Box sx={{ padding: '20px', background: '#f6f6f6' }}>
            <DialogTitle id="delete-dialog-title">Delete User?</DialogTitle>
            <DialogContent>
              <DialogContentText id="delete-dialog-description">
                Are you sure you want to delete this user? This action cannot be
                undone.
              </DialogContentText>
            </DialogContent>
          </Box>
          <DialogActions>
            <Button
              onClick={handleCloseDeleteDialog}
              sx={{
                backgroundColor: 'white2.main',
                color: 'primaryBlue.main',
                padding: '10px 30px',
                boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                textTransform: 'capitalize'
              }}
            >
              CANCEL
            </Button>
            <Button
              onClick={confirmDeleteSection}
              sx={{
                backgroundColor: 'primaryBlue.main',
                color: 'white2.main',
                padding: '10px 35px',
                boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                '&:hover': {
                  color: 'white2.main',
                  backgroundColor: 'primaryBlue.main'
                }
              }}
            >
              DELETE
            </Button>
          </DialogActions>
        </Box>
      </Dialog>

      {/* <SnackbarElement
        message={snackbarOpen.message}
        statusType={snackbarOpen.type || 'success'}
        snackbarOpen={snackbarOpen.status}
        setSnackbarOpen={setSnackbarOpen}
      /> */}
    </Box>
  );

  const getSubMenu = () => {
    return (
      <SubMenu
        // pageName="Users"
        buttonWithoutBg={{
          status: true,
          title: 'New Employee',
          icon: 'AddCircleOutline',
          redirectUrl: '/users/user-registration'
        }}
        iconStyles={{
          color: '#fff'
        }}
      />
    );
  };

  const menuItems = UserNavBarModules();

  const getDrawer = () => {
    return <Sidebar menuItems={menuItems} userType={undefined} />;
  };

  const render = () => {
    if (isLoading) {
      return <LoaderUI />;
    }
    if (userType === 'super_admin') {
      return <Box sx={{ p: '0px 100px' }}>{content}</Box>;
    }
    return content;
  };

  return (
    <Shell
      subMenu={getSubMenu()}
      showDrawer={userType === 'organization'}
      drawerData={getDrawer()}
    >
      {render()}
    </Shell>
  );
};

export default UsersList;
