/* eslint-disable react/no-array-index-key */
// Package Imports
import {
  Autocomplete,
  Box,
  Button,
  Chip,
  FormControlLabel,
  Grid,
  IconButton,
  Modal,
  Switch,
  TextField,
  Typography
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import * as Yup from 'yup';
import { getIn, useFormikContext } from 'formik';
// import ReactQuill from 'react-quill';
import { CSS } from '@dnd-kit/utilities';
import { closestCenter, DndContext } from '@dnd-kit/core';
import {
  arrayMove,
  rectSortingStrategy,
  SortableContext,
  useSortable
} from '@dnd-kit/sortable';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
import {
  AppForm,
  FieldDescription,
  FormInput,
  FormSelect,
  Icon,
  SubmitButton
} from '../../form.elements';
import { JobPostTypes } from '../../../types';
import CreateStages from './CreateStages';
import { AppDispatch, AppState } from '../../../redux/app.store';
import {
  createsjobpost,
  getjobpostwithid,
  updatejobpost
} from '../../../redux/reducers/jobpost.reducer';
import { getstages } from '../../../redux/reducers/stages.reducer';

const StageCardComponent: React.FC<any> = ({
  card,
  onCardClick,
  isDraggable = false
}) => {
  const [showModal, setShowModal] = useState<{ status: boolean; id: string }>({
    status: false,
    id: ''
  });
  console.log('Stage Details: ', card);
  return (
    <Box
      onClick={() => onCardClick(card?.stage_id)}
      sx={{
        borderRadius: 300,
        boxShadow: 1,
        p: 0,
        width: '100%',
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: 'red',
        // backgroundColor: 'background.paper',
        border: '1px solid transparent',
        transition: 'all 0.3s ease-in-out',
        ...(isDraggable && {
          '&:hover': {
            backgroundColor: 'background.default',
            border: '1px solid',
            borderColor: 'primary.main',
            transform: 'scale(1.05)',
            boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)',
            borderRadius: '300px'
          }
        })
      }}
    >
      <Box
        sx={{
          padding: '10px 20px',
          borderRadius: '300px',
          backgroundColor: '#00000014',
          width: '100%',
          position: 'relative',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: '10px'
        }}
      >
        <Typography>{card?.title}</Typography>
        <IconButton
          sx={{
            color: 'primary.main'
          }}
          onClick={() => {
            // e.stopPropagation();
            // onCardClick(card?.id);
            setShowModal({ status: true, id: card?.stage_id });
          }}
        >
          <Icon name="Edit" />
        </IconButton>
        {/* <Typography>{card.label}</Typography> */}
      </Box>
      <Modal
        open={showModal?.status}
        onClose={() => setShowModal({ status: false, id: '' })}
      >
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            boxShadow: 24,
            p: 4,
            borderRadius: '10px',
            overflow: 'auto',
            backgroundColor: '#FFFFFF'
          }}
        >
          <Box
            sx={{
              marginTop: '10px',
              maxWidth: '900px',
              overflow: 'auto'
            }}
          >
            <CreateStages
              page="JobPost"
              stageId={card?.stage_id}
              onClose={() => setShowModal({ status: false, id: '' })}
            />
          </Box>
        </Box>
      </Modal>
    </Box>
  );
};

const SortableStageCard: React.FC<any> = ({ card, onCardClick }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({
    id: card.id
  });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.8 : 1,
    padding: '0px'
  };

  return (
    <Grid
      item
      xs={12}
      sm={6}
      md={4}
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
    >
      <StageCardComponent card={card} onCardClick={onCardClick} isDraggable />
    </Grid>
  );
};

interface FormRequirementsProps {
  name: string;
  label?: string;
}

interface FieldStagesProps {
  name: string;
  label: string;
  isEnabled: boolean;
  stagesOptions: any[];
}
interface StageDataProps {
  stage_id: string;
  sequence_order: number;
  is_required: boolean;
  is_active: boolean;
}

const FormRequirements: React.FC<FormRequirementsProps> = ({ name, label }) => {
  const { values, setFieldValue } = useFormikContext<any>();
  const formikValue = getIn(values, name);
  const [requirements, setRequirements] = useState<string[]>(['']);

  const handleChange = (index: number, value: string) => {
    const updated = [...requirements];
    updated[index] = value;
    setRequirements(updated);
    // setFieldValue(name, requirements);
  };

  const addRequirement = () => {
    setRequirements([...requirements, '']);
  };

  const removeRequirement = (index: number) => {
    const updated = requirements.filter((_, i) => i !== index);
    setRequirements(updated.length > 0 ? updated : ['']);
  };

  useEffect(() => {
    if (formikValue[0] === '') {
      setRequirements(['']);
    } else if (Array.isArray(formikValue) && formikValue[0] !== '') {
      setRequirements(formikValue);
    }
  }, [formikValue]);

  // Update Formik whenever local state changes
  useEffect(() => {
    setFieldValue(name, requirements);
  }, [requirements]);

  return (
    <Box
      sx={{
        display: 'grid',
        gridColumn: 'span 2'
      }}
    >
      <Typography sx={{ mb: 1, fontWeight: 500 }}>
        {label || 'Job Requirements'}
      </Typography>

      {requirements?.map((req, index) => (
        <Box
          key={index}
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            mb: 1
          }}
        >
          <TextField
            fullWidth
            variant="outlined"
            placeholder={`Requirement ${index + 1}`}
            value={req}
            onChange={(e) => handleChange(index, e.target.value)}
          />
          {(requirements?.length ?? 0) - 1 === index && (
            <IconButton color="primary" onClick={addRequirement}>
              <Icon name="AddCircle" />
            </IconButton>
          )}
          {requirements?.length > 1 &&
            (requirements?.length ?? 0) - 1 !== index && (
              <IconButton
                color="error"
                onClick={() => removeRequirement(index)}
                disabled={requirements.length === 1}
              >
                <Icon name="DeleteOutline" />
              </IconButton>
            )}
        </Box>
      ))}

      {/* <Button
        variant="outlined"
        startIcon={<Icon name="AddCircle" />}
        onClick={addRequirement}
      >
        Add Requirement
      </Button> */}
    </Box>
  );
};

const FieldStages: React.FC<FieldStagesProps> = ({
  name,
  label,
  isEnabled,
  stagesOptions
}) => {
  const { setFieldValue } = useFormikContext<any>();
  const [stagesField, setStagesField] = useState<any[]>([]);
  const [showStageModal, setShowStageModal] = useState<boolean>(false);

  const handleStages = async (_e: any, value: any) => {
    const stageData: StageDataProps[] = value?.map((stage: any) => ({
      stage_id: stage?.value,
      sequence_order: 1,
      is_required: true,
      is_active: true
    }));
    setStagesField(value);
    setFieldValue(name, stageData);
  };

  const fetchStagesList = (stage: any) => {
    const stageData = [
      ...stagesField,
      {
        stage_id: stage?.value,
        sequence_order: 1,
        is_required: true,
        is_active: true
      }
    ];

    setStagesField((prevData) => {
      const data = [
        ...prevData,
        { value: stage?.stage_id, label: stage?.title }
      ];
      return data;
    });
    setFieldValue(name, stageData);
  };
  return (
    <Box>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: '20px',
          width: '100%'
        }}
      >
        <Typography sx={{ position: 'relative' }}>{label}</Typography>
        {!isEnabled && (
          <IconButton onClick={() => setShowStageModal(true)}>
            <Icon name="AddCircle" sx={{ color: 'blue' }} />
          </IconButton>
        )}
      </Box>
      <Autocomplete
        multiple
        options={stagesOptions}
        getOptionLabel={(option) => `${option.label}`}
        value={Array.isArray(stagesField) ? stagesField : []}
        onChange={handleStages}
        disabled={isEnabled}
        isOptionEqualToValue={(option, value) => option.value === value.value}
        renderTags={(value: any, getTagProps) =>
          value.map((option: any, index: number) => (
            <Chip
              {...getTagProps({ index })}
              label={`${option.label}`}
              key={`${index + 1}`}
            />
          ))
        }
        renderInput={(params: any) => (
          <TextField
            {...params}
            placeholder="Select Stages..."
            error={!stagesField || stagesField.length === 0}
            helperText={
              !stagesField || stagesField.length === 0
                ? 'Select at least one Stage is required'
                : ''
            }
          />
        )}
      />
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          gap: '10px',
          width: '100%'
        }}
      >
        <Typography
          sx={{
            fontWeight: '500'
          }}
        >
          Note:
        </Typography>
        <Typography>Select the Stages in the Job Process Order.</Typography>
      </Box>
      <Modal open={showStageModal} onClose={() => setShowStageModal(false)}>
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            boxShadow: 24,
            p: 4,
            borderRadius: '10px',
            overflow: 'auto',
            backgroundColor: '#FFFFFF'
          }}
        >
          <Box
            sx={{
              marginTop: '10px',
              maxWidth: '900px',
              overflow: 'auto'
            }}
          >
            <CreateStages
              page="JobPost"
              onClose={() => setShowStageModal(false)}
              fetchStagesList={fetchStagesList}
            />
          </Box>
        </Box>
      </Modal>
    </Box>
  );
};

const employmentTypeOptions = [
  { value: 'Full-time', label: 'Full-Time' },
  { value: 'Part-time', label: 'Part-Time' },
  { value: 'Contract', label: 'Contract' },
  { value: 'Internship', label: 'Internship' },
  { value: 'Temporary', label: 'Temporary' }
];

const jobStatusOptions = [
  { value: 'Active', label: 'Active' },
  { value: 'Inactive', label: 'Inactive' },
  { value: 'Draft', label: 'Draft' }
];

const CreateJobPost = () => {
  const [initialValues, setinitialValues] = useState<JobPostTypes>({
    title: '',
    department: '',
    description: '',
    requirements: [''],
    salary_range: '',
    employment_type: 'Full-time',
    location: '',
    status: 'Active',
    application_start_date: '',
    application_end_date: '',
    stages: []
  });
  const { id } = useParams();
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { jobPost } = useSelector((state: AppState) => state.jobpost);

  const [stagesOptions, setStagesOptions] = React.useState<any>([]);
  const [stagesListData, setStagesListData] = React.useState<any>([]);
  const [isEnabled, setIsEnabled] = useState(false);
  const { stagesList } = useSelector((state: AppState) => state.stages);

  const validationSchema = Yup.object().shape({
    title: Yup.string().required('Title is required'),
    department: Yup.string().required('Department is required'),
    description: Yup.string().required('Description is required'),
    salary_range: Yup.string().required('Salary range is required'),
    employment_type: Yup.string().required('Employment type is required'),
    location: Yup.string().required('Location is required'),
    status: Yup.string().required('Status is required'),
    application_start_date: Yup.date()
      .required('Application start date is required')
      .min(new Date(), 'Application start date cannot be in the past'),
    application_end_date: Yup.date()
      .required('Application end date is required')
      .min(new Date(), 'Application end date cannot be in the past')
  });

  const handleToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { checked } = event.target;
    setIsEnabled(checked);

    if (checked) {
      console.log('Order Sequence Enabled');
    } else {
      console.log('Order Sequence Disabled');
    }
  };

  const handleDragEnd = async (event: any) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    setStagesList((currentStages: any) => {
      const oldIndex = currentStages.findIndex(
        (stage: any) => stage.id === active.id
      );
      const newIndex = currentStages.findIndex(
        (stage: any) => stage.id === over.id
      );
      return arrayMove(currentStages, oldIndex, newIndex);
    });

    // const updatedForms = arrayMove(
    //   formsList,
    //   formsList.findIndex((form) => form.id === active.id),
    //   formsList.findIndex((form) => form.id === over.id)
    // );

    // const data = {
    //   forms: updatedForms.map((form, index) => ({
    //     form_id: form.id,
    //     form_index: index
    //   }))
    // };

    // try {
    //   await dispatch(updateformslist({ appId: id, data }));
    // } catch {
    //   toast.error('Something went wrong. Try again later.');
    // }
  };

  const handleCardClick = (stageId: string) => {
    // navigate(`/form-builder/edit-form/${cardId}`);
    // if (id) {
    //   localStorage.setItem('app_id', id);
    // }
    console.log('Stage Id: ', stageId);
  };

  const getStagesListData = async () => {
    const res = await dispatch(getstages(null));
    if (res.meta.requestStatus === 'rejected') {
      toast.error(res.payload?.message);
    } else {
      const data = res.payload.stages?.map((stage: any) => {
        return {
          value: stage?.stage_id,
          label: stage?.title
        };
      });
      setStagesOptions(data);
    }
  };

  const getJobPostData = async () => {
    const res = await dispatch(getjobpostwithid(id));
    if (res.meta.requestStatus === 'rejected') {
      toast.error(res.payload?.message);
    } else {
      const { data } = res.payload;
      const jobStatusData = data?.job_stages?.map((stage: any) => {
        return {
          value: stage?.job_stage_id,
          label: stage?.title
        };
      });

      const initValues: JobPostTypes = {
        title: data?.title,
        department: data?.department,
        description: data?.description,
        requirements: data?.requirements,
        salary_range: data?.salary_range,
        employment_type: data?.employment_type,
        location: data?.location,
        status: data?.status,
        application_start_date: data?.application_start_date,
        application_end_date: data?.application_end_date,
        stages: jobStatusData
      };
      console.log('get init values:', initValues, res);
      setIsEnabled(true);
      setStagesListData(data?.job_stages);
      setStagesOptions(jobStatusData);
      setinitialValues(initValues);
    }
  };
  useEffect(() => {
    if (id) {
      getJobPostData();
    } else {
      if (stagesList?.length === 0) {
        getStagesListData();
      } else {
        const data = stagesList?.map((stage: any) => {
          return {
            value: stage?.stage_id,
            label: stage?.title
          };
        });
        setStagesOptions(data);
      }
      const initValues: JobPostTypes = {
        title: '',
        department: '',
        description: '',
        requirements: [''],
        salary_range: '',
        employment_type: 'Full-time', // default from EmploymentType
        location: '',
        status: 'Active',
        application_start_date: '',
        application_end_date: '',
        stages: []
      };
      setinitialValues(initValues);
    }
  }, []);

  useEffect(() => {
    const data = stagesList?.map((stage: any) => {
      return {
        value: stage?.stage_id,
        label: stage?.title
      };
    });
    setStagesOptions(data);
  }, [stagesList]);

  const handleSubmit = async (val: any) => {
    if (id) {
      const data = {
        id: jobPost?.id || '',
        job_id: jobPost?.job_id || '',
        title: val?.title,
        department: val?.department,
        description: val?.description,
        requirements: val?.requirements,
        salary_range: val?.salary_range,
        employment_type: val?.employment_type,
        location: val?.location,
        application_start_date: val?.application_start_date,
        application_end_date: val?.application_end_date,
        change_reason: 'changed',
        stages: val?.stages?.map((stage: any) => ({
          stage_id: stage?.value,
          sequence_order: 1, // Default value, can be updated later
          is_required: true, // Default value, can be updated later
          is_active: true // Default value, can be updated later
        }))
      };
      const res = await dispatch(updatejobpost({ id, data }));
      if (res.meta.requestStatus === 'rejected') {
        toast.error(res.payload.message);
      } else {
        toast.success(res.payload.message);
        navigate('/users/employee-onboarding/create-onboarding-process');
      }
      console.log(val, data, res);
    } else {
      const data = {
        title: val?.title,
        department: val?.department,
        description: val?.description,
        requirements: val?.requirements,
        salary_range: val?.salary_range,
        employment_type: val?.employment_type,
        location: val?.location,
        application_start_date: val?.application_start_date,
        application_end_date: val?.application_end_date,
        stages: val?.stages?.map((stage: any) => ({
          stage_id: stage?.value,
          sequence_order: 1, // Default value, can be updated later
          is_required: true, // Default value, can be updated later
          is_active: true // Default value, can be updated later
        }))
      };
      const res = await dispatch(createsjobpost(data));
      if (res.meta.requestStatus === 'rejected') {
        toast.error(res.payload.message);
      } else {
        toast.success(res.payload.message);
        navigate('/users/employee-onboarding/create-onboarding-process');
      }
      console.log(val, data, res);
    }
  };

  return (
    <Box
      sx={{
        padding: '20px'
      }}
    >
      <Typography
        // className="title"
        sx={{
          fontWeight: 600,
          color: '#27292D',
          fontSize: 'clamp(1rem, 1.5vw, 1.25rem)'
        }}
      >
        Create Job Post
      </Typography>

      <Box
        sx={{
          background: '#FFFFFF',
          border: 1,
          borderColor: '#A3A3A3',
          padding: '50px 20px',
          marginTop: '20px'
        }}
      >
        <AppForm
          initialValues={initialValues}
          onSubmit={handleSubmit}
          validationSchema={validationSchema}
        >
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: 'repeat(2, 1fr)',
              gap: '20px'
            }}
          >
            <FormInput name="title" type="text" label="Job Post Title" />
            <FormInput name="department" type="text" label="Department" />
            <Box
              sx={{ height: '220px', display: 'grid', gridColumn: 'span 2' }}
            >
              <FieldDescription name="description" label="Job Description" />
            </Box>
            <FormRequirements name="requirements" label="Job Requirements" />
            <FormInput
              name="salary_range"
              type="text"
              label="Salary Range"
              placeholder="e.g.: 4,00,00 to 10,00,000, 6,00,00 to 10,00,000"
            />
            <FormSelect
              name="employment_type"
              label="Employment Type"
              data={employmentTypeOptions}
            />
            <FormInput name="location" type="text" label="Location" />
            <FormSelect name="status" label="Status" data={jobStatusOptions} />
            <FormInput
              name="application_start_date"
              type="date"
              label="Application Start Date"
            />
            <FormInput
              name="application_end_date"
              type="date"
              label="Application End Date"
            />
            {/* <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                gap: '20px',
                width: '100%'
              }}
            >
              <FormInput
                name="application_start_date"
                type="date"
                label="Application Start Date"
              />
              <FormInput
                name="application_end_date"
                type="date"
                label="Application End Date"
              />
            </Box> */}
            <FieldStages
              name="stages"
              label="Select Stages"
              isEnabled={isEnabled}
              stagesOptions={stagesOptions}
            />
            <Box>
              <Typography>.</Typography>
            </Box>
            {id && (
              <Box>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    gap: '10px',
                    width: '100%'
                  }}
                >
                  <Typography>Order Sequences</Typography>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      gap: '20px'
                    }}
                  >
                    {/* {isEnabled && (
                      <Button type="button" variant="text">
                        Save Sequence
                      </Button>
                    )} */}
                    <FormControlLabel
                      control={
                        <Switch checked={isEnabled} onChange={handleToggle} />
                      }
                      label={isEnabled ? 'Enabled' : 'Disabled'}
                    />
                  </Box>
                </Box>
                <Box>
                  <DndContext
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                  >
                    <SortableContext
                      items={stagesListData?.map((stage: any) => stage.id)}
                      strategy={rectSortingStrategy}
                    >
                      <Grid
                        container
                        spacing={3}
                        sx={{
                          px: '20px',
                          py: '60px',
                          maxWidth: '100%',
                          mx: 'auto',
                          rowGap: '10px'
                        }}
                      >
                        {stagesListData?.map((stage: any) => (
                          <SortableStageCard
                            key={stage.id}
                            card={stage}
                            onCardClick={handleCardClick}
                          />
                          //   <Box
                          //     key={stage.id}
                          //     sx={{
                          //       padding: '10px 20px',
                          //       borderRadius: '300px',
                          //       backgroundColor: '#00000014'
                          //     }}
                          //   >
                          //     <Typography>{stage.label}</Typography>
                          //   </Box>
                        ))}
                      </Grid>
                    </SortableContext>
                  </DndContext>
                </Box>
              </Box>
            )}
            {id && (
              <Box>
                <Typography>.</Typography>
              </Box>
            )}
            <Box>
              <SubmitButton
                title={id ? 'Update Job Post' : 'Create Job Post'}
              />
            </Box>
          </Box>
        </AppForm>
      </Box>
    </Box>
  );
};

export default CreateJobPost;
