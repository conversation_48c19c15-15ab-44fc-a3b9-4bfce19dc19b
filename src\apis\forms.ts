import api, { handleError, apiRoutes, authHeaders } from './config';

export const getForm = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();

    const userType = localStorage.getItem('user_type');
    const expandParam =
      userType !== 'super_admin' ? '?expand=form_response_as_email' : '';

    const response = await api.get(`${apiRoutes.form}/${id}${expandParam}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getSectionTemplate = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.formsSection}/${id}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const UpdateForm = async (
  // data: any,
  payload: { value: any; values: any; name: any; formId: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  const form = payload.values;
  if (payload.name === 'name') {
    form.name = payload.value;
  } else if (payload.name === 'description') {
    form.description = payload.value;
  } else if (payload.name === 'icon') {
    form.icon = payload.value;
  }

  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.form}/${payload.formId}`,
      form,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const UpdateFormsList = async (
  // data: any,
  // payload: {appId:string, forms : {form_id:string,form_index: number}[]},
  payload: { appId: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  // const data = {forms: payload.data}

  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.form}/update-forms-index/${payload.appId}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const formsRepository = async (
  app_id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.form}?app_id=${app_id}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const toggleFormsList = async (
  payload: { appId: any; formId?: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();

    let url = `${apiRoutes.form}/toggle-forms-fill-order/${payload?.appId}`;
    if (payload?.formId) {
      url += `?form_id=${encodeURIComponent(payload.formId)}`;
    }

    const response = await api.patch(url, {}, { headers });

    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const UpdateField = async (
  // data: any,
  payload: { fieldData: any; formId: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  // const [formData, setFormData]=useState({});
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.form}/update-field/${payload.formId}`,
      payload.fieldData,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const UpdateFormStatus = async (
  payload: { id: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.put(
      `${apiRoutes.form}/change-status/${payload.id}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const dragAndDropSection = async (
  // data: any,
  payload: { formId: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  // const [formData, setFormData]=useState({});
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.form}/update-index/${payload.formId}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const DeleteField = async (
  // data: any,
  payload: { formId: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  // const [formData, setFormData]=useState({});
  try {
    const headers = await authHeaders();
    const response = await api.delete(
      `${apiRoutes.form}/delete-field/${payload.formId}`,
      {
        headers,
        data: payload.data
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const DuplicateField = async (
  // data: any,
  payload: { formId: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  // const [formData, setFormData]=useState({});
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.form}/copy-field/${payload.formId}`,
      payload.data,
      {
        headers
        // data: payload.data,
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const DeleteSection = async (
  // data: any,
  payload: { formId: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  // const [formData, setFormData]=useState({});
  try {
    const headers = await authHeaders();
    const response = await api.delete(
      `${apiRoutes.form}/delete-field/${payload.formId}`,
      {
        headers,
        data: payload.data
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const DuplicateSection = async (
  // data: any,
  payload: { formId: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  // const [formData, setFormData]=useState({});
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.form}/duplicate-section/${payload.formId}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const AddFieldMethod = async (
  // data: any,
  payload: { fieldData: any; formId: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  // const [formData, setFormData]=useState({});
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.form}/add-field/${payload.formId}`,
      payload.fieldData,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
export const UpdateFieldIndexes = async (
  // data: any,
  payload: { fieldsData: any; formId: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  // const [formData, setFormData]=useState({});
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.form}/update-index/${payload.formId}`,
      payload.fieldsData,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
export const UpdateSectionIndexes = async (
  // data: any,
  payload: { data: any; formId: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  // const [formData, setFormData]=useState({});
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.form}/update-group-index/${payload.formId}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const AddSectionMethod = async (
  // data: any,
  payload: { data: any; formId: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  // const [formData, setFormData]=useState({});

  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.form}/${payload.formId}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getFormFields = async (
  data: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get('form-fields', { headers, params: data });
    return fulfillWithValue(response.data?.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getCountriesList = async (
  _id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(apiRoutes.countryStateCity.countries, {
      headers
    });
    return fulfillWithValue(response.data?.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
export const getStatesList = async (
  countryCode: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(
      apiRoutes.countryStateCity.states + countryCode,
      { headers }
    );
    return fulfillWithValue(response.data?.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
export const getCitiesList = async (
  payload: { countryCode: string; stateCode: string },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(
      `${apiRoutes.countryStateCity.cities}${payload.countryCode}/${payload.stateCode}`,
      { headers }
    );
    return fulfillWithValue(response.data?.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const validatePreviewForm = async (
  payload: { id: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post(
      `${apiRoutes.form}/preview-validate/${payload.id}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getFormSections = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(apiRoutes.formsSection, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getThemes = async (
  appId: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.themes}/me?app_id=${appId}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const addTheme = async (
  payload: { appId: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post(
      `${apiRoutes.themes}/${payload.appId}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const updateTheme = async (
  payload: { appId: any; themeId: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.put(
      `${apiRoutes.themes}/${payload.themeId}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const activeteTheme = async (
  payload: { appId: any; themeId: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.themes}/activate/${payload.themeId}?app_id=${payload.appId}`,
      {},
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const updateformLayoutChanges = async (
  payload: { id: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.form}/${payload.id}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getFormResponseAsEmail = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`form-response-as-email/${id}`, {
      headers
    });
    return fulfillWithValue(response.data?.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const saveFormResponseAsEmail = async (
  data: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post('form-response-as-email', data, {
      headers
    });
    return fulfillWithValue(response.data?.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const updateFormResponseAsEmail = async (
  payload: { id: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.put(
      `form-response-as-email/${payload.id}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data?.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const updateCustomValidations = async (
  payload: { id: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.form}/custom-validations/${payload.id}`,
      payload.data,
      {
        headers
      }
    );

    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

// --- Reusable Field API Calls ---
export const createReuseField = async (
  data: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post(apiRoutes.reusableFields, data, {
      headers
    });

    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
