import { useParams } from 'react-router-dom';
// import ViewPdf from '../../../components/users/client-assessment/ViewPdf';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '../../../redux/app.store';
import { formresponsepdf } from '../../../redux/reducers/clients.reducer';
import { toast } from 'react-toastify';
import { Box } from '@mui/material';
import { AppState } from '../../../redux/app.store';
import { useSelector } from 'react-redux';
import { SubMenu } from '../../../components/form.elements';
import Shell from '../../../components/layout/Shell';
import LoaderUI from '../../../components/reusable/loaderUI';
const ViewPdfForm: React.FC = () => {
  const { clientId, appId } = useParams<{ clientId: string; appId: string }>();
  const { isLoading } = useSelector((state: AppState) => state.clients);
  const dispatch = useDispatch<AppDispatch>();
  const [message, setMessage] = useState<string>('');
  const [pdfUrl, setPdfUrl]: any = useState(null);
  const getPdfData = async () => {
    try {
      const response = await dispatch(
        formresponsepdf({
          clientId,
          formId: '37caadf7-4d65-4fb3-930d-47ecc92a5b5a'
        })
      );

      const bufferData = response.payload;

      if (bufferData && bufferData.byteLength) {
        // Convert ArrayBuffer to Uint8Array
        const uint8Array = new Uint8Array(bufferData);

        // Create Blob and preview URL
        const blob = new Blob([uint8Array], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        setPdfUrl(url); // this will be used in <iframe src={pdfUrl} />
      } else {
        toast.error('Invalid PDF data received');
      }

      // Optional: message or error from server
      if (response.payload?.message) {
        setMessage(response.payload.message);
      }

      if (response.payload?.error) {
        toast.error(response.payload.error);
      }
    } catch (error: any) {
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };
  const getSubMenu = () => {
    return <SubMenu backNavigation />;
  };
  useEffect(() => {
    getPdfData();
  }, [clientId, appId]);

  return (
    <Shell subMenu={getSubMenu()}>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box sx={{ padding: '20px 100px' }}>
          {message
            ? message
            : pdfUrl && (
                <iframe
                  src={pdfUrl}
                  width="100%"
                  height="1000px"
                  title="PDF Preview"
                />
              )}
        </Box>
      )}
    </Shell>
  );
};

export default ViewPdfForm;
