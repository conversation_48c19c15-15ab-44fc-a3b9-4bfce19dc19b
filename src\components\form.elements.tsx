/* eslint-disable react-hooks/exhaustive-deps */
// Package Imports
import {
  getIn,
  Formik,
  FormikProps,
  FormikValues,
  FormikHelpers,
  useFormikContext,
  FieldArray,
  Field,
  FieldProps,
  Form
} from 'formik';
import {
  Box,
  Link,
  alpha,
  Button,
  styled,
  useTheme,
  Checkbox,
  InputBase,
  FormGroup,
  FormControl,
  FormHelperText,
  FormControlLabel,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Radio,
  Switch,
  Divider,
  IconButton,
  Tooltip,
  Paper,
  Select,
  MenuItem,
  SelectChangeEvent,
  Backdrop,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Stepper,
  StepLabel,
  Grid,
  Menu
  // Stack
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import React, {
  // useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import moment from 'moment';
import { Add, RemoveCircleOutline } from '@mui/icons-material';
import { isNumber, isUndefined } from 'lodash';
import { matchIsValidTel, MuiTelInput } from 'mui-tel-input';
import * as Icons from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import {
  closestCorners,
  DndContext,
  KeyboardSensor,
  PointerSensor,
  TouchSensor,
  useSensor,
  useSensors
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import ReactQuill from 'react-quill';
// import { DemoContainer } from '@mui/x-date-pickers/internals/demo';
// import { DatePicker } from '@mui/x-date-pickers/DatePicker';

// Local Imports
import { useNavigate } from 'react-router-dom';
// import {
//   DateTimePicker,
//   LocalizationProvider,
//   TimePicker
// } from '@mui/x-date-pickers';
// import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
// import dayjs, { Dayjs } from 'dayjs';
import {
  APPFIELD,
  FIELD,
  FORM,
  FormCheckboxType,
  FormInputType,
  FormParagraphType,
  FormPhoneInputType,
  FormRadioType,
  FormSelectProps,
  FormSignatureType,
  FormTextareaProps,
  FormToggleType,
  GROUP,
  IconProps
} from '../types';
import Signature from './signature';
import {
  capitalizeWords,
  debounce,
  formatParagraph,
  formatText,
  generateEmptyObjectForArraySections,
  getCanvasWidth,
  removeHtmlTags
} from '../utils/functions';
import { RootState } from '../redux/reducers';
import { RenderField } from './reusable/RenderField';
import { AppDispatch, AppState } from '../redux/app.store';
// import { updateFormValues } from '../redux/reducers/preview.reducer';
import {
  formlayoutchanges,
  getcitieslist,
  getstateslist,
  updateDeviceLayoutType,
  updateFormLayout,
  updateSectionViewType,
  updateSelectedCity,
  updateSelectedCountry,
  updateSelectedState,
  updateformstatus
} from '../redux/reducers/form.reducer';

export const Icon: React.FC<IconProps> = ({
  name,
  color,
  fontSize,
  style,
  sx,
  onClick
}) => {
  const IconComponent = Icons[name];

  if (!IconComponent) {
    return null;
  }

  return (
    <IconComponent
      color={color}
      fontSize={fontSize}
      style={style}
      sx={sx}
      onClick={onClick}
    />
  );
};

export const AppForm = React.forwardRef(function AppForm(
  {
    children,
    initialValues,
    onSubmit,
    validationSchema,
    ...props
  }: {
    children: JSX.Element;
    initialValues: any;
    onSubmit: (
      values: FormikValues,
      formikHelpers?: FormikHelpers<FormikValues>
    ) => void;
    validationSchema?: any;
  },
  ref: React.ForwardedRef<FormikProps<any>>
) {
  return (
    <Formik
      enableReinitialize
      validateOnBlur={false}
      validateOnChange={false}
      onSubmit={onSubmit}
      initialValues={initialValues}
      validationSchema={validationSchema}
      {...props}
      innerRef={ref}
    >
      {() => <Form>{children}</Form>}
    </Formik>
  );
});

const BootstrapInput = styled(InputBase)(({ theme, multiline }) => ({
  '&': {
    width: '100%'
  },
  'label + &': {
    marginTop: theme.spacing(1.25)
  },
  '& .MuiInputBase-input': {
    fontSize: 16,
    width: '100%',
    borderRadius: 4,
    border: '1px solid',
    position: 'relative',
    padding: '10px 12px',
    borderColor: theme.palette.divider,
    color:
      theme.palette.mode === 'light'
        ? theme.palette.input?.dark
        : theme.palette.input?.main,
    height: multiline ? '90px !important' : theme.spacing(2.8),
    backgroundColor:
      theme.palette.mode === 'light'
        ? theme.palette.input?.main
        : theme.palette.input?.dark,
    transition: theme.transitions.create([
      'border-color',
      'background-color',
      'box-shadow'
    ]),
    // Use the system font instead of the default Roboto font.
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"'
    ].join(','),
    '&:focus': {
      boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      borderColor: theme.palette.primary.main
    }
  }
}));

const BootstrapTextarea = styled(InputBase)(({ theme }) => ({
  '&': {
    width: '100%'
  },
  'label + &': {
    marginTop: theme.spacing(1.25)
  },
  '& .MuiInputBase-input': {
    fontSize: 16,
    width: '100%',
    borderRadius: 4,
    border: '1px solid',
    position: 'relative',
    padding: '10px 12px',
    borderColor: theme.palette.divider,
    color:
      theme.palette.mode === 'light'
        ? theme.palette.input?.dark
        : theme.palette.input?.main,
    backgroundColor:
      theme.palette.mode === 'light'
        ? theme.palette.input?.main
        : theme.palette.input?.dark,
    transition: theme.transitions.create([
      'border-color',
      'background-color',
      'box-shadow'
    ]),
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"'
    ].join(','),
    '&:focus': {
      boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      borderColor: theme.palette.primary.main
    }
  }
}));

const BootstrapSelect = styled(Select)(({ theme, multiline }) => ({
  '&': {
    width: '100%',
    border: '0px solid #ffffff'
  },
  'label + &': {
    marginTop: theme.spacing(1.25)
  },
  '& .MuiSelect-select': {
    fontSize: 16,
    width: '100%',
    borderRadius: 4,
    border: '1px solid',
    position: 'relative',
    padding: '10px 12px',
    borderColor: 'rgba(0,0,0,0.12)',
    // borderColor: theme.palette.divider,
    color:
      theme.palette.mode === 'light'
        ? theme.palette.input?.dark
        : theme.palette.input?.main,
    height: multiline ? '90px !important' : theme.spacing(2.8),
    backgroundColor:
      theme.palette.mode === 'light'
        ? theme.palette.input?.main
        : theme.palette.input?.dark,
    transition: theme.transitions.create([
      'border-color',
      'background-color',
      'box-shadow'
    ]),
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"'
    ].join(','),
    '&:focus': {
      boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      borderColor: theme.palette.primary.main
    },
    '&.Mui-error': {
      // borderColor: "transparent", // Removes the red border
      // border: '0px solid #fff',
    }
  },
  '& .MuiSelect-icon': {
    color:
      theme.palette.mode === 'light'
        ? theme.palette.input?.dark
        : theme.palette.input?.main
  },
  '& fieldset': {
    // borderColor: 'rgba(0,0,0,0.12)',
    border: 'none'
  }
}));

const formControlStyle = {
  mb: 2
};

const Label: React.FC<{
  text: string;
  labelStyles?: any;
  activeTheme?: any;
  required?: boolean;
}> = ({ text, labelStyles, activeTheme, required }) => {
  return (
    <Typography
      sx={{
        textTransform: 'capitalize',
        color: activeTheme?.theme?.textColor || '#000000',
        ...labelStyles
      }}
    >
      {text}
      {required && <span style={{ color: 'red', marginLeft: '4px' }}>*</span>}
    </Typography>
    // <FormLabel
    //   sx={{
    //     textTransform: "capitalize",
    //     color: activeTheme?.theme?.textColor || "#000000",
    //     ...labelStyles,
    //   }}
    // >
    //   {text}
    // </FormLabel>
  );
};

export const AppRoundedCard: React.FC<{
  logo?: string;
  name: string;
  icon: any;
  versionsList?: any;
  listStyle: string;
  containerStyles?: any;
  roundCardStyles?: any;
  logoStyles?: any;
  nameStyles?: any;
  onClick?: any;
}> = ({
  logo,
  name,
  icon,
  versionsList,
  listStyle,
  containerStyles,
  roundCardStyles,
  logoStyles,
  nameStyles,
  onClick
}) => {
  return listStyle === 'list' ? (
    <Tooltip title={name} arrow>
      <Box
        sx={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          flexDirection: 'row',
          cursor: 'pointer',
          ...containerStyles
        }}
        onClick={onClick}
      >
        <Box
          sx={{
            width: '100%',
            height: '100%',
            aspectRatio: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '50%',
            flexDirection: 'column',
            backgroundColor: '#ffffff',
            ...roundCardStyles
          }}
        >
          {logo ? (
            <img src={logo} alt="img" style={logoStyles} />
          ) : (
            <Icon name={icon} />
          )}
        </Box>

        <Box sx={{ marginLeft: '16px' }}>
          <Typography
            sx={{
              width: '90%',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              fontSize: '18px',
              ...nameStyles
            }}
          >
            {name}
          </Typography>
          {versionsList && (
            <Box
              sx={{
                marginTop: '8px',
                display: 'flex',
                alignItems: 'center',
                gap: '4px'
              }}
            >
              <Box
                sx={{
                  fontSize: '9px',
                  borderRadius: '30px',
                  backgroundColor: '#EBEBEB',
                  padding: '4px 6px',
                  whiteSpace: 'nowrap'
                }}
              >
                {versionsList[0]}
              </Box>
              <Box
                sx={{
                  fontSize: '9px',
                  borderRadius: '30px',
                  backgroundColor: '#EBEBEB',
                  padding: '4px 6px',
                  whiteSpace: 'nowrap'
                }}
              >
                {versionsList[1]}
              </Box>
              {versionsList.length > 3 && <Box>...</Box>}
              <Box
                sx={{
                  fontSize: '9px',
                  borderRadius: '30px',
                  backgroundColor: '#EBEBEB',
                  padding: '4px 6px',
                  whiteSpace: 'nowrap'
                }}
              >
                {versionsList[versionsList.length - 1]}
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </Tooltip>
  ) : (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
        ...containerStyles
      }}
    >
      <Tooltip title={name} arrow>
        <Box
          sx={{
            width: '100%',
            height: '100%',
            aspectRatio: 1,
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '50%',
            flexDirection: 'column',
            backgroundColor: '#ffffff',
            ...roundCardStyles
          }}
          onClick={onClick}
        >
          {logo ? (
            <img src={logo} alt="img" style={logoStyles} />
          ) : (
            <Icon
              name={icon}
              sx={{
                fontSize: '32px',
                marginBottom: '10px'
              }}
            />
          )}
          <Typography
            sx={{
              width: '80%',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              textAlign: 'center',
              ...nameStyles
            }}
          >
            {name}
          </Typography>
        </Box>
      </Tooltip>
      {versionsList && (
        <Box
          sx={{
            marginTop: '16px',
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}
        >
          <Box
            sx={{
              fontSize: '9px',
              borderRadius: '30px',
              backgroundColor: '#EBEBEB',
              padding: '4px 6px',
              whiteSpace: 'nowrap'
            }}
          >
            {versionsList[0]}
          </Box>
          <Box
            sx={{
              fontSize: '9px',
              borderRadius: '30px',
              backgroundColor: '#EBEBEB',
              padding: '4px 6px',
              whiteSpace: 'nowrap'
            }}
          >
            {versionsList[1]}
          </Box>
          {versionsList.length > 3 && <Box>...</Box>}
          <Box
            sx={{
              fontSize: '9px',
              borderRadius: '30px',
              backgroundColor: '#EBEBEB',
              padding: '4px 6px',
              whiteSpace: 'nowrap'
            }}
          >
            {versionsList[versionsList.length - 1]}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export const SubMenu: React.FC<{
  backNavigation?: boolean;
  search?: boolean;
  roundedButton?: any;
  pageName?: string;
  buttonWithoutBg?: any;
  roundedButtonStyles?: any;
  transparentButtonStyles?: any;
  iconStyles?: any;
  searchStyles?: any;
  pageNameStyles?: any;
  formActionButtons?: any;
  previewActionButtons?: any;
  displayForm?: boolean;
  uploadForms?: boolean;
  exportCSV?: boolean;
  sendMail?: any;
  sendPdf?: any;
  viewPdf?: any;
  handleUploadClick?: any;
  handleExportCSVClick?: any;
  handleSendMailClick?: any;
  handleSendPdfClick?: any;
  handleViewPdfClick?: any;
  activeSelection?: any;
  setActiveSelection?: any;
  clickPreviewType?: any;
  displayStylePanel?: boolean;
  createChecklist?: boolean;
  createApplicant?: boolean;
  createOnboardingProcess?: boolean;
  createStages?: boolean;
  showAppDashboardConfig?: boolean;
  id?: any;
  showMenuIcon?: boolean;
  onMenuClick?: (event: any) => void;
  formSettings?: boolean;
  handleSettings?: any;
  showFormFillOrder?: any;
  saveFormOrder?: boolean;
  redirectLink?: any;
  subformSettings?: boolean;
  formData?: any;
  enableCreateCaregiverButton?: boolean;
}> = ({
  backNavigation,
  redirectLink,
  search,
  pageName,
  searchStyles,
  pageNameStyles,
  showMenuIcon,
  formActionButtons,
  previewActionButtons,
  displayForm,
  uploadForms,
  exportCSV,
  sendMail,
  sendPdf,
  viewPdf,
  handleUploadClick,
  handleExportCSVClick,
  handleSendMailClick,
  handleSendPdfClick,
  handleViewPdfClick,
  activeSelection,
  setActiveSelection,
  clickPreviewType,
  displayStylePanel,
  createChecklist,
  createApplicant,
  createOnboardingProcess,
  createStages,
  showAppDashboardConfig,
  id,
  roundedButton,
  roundedButtonStyles,
  buttonWithoutBg,
  transparentButtonStyles,
  iconStyles,
  onMenuClick,
  formSettings,
  handleSettings,
  showFormFillOrder,
  saveFormOrder,
  subformSettings,
  formData,
  enableCreateCaregiverButton
}) => {
  const { userType }: any = useSelector((state: AppState) => state.auth);
  const { tabletLayout, sectionViewType, deviceLayoutMode }: any = useSelector(
    (state: AppState) => state.form
  );
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const navigate = useNavigate();
  const [tabLayout, setTabLayout] = useState('');
  const [sectionType, setSectionType] = useState('');
  const [formPublishStatus, setFormPublishStatus] = useState(false);
  const [deviceLayoutType, setDeviceLayoutType] = useState(
    deviceLayoutMode || 'computer'
  );
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    setTabLayout(tabletLayout);
    setSectionType(sectionViewType);
  }, [tabletLayout, sectionViewType]);
  useEffect(() => {
    setFormPublishStatus(formData?.status);
  }, [formData]);

  const handleDisplayForm = async () => {
    const data = {
      sectionViewType: sectionType,
      tabletLayout: tabLayout
    };

    try {
      const res = await dispatch(formlayoutchanges({ id, data }));
      if (res.payload.status) {
        dispatch(updateFormLayout(res.payload.data.tabletLayout));
        dispatch(updateSectionViewType(res.payload.data.sectionViewType));
        // console.log('Form Layout Changes after api call:', res.payload);
      }
    } catch (error: any) {
      console.log(error);
    }
  };

  const handleClick = (event: any) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const handleCloseone = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  const changeFormStatus = async (FrmStatus: boolean) => {
    const data = {
      status: FrmStatus
    };

    setFormPublishStatus(FrmStatus);

    if (formData?.form_id) {
      await dispatch(updateformstatus({ id: formData?.form_id, data }));
    }
  };

  return (
    <Box
      sx={{
        backgroundColor: '#09376B',
        height: '62px',
        width: '100%',
        padding: '0px 107px'
      }}
    >
      <Box
        sx={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: '20px'
        }}
      >
        {showMenuIcon && (
          <IconButton
            aria-label="menu"
            onClick={onMenuClick}
            sx={{
              color: '#ffffff'
            }}
          >
            <Icon name="Menu" />
          </IconButton>
        )}

        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {backNavigation && (
            <Box
              onClick={() =>
                redirectLink ? navigate(redirectLink) : navigate(-1)
              }
              // onClick={() => redirectLink ? navigate(redirectLink) : history.back()}
              sx={{
                display: 'flex',
                alignItems: 'center',
                color: '#ffffff',
                gap: '10px',
                cursor: 'pointer'
              }}
            >
              <Icon name="KeyboardBackspace" fontSize="medium" />
              <Typography
                sx={{
                  fontSize: '18px'
                }}
              >
                Back
              </Typography>
            </Box>
          )}
          {pageName && (
            <Box
              sx={{
                fontSize: '26px',
                color: '#fff',
                ...pageNameStyles
              }}
            >
              {pageName}
            </Box>
          )}
        </Box>

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
            gap: '50px',
            flexGrow: 1
          }}
        >
          {search && (
            <Paper
              component="form"
              sx={{
                p: '2px 4px',
                display: 'flex',
                alignItems: 'center',
                minWidth: 400,
                borderRadius: '30px',
                backgroundColor: '#F0F0F0',
                ...searchStyles
              }}
            >
              <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
                <Icon name="Search" />
              </IconButton>
              <Divider sx={{ height: 28, m: 0.5 }} orientation="vertical" />
              <InputBase
                sx={{ ml: 1, flex: 1 }}
                placeholder=""
                inputProps={{ 'aria-label': '.....' }}
              />
              <Divider sx={{ height: 28, m: 0.5 }} orientation="vertical" />
              <IconButton sx={{ p: '10px' }} aria-label="menu">
                <Icon name="FilterList" />
              </IconButton>
            </Paper>
          )}

          {showAppDashboardConfig && (
            <Box
              sx={{
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                height: '50px',
                cursor: 'pointer',
                borderBottomWidth: '2px',
                paddingRight: '12px'
              }}
              className={`${
                activeSelection == 'styles' && 'form-active-selection'
              }`}
              onClick={() => {
                navigate(`/apps/dashboard-configuration/${id}`);
              }}
            >
              <Icon name="AddCircleOutline" fontSize="small" />
              <Typography sx={{ paddingLeft: '10px' }}>
                App Dashboard Configuration
              </Typography>
            </Box>
          )}

          {roundedButton?.status && (
            <Button
              variant="contained"
              sx={{
                borderRadius: '30px',
                backgroundColor: '#C7E9F4',
                color: '#242424',
                '&:hover': {
                  color: '#FFFFFF'
                },

                ...roundedButtonStyles
              }}
              onClick={() => navigate(roundedButton?.redirectUrl)}
            >
              {roundedButton?.icon !== '' && (
                <IconButton sx={{ ...iconStyles }}>
                  <Icon name={roundedButton?.icon} />
                </IconButton>
              )}
              <span>{roundedButton?.title}</span>
            </Button>
          )}

          {buttonWithoutBg?.status && (
            <Button
              variant="text"
              sx={{ color: '#fff', ...transparentButtonStyles }}
              href={buttonWithoutBg?.redirectUrl}
            >
              {buttonWithoutBg?.icon && (
                <IconButton sx={{ color: '#0483BA', ...iconStyles }}>
                  <Icon name={buttonWithoutBg?.icon} />
                </IconButton>
              )}
              <span>{buttonWithoutBg?.title}</span>
            </Button>
          )}

          {previewActionButtons && (
            <Box sx={{ color: 'white', display: 'flex' }}>
              <Box
                sx={{
                  color: 'white',
                  display: 'flex',
                  alignItems: 'center',
                  padding: '20px',
                  cursor: 'pointer',
                  borderBottomWidth: '2px'
                }}
                className={`${
                  deviceLayoutType == 'computer' && 'form-active-selection'
                }`}
                onClick={() => {
                  clickPreviewType('computer');
                  setDeviceLayoutType('computer');
                  dispatch(updateDeviceLayoutType('computer'));
                }}
              >
                <Icon name="RemoveFromQueue" fontSize="small" />
                <Typography sx={{ paddingLeft: '10px' }}>Desktop</Typography>
              </Box>
              <Box
                sx={{
                  color: 'white',
                  display: 'flex',
                  alignItems: 'center',
                  padding: '20px',
                  cursor: 'pointer'
                }}
                className={`${
                  deviceLayoutType == 'tab' && 'form-active-selection'
                }`}
                onClick={() => {
                  clickPreviewType('tab');
                  setDeviceLayoutType('tab');
                  dispatch(updateDeviceLayoutType('tab'));
                }}
              >
                <Icon name="TabletMac" fontSize="small" />
                <Typography sx={{ paddingLeft: '10px' }}>Tab</Typography>
              </Box>
              <Box
                sx={{
                  color: 'white',
                  display: 'flex',
                  alignItems: 'center',
                  padding: '20px',
                  cursor: 'pointer'
                }}
                className={`${
                  deviceLayoutType == 'mobile' && 'form-active-selection'
                }`}
                onClick={() => {
                  clickPreviewType('mobile');
                  setDeviceLayoutType('mobile');
                  dispatch(updateDeviceLayoutType('mobile'));
                }}
              >
                <Icon name="MobileFriendly" fontSize="small" />
                <Typography sx={{ paddingLeft: '10px' }}>Mobile</Typography>
              </Box>
              {displayForm && userType == 'organization' && (
                <>
                  <Box
                    sx={{
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      padding: '20px',
                      cursor: 'pointer'
                    }}
                    onClick={handleClick}
                  >
                    <Icon name="TableChartOutlined" fontSize="small" />
                    <Typography sx={{ paddingLeft: '10px' }}>
                      Form Display
                    </Typography>
                  </Box>
                  {/* Overlay */}
                  <Backdrop
                    open={open}
                    sx={{
                      zIndex: (theme) => theme.zIndex.drawer - 1,
                      backgroundColor: 'rgba(0, 0, 0, 0.5)'
                    }}
                  >
                    <Menu
                      anchorEl={anchorEl}
                      open={open}
                      onClose={handleCloseone}
                      sx={{
                        '& .MuiPaper-root': {
                          width: '370px',
                          padding: '20px 30px 30px 30px',
                          // height: "410px",
                          marginTop: '8px',
                          zIndex: (theme) => theme.zIndex.modal + 1
                        }
                      }}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          paddingBottom: '20px'
                        }}
                      >
                        <Typography
                          variant="h6"
                          sx={{ color: '#3B5864', fontWeight: '500' }}
                        >
                          FORM LAYOUT
                        </Typography>
                        <IconButton
                          edge="end"
                          size="small"
                          onClick={handleCloseone}
                          sx={{
                            backgroundColor: 'gray',
                            '&:hover': {
                              backgroundColor: 'lightgray'
                            }
                          }}
                        >
                          <CloseIcon
                            sx={{
                              color: 'primaryBlue.main'
                            }}
                          />
                        </IconButton>
                      </Box>
                      <Typography
                        variant="subtitle2"
                        color="textSecondary"
                        sx={{
                          paddingBottom: '8px',
                          // color: "#7e949d",
                          color: '#00000096',
                          fontSize: '15px',
                          fontWeight: '500'
                        }}
                      >
                        Select Section Style
                      </Typography>
                      <FormControl fullWidth>
                        <Select
                          value={sectionType}
                          onChange={(e) => {
                            setSectionType(e.target.value);
                            // handleCloseone();
                          }}
                          // label="Select Section Style"
                          displayEmpty
                          sx={{
                            backgroundColor: '#f7fafb',
                            '& .MuiOutlinedInput-input': {
                              backgroundColor: '#f7fafb0'
                            }
                            // "& .MuiOutlinedInput-notchedOutline": {
                            //   border: "none",
                            // },
                            // "&:hover .MuiOutlinedInput-notchedOutline": {
                            //   border: "none",
                            // },
                          }}
                        >
                          <MenuItem value="accordion">Accordion</MenuItem>
                          <MenuItem value="stepper">Stepper</MenuItem>
                          <MenuItem value="plain">Standard</MenuItem>
                        </Select>
                      </FormControl>

                      <Divider
                        sx={{
                          margin: '30px 0',
                          borderColor: 'primaryBlue.main', // Use theme color or a custom color
                          borderBottomWidth: 1
                        }}
                      />

                      <Box sx={{ marginTop: '10px' }}>
                        <Typography
                          variant="subtitle2"
                          color="textSecondary"
                          sx={{
                            paddingBottom: '8px',
                            // color: "#7e949d",
                            color: '#00000096',
                            fontSize: '15px',
                            fontWeight: '500'
                          }}
                        >
                          Tab Layout
                        </Typography>
                        <FormControl fullWidth>
                          <Select
                            value={tabLayout}
                            onChange={(e: any) => {
                              setTabLayout(e.target.value);
                              // handleCloseone();
                            }}
                            displayEmpty
                            sx={{
                              backgroundColor: '#f7fafb',
                              '& .MuiOutlinedInput-input': {
                                backgroundColor: '#f7fafb0'
                              }
                              // "& .MuiOutlinedInput-notchedOutline": {
                              //   border: "none",
                              // },
                              // "&:hover .MuiOutlinedInput-notchedOutline": {
                              //   border: "none",
                              // },
                            }}
                          >
                            <MenuItem value="singleColumn">
                              Single Column
                            </MenuItem>
                            <MenuItem value="doubleColumn">
                              Double Column
                            </MenuItem>
                          </Select>
                        </FormControl>
                      </Box>
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'flex-end',
                          marginTop: '16px'
                        }}
                      >
                        <Button
                          variant="contained"
                          onClick={() => {
                            handleDisplayForm();
                            handleCloseone();
                          }}
                          sx={{
                            backgroundColor: 'primaryBlue.main',
                            color: 'white2.main',
                            width: '100%',
                            height: '45px',
                            '&:hover': {
                              backgroundColor: 'primaryBlue.main',
                              color: 'white2.main'
                            }
                          }}
                        >
                          Apply Layout
                        </Button>
                      </Box>
                    </Menu>
                  </Backdrop>
                </>
              )}
              {displayStylePanel && userType == 'organization' && (
                <Box
                  sx={{
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    // height: "50px",
                    cursor: 'pointer',
                    borderBottomWidth: '2px'
                  }}
                  className={`${
                    activeSelection == 'styles' && 'form-active-selection'
                  }`}
                  onClick={() => setActiveSelection('styles')}
                >
                  <Icon name="PaletteOutlined" fontSize="small" />
                  <Typography sx={{ paddingLeft: '10px' }}>Styles</Typography>
                </Box>
              )}
            </Box>
          )}

          {formActionButtons && (
            <Box
              sx={{
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-end',
                gap: '30px'
              }}
            >
              <Box
                sx={{
                  color: activeSelection == 'mode' ? '#ffffff' : '#98CEF6',
                  display: 'flex',
                  alignItems: 'center',
                  height: '50px',
                  cursor: 'pointer',
                  borderBottomWidth: '2px'
                }}
                onClick={() => setActiveSelection('mode')}
              >
                <Icon name="WbSunnyOutlined" fontSize="small" />
                <Typography sx={{ paddingLeft: '10px' }}>Mode</Typography>
              </Box>
              <Box
                sx={{
                  color: activeSelection == 'preview' ? '#ffffff' : '#98CEF6',
                  display: 'flex',
                  alignItems: 'center',
                  height: '50px',
                  cursor: 'pointer',
                  borderBottomWidth: '2px'
                }}
                onClick={() => {
                  setActiveSelection('preview');
                  navigate(`/form-builder/preview-form/${id}`);
                }}
              >
                <Icon name="RemoveRedEyeOutlined" fontSize="small" />
                <Typography sx={{ paddingLeft: '10px' }}>Preview</Typography>
              </Box>
              {/* {userType == 'organization' && (
                <Box
                  sx={{
                    color: activeSelection == 'styles' ? '#ffffff' : '#98CEF6',
                    display: 'flex',
                    alignItems: 'center',
                    height: '50px',
                    cursor: 'pointer',
                    borderBottomWidth: '2px'
                  }}
                  onClick={() => {
                    setActiveSelection('styles');
                    navigate(`/preview-form/${id}`);
                  }}
                >
                  <Icon name="PaletteOutlined" fontSize="small" />
                  <Typography sx={{ paddingLeft: '10px' }}>Styles</Typography>
                </Box>
              )} */}
              <Box
                sx={{
                  color: activeSelection == 'settings' ? '#ffffff' : '#98CEF6',
                  display: 'flex',
                  alignItems: 'center',
                  height: '50px',
                  cursor: 'pointer',
                  borderBottomWidth: '2px'
                }}
                onClick={() => setActiveSelection('settings')}
              >
                <Icon
                  name="SettingsOutlined"
                  fontSize="small"
                  sx={{
                    color: activeSelection == 'settings' ? '#ffffff' : '#98CEF6'
                  }}
                />
                <Typography sx={{ padding: '10px' }}>Settings</Typography>
              </Box>
            </Box>
          )}

          {createChecklist && (
            <Box
              sx={{
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                height: '50px',
                cursor: 'pointer',
                borderBottomWidth: '2px'
              }}
              className={`${
                activeSelection == 'styles' && 'form-active-selection'
              }`}
              onClick={() => {
                navigate('/users/employee-onboarding/create-checklist');
              }}
            >
              <Icon name="AddCircleOutline" fontSize="small" />
              <Typography sx={{ paddingLeft: '10px' }}>
                Create Checklist
              </Typography>
            </Box>
          )}

          {createApplicant && (
            <Box
              sx={{
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                height: '50px',
                cursor: 'pointer',
                borderBottomWidth: '2px'
              }}
              className={`${
                activeSelection == 'styles' && 'form-active-selection'
              }`}
              onClick={() => {
                navigate('/users/employee-onboarding/create-applicant');
              }}
            >
              <Icon name="AddCircleOutline" fontSize="small" />
              <Typography sx={{ paddingLeft: '10px' }}>
                Create New Applicant
              </Typography>
            </Box>
          )}

          {createOnboardingProcess && (
            <Box
              sx={{
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                height: '50px',
                cursor: 'pointer',
                borderBottomWidth: '2px'
              }}
              className={`${
                activeSelection === 'styles' && 'form-active-selection'
              }`}
              onClick={() => {
                navigate(
                  '/users/employee-onboarding/create-onboarding-process'
                );
              }}
            >
              <Icon name="AddCircleOutline" fontSize="small" />
              <Typography sx={{ paddingLeft: '10px' }}>
                Create Onboarding Process
              </Typography>
            </Box>
          )}

          {createStages && (
            <Box
              sx={{
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                height: '50px',
                cursor: 'pointer',
                borderBottomWidth: '2px'
              }}
              className={`${
                activeSelection === 'styles' && 'form-active-selection'
              }`}
              onClick={() => {
                navigate('/users/employee-onboarding/stages-list');
              }}
            >
              <Icon name="AddCircleOutline" fontSize="small" />
              <Typography sx={{ paddingLeft: '10px' }}>
                Create Stages
              </Typography>
            </Box>
          )}

          {enableCreateCaregiverButton && (
            <Box
              sx={{
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                height: '50px',
                cursor: 'pointer',
                borderBottomWidth: '2px'
              }}
              className={`${
                activeSelection === 'styles' && 'form-active-selection'
              }`}
              onClick={() => {
                navigate('/users/caregivers/create-update');
              }}
            >
              <Icon name="AddCircleOutline" fontSize="small" />
              <Typography sx={{ paddingLeft: '10px' }}>
                Create New Caregiver
              </Typography>
            </Box>
          )}

          {uploadForms && (
            <Box
              sx={{
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                padding: '20px',
                cursor: 'pointer'
              }}
              onClick={handleUploadClick}
            >
              <Icon name="Upload" fontSize="small" />
              <Typography sx={{ paddingLeft: '10px' }}>Upload Forms</Typography>
            </Box>
          )}

          {exportCSV && (
            <Box
              sx={{
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                padding: '20px',
                cursor: 'pointer'
              }}
              onClick={handleExportCSVClick}
            >
              <Icon name="FileDownload" fontSize="small" />
              <Typography sx={{ paddingLeft: '10px' }}>
                Export to CSV
              </Typography>
            </Box>
          )}

          {sendMail && (
            <Box
              sx={{
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                padding: '20px',
                cursor: 'pointer'
              }}
              onClick={handleSendMailClick}
            >
              <Icon name="Mail" fontSize="small" />
              <Typography sx={{ paddingLeft: '10px' }}>Send Mail</Typography>
            </Box>
          )}

          {sendPdf && (
            <Box
              sx={{
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                padding: '20px',
                cursor: 'pointer'
              }}
              onClick={handleSendPdfClick}
            >
              <Icon name="PictureAsPdf" fontSize="small" />
              <Typography sx={{ paddingLeft: '10px' }}>Preview Pdf</Typography>
            </Box>
          )}

          {viewPdf && (
            <Box
              sx={{
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                padding: '20px',
                cursor: 'pointer'
              }}
              onClick={handleViewPdfClick}
            >
              <Icon name="PictureAsPdf" fontSize="small" />
              <Typography sx={{ paddingLeft: '10px' }}>View Pdf</Typography>
            </Box>
          )}

          {formSettings && (
            <Box>
              <Box
                onClick={handleClick}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                  borderBottomWidth: '2px',
                  color: '#ffffff'
                }}
              >
                <Icon
                  name="SettingsOutlined"
                  fontSize="small"
                  sx={{
                    color: '#ffffff'
                  }}
                />
                <Typography sx={{ padding: '10px' }}>Settings</Typography>
              </Box>
              <Menu
                anchorEl={anchorEl}
                open={open}
                sx={{
                  '& .MuiPaper-root': {
                    width: '240px',
                    padding: '10px',
                    height: '100px',
                    marginTop: '20px'
                  }
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    alignItems: 'center'
                  }}
                >
                  <IconButton edge="end" size="small" onClick={handleCloseone}>
                    <CloseIcon />
                  </IconButton>
                </Box>
                <Box
                  sx={{
                    background: '#ffffff',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1.5,
                    height: '40px',
                    position: 'absolute',
                    justifyContent: 'right',
                    padding: '10px'
                  }}
                >
                  <input
                    type="checkbox"
                    onClick={handleSettings}
                    style={{ cursor: 'pointer' }}
                  />
                  <Typography>Custom Form Order</Typography>
                </Box>
              </Menu>
            </Box>
          )}

          {showFormFillOrder?.status && (
            <Box
              sx={{
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                height: '50px',
                cursor: 'pointer',
                borderBottomWidth: '2px'
              }}
              className={`${
                activeSelection == 'styles' && 'form-active-selection'
              }`}
              onClick={() => {
                navigate(
                  `/form-builder/custom-form-fill-order/${
                    showFormFillOrder?.appId
                  }`
                );
              }}
            >
              <Icon name="AddCircleOutline" fontSize="small" />
              <Typography sx={{ paddingLeft: '10px' }}>
                Form Fill Order
              </Typography>
            </Box>
          )}

          {saveFormOrder && (
            <Button
              variant="outlined"
              sx={{
                p: '7px 30px',
                display: 'flex',
                alignItems: 'center',
                borderRadius: '50px',
                boxShadow: '0px 0px 2px 0px',
                color: '#ffffff'
              }}
            >
              SAVE
            </Button>
          )}

          {subformSettings && (
            <Box>
              <Box
                onClick={handleClick}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                  borderBottomWidth: '2px',
                  color: '#ffffff'
                }}
              >
                <Icon
                  name="SettingsOutlined"
                  fontSize="small"
                  sx={{
                    color: '#ffffff'
                  }}
                />
                <Typography sx={{ padding: '10px' }}>Settings</Typography>
              </Box>
              <Menu
                anchorEl={anchorEl}
                open={open}
                sx={{
                  '& .MuiPaper-root': {
                    width: '240px',
                    padding: '10px',
                    height: '100px',
                    marginTop: '20px'
                  }
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    alignItems: 'center'
                  }}
                >
                  <IconButton edge="end" size="small" onClick={handleCloseone}>
                    <CloseIcon />
                  </IconButton>
                </Box>
                <Box
                  sx={{
                    background: '#ffffff',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1.5,
                    height: '40px',
                    position: 'absolute',
                    justifyContent: 'right',
                    padding: '10px'
                  }}
                >
                  <FormControl sx={{ display: 'flex', alignItems: 'center' }}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={formPublishStatus}
                          onChange={(e) => changeFormStatus(e?.target?.checked)}
                          sx={{ cursor: 'pointer' }}
                        />
                      }
                      label={<Typography>Publish</Typography>}
                    />
                  </FormControl>
                </Box>
              </Menu>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export const Loader: React.FC<{ status: boolean; handleClose?: any }> = ({
  status,
  handleClose
}) => {
  return (
    <Backdrop
      sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
      open={status}
      onClick={handleClose}
    >
      <CircularProgress color="inherit" />
    </Backdrop>
  );
};

export const CustomAccordion: React.FC<{
  head: React.ReactNode;
  children: React.ReactNode;
}> = ({ head, children }) => {
  return (
    <Accordion>
      <AccordionSummary
        expandIcon={<Icon name="ExpandMore" />}
        aria-controls="panel1a-content"
        id="panel1a-header"
      >
        {head}
      </AccordionSummary>
      <AccordionDetails>{children}</AccordionDetails>
    </Accordion>
  );
};

export const FormTextarea: React.FC<FormTextareaProps> = ({
  name,
  label,
  placeholder,
  containerStyles,
  ...props
}) => {
  const { setFieldValue, values, errors } = useFormikContext<any>();

  const getValue = () => {
    return getIn(values, name);
  };

  const getError = () => {
    return getIn(errors, name);
  };

  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFieldValue(name, event.target.value);
  };

  return (
    <FormControl error={Boolean(getError())} sx={{ ...containerStyles }}>
      {label && <Label text={label} />}
      <BootstrapTextarea
        multiline
        name={name}
        value={getValue() || ''}
        onChange={handleChange}
        placeholder={placeholder}
        rows={4}
        {...props}
      />
      {getError() && (
        <FormHelperText
          sx={{
            textTransform: 'capitalize',
            marginLeft: 0,
            marginRight: 0
          }}
        >
          {getError()}
        </FormHelperText>
      )}
    </FormControl>
  );
};

export const SubmitButton: React.FC<{
  title: string;
  sx?: any;
  startIcon?: any;
  isLoading?: boolean;
}> = ({ title, sx, startIcon, isLoading }) => {
  // const { handleSubmit } = useFormikContext<any>();
  return (
    <Button
      variant="contained"
      // onClick={() => handleSubmit()}
      type="submit"
      sx={sx}
      startIcon={startIcon}
    >
      {isLoading ? (
        <CircularProgress size={24} sx={{ color: 'white', marginLeft: 1 }} />
      ) : (
        title
      )}
    </Button>
  );
};

export const ActiveSelectionBorderSvg: React.FC<{
  title?: string;
  name?: string;
  image?: any;
  titleStyles?: any;
  imageStyles?: any;
  onClick?: any;
  icon?: any;
  allowClick?: boolean;
}> = ({
  title,
  name,
  image,
  titleStyles,
  imageStyles,
  onClick,
  icon,
  allowClick = true
}) => {
  return (
    <Tooltip title={title || name} arrow>
      <Box
        sx={{
          position: 'relative',
          width: '160px',
          height: '158px',
          cursor: 'pointer',
          textTransform: 'capitalize'
        }}
        onClick={onClick}
      >
        <svg
          width="160"
          height="158"
          viewBox="0 0 276 272"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g filter="url(#filter0_dd_2317_6405)">
            <mask id="path-1-inside-1_2317_6405" fill="white">
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M138 4C64.0213 4 4 64.0213 4 138C4 200.234 46.4766 252.591 104.006 267.646C104.002 267.431 104 267.216 104 267C104 248.222 119.222 233 138 233C156.778 233 172 248.222 172 267C172 267.216 171.998 267.431 171.994 267.646C229.523 252.591 272 200.234 272 138C272 64.0213 211.979 4 138 4Z"
              />
            </mask>
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M138 4C64.0213 4 4 64.0213 4 138C4 200.234 46.4766 252.591 104.006 267.646C104.002 267.431 104 267.216 104 267C104 248.222 119.222 233 138 233C156.778 233 172 248.222 172 267C172 267.216 171.998 267.431 171.994 267.646C229.523 252.591 272 200.234 272 138C272 64.0213 211.979 4 138 4Z"
              fill="white"
              // fill={allowClick ? 'white' : '#1212122e'}
            />
            <path
              d="M104.006 267.646L103.5 269.581L106.055 270.25L106.006 267.609L104.006 267.646ZM171.994 267.646L169.994 267.609L169.945 270.25L172.5 269.581L171.994 267.646ZM6 138C6 65.1259 65.1259 6 138 6V2C62.9168 2 2 62.9168 2 138H6ZM104.512 265.711C47.8407 250.88 6 199.302 6 138H2C2 201.166 45.1125 254.301 103.5 269.581L104.512 265.711ZM106.006 267.609C106.002 267.406 106 267.203 106 267H102C102 267.228 102.002 267.456 102.006 267.683L106.006 267.609ZM106 267C106 249.327 120.327 235 138 235V231C118.118 231 102 247.118 102 267H106ZM138 235C155.673 235 170 249.327 170 267H174C174 247.118 157.882 231 138 231V235ZM170 267C170 267.203 169.998 267.406 169.994 267.609L173.994 267.683C173.998 267.456 174 267.228 174 267H170ZM270 138C270 199.302 228.159 250.88 171.488 265.711L172.5 269.581C230.887 254.301 274 201.166 274 138H270ZM138 6C210.874 6 270 65.1259 270 138H274C274 62.9168 213.083 2 138 2V6Z"
              // fill="#0483BA"
              fill={allowClick ? '#0483BA' : '#1212122e'}
              mask="url(#path-1-inside-1_2317_6405)"
            />
          </g>
          <defs>
            <filter
              id="filter0_dd_2317_6405"
              x="0"
              y="0"
              width="276"
              height="271.646"
              filterUnits="userSpaceOnUse"
              colorInterpolationFilters="sRGB"
            >
              <feFlood floodOpacity="0" result="BackgroundImageFix" />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feOffset dy="1" />
              <feGaussianBlur stdDeviation="1" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 0.141176 0 0 0 0 0.141176 0 0 0 0 0.141176 0 0 0 0.1 0"
              />
              <feBlend
                mode="normal"
                in2="BackgroundImageFix"
                result="effect1_dropShadow_2317_6405"
              />
              <feColorMatrix
                in="SourceAlpha"
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                result="hardAlpha"
              />
              <feOffset />
              <feGaussianBlur stdDeviation="2" />
              <feComposite in2="hardAlpha" operator="out" />
              <feColorMatrix
                type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
              />
              <feBlend
                mode="normal"
                in2="effect1_dropShadow_2317_6405"
                result="effect2_dropShadow_2317_6405"
              />
              <feBlend
                mode="normal"
                in="SourceGraphic"
                in2="effect2_dropShadow_2317_6405"
                result="shape"
              />
            </filter>
          </defs>
        </svg>
        <Box
          sx={{
            position: 'absolute',
            left: '0px',
            top: '0px',
            bottom: '0px',
            right: '0px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column'
          }}
        >
          {image ? (
            <img
              src={image}
              alt="logo"
              style={{
                width: '60px',
                height: '60px',
                ...imageStyles
              }}
            />
          ) : (
            <Icon
              name={icon || 'Widgets'}
              sx={{
                fontSize: '2.1875rem',
                // color: '#0483BA'
                color: allowClick ? '#0483BA' : '#1212122e'
              }}
            />
          )}
          <Typography
            sx={{
              fontSize: '1rem',
              width: '80%',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              textAlign: 'center',
              ...titleStyles
            }}
          >
            {title || name}
          </Typography>
        </Box>
        <Box
          sx={{
            position: 'absolute',
            bottom: '-22px',
            left: '64px'
          }}
        >
          <svg
            width="34"
            height="34"
            viewBox="0 0 54 54"
            fill={allowClick ? 'none' : '#1212122e'}
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M27 0.333496C12.28 0.333496 0.333313 12.2802 0.333313 27.0002C0.333313 41.7202 12.28 53.6668 27 53.6668C41.72 53.6668 53.6666 41.7202 53.6666 27.0002C53.6666 12.2802 41.72 0.333496 27 0.333496ZM27 48.3335C15.24 48.3335 5.66665 38.7602 5.66665 27.0002C5.66665 15.2402 15.24 5.66683 27 5.66683C38.76 5.66683 48.3333 15.2402 48.3333 27.0002C48.3333 38.7602 38.76 48.3335 27 48.3335ZM21.6666 32.7868L39.24 15.2135L43 19.0002L21.6666 40.3335L11 29.6668L14.76 25.9068L21.6666 32.7868Z"
              // fill="#0483BA"
              fill={allowClick ? '#0483BA' : '#1212122e'}
            />
          </svg>
        </Box>
      </Box>
    </Tooltip>
  );
};

export const FormInput: React.FC<FormInputType> = ({
  name,
  label,
  placeholder,
  containerStyles,
  isCreateForm,
  handleInputChange,
  activeTheme,
  description,
  required,
  ...props
}) => {
  const { handleChange, errors, values } = useFormikContext<any>();

  // Store a stable reference to the debounced function
  const debouncedHandleInputChange = useRef(
    debounce((event: React.ChangeEvent<HTMLInputElement>, formValues: any) => {
      if (handleInputChange) {
        handleInputChange(event, formValues); // Call custom handler
      }
    }, 1000)
  ).current;

  const getError = () => {
    return formatParagraph(formatText(getIn(errors, name), 'plain'));
  };

  const getValue = () => {
    return getIn(values, name); // Retrieve current value from Formik
  };

  return (
    <FormControl
      error={!!getError()}
      sx={{ ...formControlStyle, ...containerStyles }}
    >
      {label && (
        <Label text={label} activeTheme={activeTheme} required={required} />
      )}
      {description && (
        <Typography
          component="div"
          dangerouslySetInnerHTML={{ __html: description }}
        />
      )}
      <BootstrapInput
        name={name}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
          handleChange(e); // Immediately update Formik state
          // debouncedHandleInputChange(e, { ...values }); // Ensure latest values
          debouncedHandleInputChange(e, { ...values, [name]: e.target.value });
        }}
        value={getValue() || ''} // Always use Formik value
        placeholder={placeholder}
        sx={[!label && !isCreateForm && { marginTop: 4.25 }]}
        {...props}
      />
      {getError() && (
        <FormHelperText
          sx={{
            textTransform: 'capitalize',
            marginLeft: 0,
            marginRight: 0
          }}
        >
          {getError()}
        </FormHelperText>
      )}
    </FormControl>
  );
};

interface Props {
  name: string;
  label: string;
}

export const FieldDescription: React.FC<Props> = ({ name, label }) => {
  const { values, setFieldValue } = useFormikContext<any>();
  return (
    <Box sx={{ height: '140px' }}>
      <Typography>{label}</Typography>
      <ReactQuill
        theme="snow"
        value={(values as any)[name] || ''}
        onChange={(val) => setFieldValue(name, val)}
        style={{ height: '100%' }}
      />
    </Box>
  );
};

export const FormSelect: React.FC<FormSelectProps> = ({
  name,
  data,
  label,
  description,
  placeholder,
  onChange,
  containerStyles,
  labelStyles,
  iconStyles,
  menuStyles,
  required,
  ...props
}) => {
  const { values, errors, setFieldValue } = useFormikContext<any>();
  const getValue = () => {
    return getIn(values, name);
  };
  const getError = () => {
    return formatParagraph(formatText(getIn(errors, name), 'plain'));
  };

  const handleSelectChange = (event: SelectChangeEvent<any>) => {
    setFieldValue(name, event.target.value);
  };

  return (
    <FormControl
      error={!!getError()}
      sx={{ ...formControlStyle, ...containerStyles }}
    >
      {label && (
        <Label text={label} labelStyles={labelStyles} required={required} />
      )}
      {description && (
        <Typography
          component="div"
          dangerouslySetInnerHTML={{ __html: description }}
        />
      )}
      <BootstrapSelect
        name={name}
        value={getValue() || ''}
        onChange={(e: any) => {
          handleSelectChange(e);
          if (onChange) {
            onChange(e, values);
          }
        }}
        displayEmpty
        {...props}
      >
        {placeholder && (
          <MenuItem value="" disabled sx={menuStyles}>
            {placeholder}
          </MenuItem>
        )}
        {data?.map(
          (
            item: {
              value: string | number;
              label: string;
              icon?: any;
              disabled?: boolean;
            },
            index
          ) => (
            <MenuItem
              value={item.value}
              key={`${item.label}_${index + 1}`}
              sx={menuStyles}
              disabled={item.disabled}
            >
              {item?.icon && <Icon name={item?.icon} sx={iconStyles} />}{' '}
              {item.label}
            </MenuItem>
          )
        )}
      </BootstrapSelect>
      {getError() && (
        <FormHelperText
          sx={{
            textTransform: 'capitalize',
            marginLeft: 0,
            marginRight: 0
          }}
        >
          {getError()}
        </FormHelperText>
      )}
    </FormControl>
  );
};
export const FormFileInput: React.FC<FormInputType> = ({
  name,
  label,
  containerStyles,
  activeTheme,
  description,
  required,
  ...props
}) => {
  const { errors, values, setFieldValue } = useFormikContext<any>();
  const getError = () => {
    return formatParagraph(formatText(getIn(errors, `${name}.name`), 'plain'));
  };

  const getValue = () => {
    return getIn(values, name);
  };

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { files } = e.currentTarget;
    if (files?.length) {
      const file = files[0];
      const reader = new FileReader();
      reader.onloadend = () => {
        setFieldValue(name, {
          name: file.name,
          file: reader.result,
          mimetype: file.type
        });
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <FormControl
      error={!!getError()}
      sx={{ ...formControlStyle, ...containerStyles }}
    >
      {label && (
        <Label text={label} activeTheme={activeTheme} required={required} />
      )}
      {description && (
        <Typography
          component="div"
          dangerouslySetInnerHTML={{ __html: description }}
        />
      )}
      <BootstrapInput
        name={name}
        onChange={onChange}
        value={getValue()}
        type={getValue() ? 'text' : 'file'}
        disabled={getValue() ? true : !!props.isCreateForm}
        {...props}
      />

      {getError() && (
        <FormHelperText
          sx={{
            textTransform: 'capitalize',
            marginLeft: 0,
            marginRight: 0
          }}
        >
          {getError()}
        </FormHelperText>
      )}
    </FormControl>
  );
};

export const FormCheckbox: React.FC<FormCheckboxType> = ({
  name,
  data,
  label,
  description,
  containerStyles,
  activeTheme,
  ...props
}) => {
  const { handleChange, values, errors } = useFormikContext<any>();
  const getValue = () => {
    return getIn(values, name);
  };
  const getError = () => {
    return formatParagraph(formatText(getIn(errors, name), 'plain'));
  };

  return (
    <FormControl
      error={!!getError()}
      sx={{ ...formControlStyle, ...containerStyles }}
    >
      {label && <Label text={label} activeTheme={activeTheme} />}
      {description && (
        <Typography
          component="div"
          dangerouslySetInnerHTML={{ __html: description }}
        />
      )}
      <FormGroup row>
        {data.map((item, index) => {
          return (
            <FormControlLabel
              control={
                <Checkbox
                  checked={getValue()?.includes(item?.value)}
                  onChange={handleChange}
                  name={name}
                  value={item?.value}
                  {...props}
                />
              }
              label={item?.label}
              key={`${item.label}_${index}`}
            />
          );
        })}
      </FormGroup>
      {getError() && (
        <FormHelperText
          sx={{
            textTransform: 'capitalize',
            marginLeft: 0,
            marginRight: 0
          }}
        >
          {getError()}
        </FormHelperText>
      )}
    </FormControl>
  );
};

export const FormRadio: React.FC<FormRadioType> = ({
  name,
  data,
  label,
  description,
  containerStyles,
  activeTheme,
  handleRadioChange,
  ...props
}) => {
  const { handleChange, values, errors } = useFormikContext<any>();
  const getValue = () => {
    return getIn(values, name);
  };
  const getError = () => {
    return formatParagraph(formatText(getIn(errors, name), 'plain'));
  };

  const getRadioLabel = (item: any) => {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
        <span
          style={{
            padding: '9px'
          }}
        >
          {item?.label}
        </span>
        {item?.url_type == 'image' && (
          <img
            src={item?.url}
            alt="Label related image"
            style={{ marginLeft: '8px', width: '260px', height: '180px' }}
          />
        )}
        {item?.url_type == 'video' && (
          <iframe
            width="260"
            height="18-"
            src={item?.url}
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            style={{ marginLeft: '8px' }}
            title="Label related video"
          />
        )}
      </div>
    );
  };

  return (
    <FormControl
      error={!!getError()}
      sx={{ ...formControlStyle, ...containerStyles }}
    >
      {label && <Label text={label} activeTheme={activeTheme} />}
      {description && (
        <Typography
          component="div"
          dangerouslySetInnerHTML={{ __html: description }}
        />
      )}
      <FormGroup row>
        {data.map((item, index) => {
          return (
            <FormControlLabel
              control={
                <Radio
                  checked={getValue() === item.value}
                  onChange={(e) => {
                    handleChange(e);
                    if (handleRadioChange) {
                      handleRadioChange(e, values);
                    } // Call custom handler if provided
                  }}
                  name={name}
                  value={item.value}
                  {...props}
                />
              }
              label={getRadioLabel(item)}
              key={`${item.label}_${index}`}
              sx={{
                display: 'flex',
                alignItems: 'start'
              }}
            />
          );
        })}
      </FormGroup>
      {getError() && (
        <FormHelperText
          sx={{
            textTransform: 'capitalize',
            marginLeft: 0,
            marginRight: 0
          }}
        >
          {getError()}
        </FormHelperText>
      )}
    </FormControl>
  );
};

export const FormSignature: React.FC<FormSignatureType> = ({
  label,
  name,
  description,
  dateName,
  agreeName,
  imageProps,
  containerStyles,
  activeTheme,
  onsubmit,
  ...props
}) => {
  const [agreementText, setAgreementText] = React.useState(
    'I agree and understand that all electronic signatures are the legal equivalent of my manual/handwritten signature and I consent to be legally bound to this'
  );
  const [signData, setSignData] = React.useState();
  const [enableShowMore, setEnabelShowMore] = React.useState(true);
  const { values, errors, setFieldValue } = useFormikContext<any>();
  const [agreement, setAgreement] = React.useState(false);
  const theme = useTheme();

  const getValue = React.useCallback(() => {
    return getIn(values, name);
  }, [name, values]);

  const getError = () => {
    return formatParagraph(formatText(getIn(errors, name), 'plain'));
  };
  const getAgreeValue = () => {
    return getIn(values, agreeName);
  };

  const getDateValue = React.useCallback(() => {
    return getIn(values, dateName) || moment().format('YYYY-MM-DD');
  }, [dateName, values]);

  React.useEffect(() => {
    if (getValue()) {
      if (!agreement) {
        setAgreement(true);
      }
      setFieldValue(dateName, getDateValue() || moment().format('YYYY-MM-DD'));
    }
  }, [agreement, dateName, getDateValue, getValue, setFieldValue]);

  const setValue = (data: any, isEmpty: any) => {
    if (!agreement) {
      alert('Please check the agreement box before saving the signature');
    } else if (!isEmpty) {
      setOpen(true);
      setSignData(data);
    }
  };

  const setError = () => {};

  const [open, setOpen] = React.useState(false);

  const handleClose = () => {
    setOpen(false);
  };

  const handleOk = () => {
    setOpen(false);
    setFieldValue(name, signData);

    onsubmit?.({
      signData,
      date: getDateValue() || moment().format('YYYY-MM-DD')
    });
  };

  return (
    <FormControl
      error={!!getError()}
      sx={{ ...formControlStyle, ...containerStyles }}
    >
      {label && <Label text={label} activeTheme={activeTheme} />}
      {description && (
        <Typography
          component="div"
          dangerouslySetInnerHTML={{ __html: description }}
        />
      )}
      <Box
        sx={{
          display: 'flex',
          gap: 1,
          alignItems: 'flex-start',
          mb: 1.4
        }}
      >
        <Checkbox
          name={`${name}_agree`}
          id={`${name}_agree`}
          onChange={(event) => {
            setAgreement(event.target.checked);
            setFieldValue(`${name}_agree`, event.target.checked);
          }}
          checked={getAgreeValue()}
          disabled={!!getValue()}
        />
        <Typography
          variant="caption"
          component="label"
          htmlFor={`${name}_agrees`}
          sx={{
            color: activeTheme?.theme?.textColor
          }}
        >
          {agreementText}
          {enableShowMore && (
            <Link
              onClick={() => {
                setAgreementText(
                  'I agree and understand that all electronic signatures are the legal equivalent of my manual/handwritten signature and I consent to be legally bound to this agreement. I further agree my signature on this document is as valid as if I signed the document in writing. This is to be used in conjunction with the use of electronic signatures on all forms regarding any and all future documentation with a signature requirement, should I elect to have signed electronically. Under penalty of perjury, I herewith affirm that my electronic signature, and all future electronic signatures, were signed by myself with full knowledge and consent and am legally bound to these terms and conditions'
                );
                setEnabelShowMore(!enableShowMore);
              }}
              sx={{
                marginLeft: 1,
                cursor: 'pointer',
                color: activeTheme?.theme?.linkColor || 'blue'
              }}
              underline="none"
            >
              Show More
            </Link>
          )}
          {!enableShowMore && (
            <Link
              onClick={() => {
                setAgreementText(
                  'I agree and understand that all electronic signatures are the legal equivalent of my manual/handwritten signature and I consent to be legally bound to this'
                );
                setEnabelShowMore(!enableShowMore);
              }}
              sx={{
                marginLeft: 1,
                cursor: 'pointer',
                color: activeTheme?.theme?.linkColor || 'blue'
              }}
              underline="none"
            >
              Show Less
            </Link>
          )}
        </Typography>
      </Box>
      <div
        style={{
          border: `2px solid ${theme.palette.divider}`,
          borderRadius: theme.spacing(0.6)
        }}
      >
        {getValue() ? (
          <img src={getValue()} {...imageProps} />
        ) : (
          <>
            <Signature
              onClear={setError}
              onDone={setValue}
              agreement={agreement}
              {...props}
            />
            <Dialog open={open} onClose={handleClose}>
              <DialogTitle
                sx={{
                  color: activeTheme?.theme?.textColor || '#000000'
                }}
              >
                Are you sure to confirm with this signature?
              </DialogTitle>
              <DialogContent>
                <DialogContentText
                  sx={{
                    color: activeTheme?.theme?.textColor || '#000000'
                  }}
                >
                  Signature is not editable content. Please verify once before
                  click on 'Yes' Button.
                </DialogContentText>
              </DialogContent>
              <DialogActions>
                <Button
                  onClick={handleClose}
                  sx={{
                    backgroundColor:
                      activeTheme?.theme?.buttonColor || '#0483BA',
                    color: activeTheme?.theme?.buttonTextColor || '#ffffff',
                    borderRadius:
                      activeTheme?.theme?.buttonStyle == 'rounded'
                        ? '30px'
                        : '0px'
                  }}
                >
                  No
                </Button>
                <Button
                  onClick={handleOk}
                  autoFocus
                  sx={{
                    backgroundColor:
                      activeTheme?.theme?.buttonColor || '#0483BA',
                    color: activeTheme?.theme?.buttonTextColor || '#ffffff',
                    borderRadius:
                      activeTheme?.theme?.buttonStyle == 'rounded'
                        ? '30px'
                        : '0px'
                  }}
                >
                  Yes
                </Button>
              </DialogActions>
            </Dialog>
          </>
        )}
      </div>
      <FormInput
        name={dateName}
        type="text"
        disabled
        label=""
        placeholder="Signed On"
        value={moment(getDateValue()).format('MM-DD-YYYY')}
      />
      {getError() && (
        <FormHelperText
          sx={{
            textTransform: 'capitalize',
            marginLeft: 0,
            marginRight: 0
          }}
        >
          {getError()}
        </FormHelperText>
      )}
    </FormControl>
  );
};

export const FormParagraph: React.FC<FormParagraphType> = ({
  label,
  description,
  containerStyles,
  activeTheme
}) => {
  return (
    <FormControl sx={{ ...formControlStyle, ...containerStyles }}>
      <Label text={label} activeTheme={activeTheme} />
      <div dangerouslySetInnerHTML={{ __html: description }} />
    </FormControl>
  );
};

export const FormToggle: React.FC<FormToggleType> = ({
  name,
  label,
  description,
  data = [],
  containerStyles,
  isCreateForm,
  activeTheme
}) => {
  const { setFieldValue, errors, values } = useFormikContext<any>();

  const getError = () => {
    return formatParagraph(formatText(getIn(errors, name), 'plain'));
  };

  const getValue = () => {
    return getIn(values, name);
  };

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.currentTarget.checked;
    if (data && data.length === 2) {
      if (value === false) {
        setFieldValue(name, data[0].value);
      } else {
        setFieldValue(name, data[1].value);
      }
    } else if (isCreateForm) {
      setFieldValue(name, value);
    } else if (value === false) {
      setFieldValue(name, 'no');
    } else {
      setFieldValue(name, 'yes');
    }
  };

  const render = () => {
    if (data && data.length === 2) {
      return (
        <>
          <Typography
            component="label"
            sx={{
              color: activeTheme?.theme?.textColor || '#000000'
            }}
          >
            {data[0].label}
          </Typography>
          <Switch
            checked={
              getValue()
                ?.toLowerCase()
                ?.includes(data[1].value?.toLowerCase()) || false
            }
            onChange={onChange}
          />
          <Typography
            component="label"
            sx={{
              color: activeTheme?.theme?.textColor || '#000000'
            }}
          >
            {data[1].label}
          </Typography>
        </>
      );
    }
    if (isCreateForm) {
      return <Switch value={getValue()} onChange={onChange} />;
    }
    return (
      <>
        <Typography
          component="label"
          sx={{
            color: activeTheme?.theme?.textColor || '#000000'
          }}
        >
          No
        </Typography>
        <Switch value={getValue()?.includes('yes')} onChange={onChange} />
        <Typography
          component="label"
          sx={{
            color: activeTheme?.theme?.textColor || '#000000'
          }}
        >
          Yes
        </Typography>
      </>
    );
  };
  return (
    <FormControl
      error={!!getError()}
      sx={{ ...formControlStyle, ...containerStyles }}
    >
      {label && <Label text={label} activeTheme={activeTheme} />}
      {description && (
        <Typography
          component="div"
          dangerouslySetInnerHTML={{ __html: description }}
        />
      )}
      <Box sx={{ display: 'flex', alignItems: 'center' }}>{render()}</Box>
      {getError() && (
        <FormHelperText
          sx={{
            textTransform: 'capitalize',
            marginLeft: 0,
            marginRight: 0
          }}
        >
          {getError()}
        </FormHelperText>
      )}
    </FormControl>
  );
};

const PhoneInput = styled(MuiTelInput)(({ theme }) => ({
  '&': {
    width: '100%'
  },
  'label + &': {
    marginTop: theme.spacing(1.25),
    backgroundColor:
      theme.palette.mode === 'light'
        ? theme.palette.input?.main
        : theme.palette.input?.dark
  },
  '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
    borderColor: `${theme.palette.primary.main} !important`
  },
  '& .MuiOutlinedInput-notchedOutline': {
    borderWidth: '1px !important',
    borderColor: theme.palette.divider
  },
  '& .MuiInputBase-root:hover .MuiOutlinedInput-notchedOutline': {
    borderColor: theme.palette.divider
  },
  '& .MuiInputBase-input': {
    padding: `${theme.spacing(1.25)} ${theme.spacing(1.5)}`
  },
  '& .Mui-focused': {
    boxShadow: `${alpha(
      theme.palette.primary.main,
      0.25
    )} 0 0 0 0.2rem !important`,
    borderColor: `${theme.palette.primary.main}`
  }
}));

export const FormPhoneInput: React.FC<FormPhoneInputType> = ({
  name,
  label,
  description,
  placeholder,
  containerStyles,
  activeTheme,
  required,
  ...props
}) => {
  const { errors, values, setFieldValue, setFieldError, setFieldTouched } =
    useFormikContext<any>();
  const getError = () => {
    return formatParagraph(formatText(getIn(errors, name), 'plain'));
  };

  const getValue = () => {
    return getIn(values, name);
  };

  const handleChange = (newValue: any) => {
    const isValid = matchIsValidTel(newValue);

    setFieldTouched(name, true, false);

    if (isValid) {
      setFieldValue(name, newValue);
      setFieldError(name, undefined);
    } else {
      setFieldValue(name, newValue, false);
      setFieldError(
        name,
        `Please enter a valid ${label?.replace('*', '')?.toLowerCase()}`
      );
    }
  };

  // Add a validation function
  const validatePhoneNumber = (value: string) => {
    if (value && !matchIsValidTel(value)) {
      return `Please enter a valid ${label?.replace('*', '')?.toLowerCase()}`;
    }
  };

  return (
    <FormControl
      error={!!getError()}
      sx={{ ...formControlStyle, ...containerStyles }}
    >
      {label && (
        <Label text={label} activeTheme={activeTheme} required={required} />
      )}
      {description && (
        <Typography
          component="div"
          dangerouslySetInnerHTML={{ __html: description }}
        />
      )}
      {/* <PhoneInput
        name={name}
        onChange={handleChange}
        value={getValue()}
        defaultCountry="US"
        placeholder={placeholder || ""}
        {...props}
      /> */}
      <Field name={name} validate={validatePhoneNumber}>
        {({ field }: FieldProps) => (
          <PhoneInput
            {...field}
            onChange={(value) => handleChange(value)}
            value={getValue()}
            defaultCountry="US"
            placeholder={placeholder || ''}
            {...props}
          />
        )}
      </Field>
      {getError() && (
        <FormHelperText
          sx={{
            textTransform: 'capitalize',
            marginLeft: 0,
            marginRight: 0
          }}
        >
          {getError()}
        </FormHelperText>
      )}
    </FormControl>
  );
};

export const AppField: React.FC<APPFIELD> = ({
  field,
  groupKey,
  index,
  findex: aindex,
  activeTheme
}) => {
  let component;
  // const theme = useTheme();
  const dispatch = useDispatch<AppDispatch>();
  const { values, setFieldValue } = useFormikContext<any>();
  const {
    countriesList,
    statesList,
    citiesList,
    selectedCountry,
    tabletLayout,
    deviceLayoutMode
  } = useSelector((state: RootState) => state.form);
  const { assessmentClients }: any = useSelector(
    (state: AppState) => state.clients
  );
  const [countriesData, setCountriesData] = useState([]);
  const [statesData, setStatesData] = useState([]);
  const [citiesData, setCitiesData] = useState([]);
  // const [paragraphText, setParagraphText] = useState('');

  useEffect(() => {
    if (countriesList && countriesData?.length === 0) {
      const countries = countriesList?.map((country: any) => ({
        value: country?.isoCode,
        label: country?.name
      }));
      setCountriesData(countries);
    }
    if (statesList && statesData?.length === 0) {
      const states = statesList?.map((state: any) => ({
        value: state?.isoCode,
        label: state?.name
      }));
      setStatesData(states);
    }
    if (citiesList && citiesData?.length === 0) {
      const cities = citiesList?.map((city: any) => ({
        value: city?.isoCode,
        label: city?.name
      }));
      setCitiesData(cities);
    }
  }, [countriesList, statesList, citiesList, assessmentClients]);

  const getStatesData = async (countryCode: any) => {
    await dispatch(getstateslist(countryCode)).then((res: any) => {
      if (res.payload) {
        const states = res.payload?.map((state: any) => ({
          value: state?.isoCode,
          label: state?.name
        }));
        setStatesData(states);
      }
    });
  };

  const getCitiesData = async (countryCode: any, stateCode: any) => {
    await dispatch(getcitieslist({ countryCode, stateCode })).then(
      (res: any) => {
        if (res.payload) {
          const cities = res.payload?.map((city: any) => ({
            value: city?.isoCode,
            label: city?.name
          }));
          setCitiesData(cities);
        }
      }
    );
  };

  const handleCountrySelect = (e: any) => {
    dispatch(updateSelectedCountry(e.target.value));
    getStatesData(e.target.value);
  };
  const handleStateSelect = (e: any) => {
    dispatch(updateSelectedState(e.target.value));
    getCitiesData(selectedCountry, e.target.value);
  };
  const handleCitySelect = (e: any) => {
    dispatch(updateSelectedCity(e.target.value));
  };

  useEffect(() => {
    if (field?.default_country) {
      dispatch(updateSelectedCountry(field?.default_country));
      if (field?.input_type === 'state') {
        getStatesData(field?.default_country);
      }
    }
    if (field?.default_state) {
      dispatch(updateSelectedState(field?.default_state));
      if (field?.input_type === 'city') {
        getCitiesData(field?.default_country, field?.default_state);
      }
    }
  }, []);

  const generateSignatureNames = (type: 'date' | 'agree'): string => {
    let fieldName = `${field.name}_${type}`;
    if (field.is_iterative_or_not) {
      fieldName = `${fieldName}.${aindex}`;
    }
    if (isUndefined(index) === false) {
      fieldName = `${index}.${fieldName}`;
    }
    fieldName = `${groupKey}.${fieldName}`;
    return fieldName;
  };

  const generateFieldName = (): string => {
    let fieldName = field.name;
    if (field.is_iterative_or_not) {
      fieldName = `${fieldName}.${aindex}`;
    }
    if (isUndefined(index) === false) {
      fieldName = `${index}.${fieldName}`;
    }
    fieldName = `${groupKey}.${fieldName}`;
    return fieldName;
  };

  const fieldName = generateFieldName();

  const fieldLabel = () => {
    const label = capitalizeWords(
      formatText(removeHtmlTags(field.label), 'plain')
    );
    const labelText = field?.validation_schema?.required
      ? `${label?.toLowerCase()} *`
      : label?.toLowerCase();
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
        <span>{labelText}</span>
        {field?.label_url_type == 'image' && (
          <img
            src={field?.label_url}
            alt="Label related image"
            style={{ marginLeft: '8px', width: '260px', height: '180px' }}
          />
        )}
        {field?.label_url_type == 'video' && (
          <iframe
            width="260"
            height="180"
            src={field?.label_url}
            frameBorder="0"
            allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            style={{ marginLeft: '8px' }}
            title="Label related video"
          />
        )}
      </div>
    );
  };

  const label = fieldLabel();
  // const [value, setValue] = React.useState<Dayjs | null>(null);
  const handleChange = (newValue: any) => {
    const { value, name } = newValue.target;
    const keys = name.split(/[[\].]+/).filter(Boolean);
    const updatedVales = JSON.parse(JSON.stringify(values));

    updatedVales[keys[0]][keys[1]] = value;

    const isValid = matchIsValidTel(value);

    if (isValid) {
      setFieldValue(fieldName, value);
    } else {
      setFieldValue(fieldName, value, false);
    }
  };

  const handleInputChange = (e: any) => {
    const { value, name } = e.target;
    const keys = name.split(/[[\].]+/).filter(Boolean);
    const updatedVales = JSON.parse(JSON.stringify(values));

    updatedVales[keys[0]][keys[1]] = value;
    if (Array.isArray(field?.auto_fill_dependencies)) {
      field?.auto_fill_dependencies?.forEach((dependency: any) => {
        const fname = `${dependency?.source_section_key}.${dependency?.source_field_name}`;
        setFieldValue(fname, value);
      });
    }

    // setFieldValue(fieldName, value);
  };

  const handleMobileInputChange = (value: any, name: any) => {
    if (Array.isArray(field?.auto_fill_dependencies)) {
      field?.auto_fill_dependencies?.forEach((dependency: any) => {
        const fname = `${dependency?.source_section_key}.${dependency?.source_field_name}`;
        setFieldValue(fname, value);
      });
    }
    console.log('Name: ', name);
  };

  const handleOptionChange = (e: any) => {
    const { value, name } = e.target;
    const keys = name.split(/[[\].]+/).filter(Boolean);
    const updatedVales = JSON.parse(JSON.stringify(values));

    updatedVales[keys[0]][keys[1]] = value;
    if (Array.isArray(field?.auto_fill_dependencies)) {
      field?.auto_fill_dependencies?.forEach((dependency: any) => {
        const fname = `${dependency?.source_section_key}.${dependency?.source_field_name}`;
        console.log(
          'email target: ',
          value,
          name,
          field,
          values,
          fname,
          dependency
        );
        setFieldValue(fname, value);
      });
    }
  };

  switch (field.type?.toLowerCase()) {
    case 'input':
      if (field.input_type === 'number') {
        component = (
          <FormInput
            type="number"
            name={fieldName}
            label={label}
            description={field?.description_status ? field?.description : ''}
            activeTheme={activeTheme}
            handleInputChange={handleInputChange}
            containerStyles={{
              // margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
              width: {
                xs:
                  tabletLayout === 'doubleColumn' &&
                  deviceLayoutMode !== 'mobile'
                    ? '48%'
                    : '100%'
                // sm: `(100% / 2 - ${theme.spacing(1.2)})`,
              },
              color: activeTheme?.theme?.textColor || '#000000',
              marginRight:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '2%'
                  : '0px',
              padding: '8px 0px'
            }}
          />
        );
      } else if (field.input_type === 'email') {
        component = (
          <FormInput
            name={fieldName}
            type="email"
            label={label}
            description={field?.description_status ? field?.description : ''}
            activeTheme={activeTheme}
            handleInputChange={handleInputChange}
            containerStyles={{
              // margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
              width: {
                xs:
                  tabletLayout === 'doubleColumn' &&
                  deviceLayoutMode !== 'mobile'
                    ? '48%'
                    : '100%'
                // sm: `calc(100% / 2 - ${theme.spacing(1.2)})`,
              },
              color: activeTheme?.theme?.textColor || '#000000',
              marginRight:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '2%'
                  : '0px',
              padding: '8px 0px'
            }}
          />
        );
      } else if (field.input_type === 'checkbox') {
        component = (
          <FormCheckbox
            data={field?.options}
            name={fieldName}
            label={label}
            description={field?.description_status ? field?.description : ''}
            activeTheme={activeTheme}
            handleOptionChange={handleOptionChange}
            containerStyles={{
              width: {
                xs:
                  tabletLayout === 'doubleColumn' &&
                  deviceLayoutMode !== 'mobile'
                    ? '48%'
                    : '100%'
                // sm: `calc(100% / 2 - ${theme.spacing(1.2)})`,
              },
              color: activeTheme?.theme?.textColor || '#000000',
              marginRight:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '2%'
                  : '0px',
              padding: '8px 0px'
            }}
          />
        );
      } else if (field.input_type === 'radio') {
        component = (
          <FormRadio
            data={field?.options}
            name={fieldName}
            label={label}
            description={field?.description_status ? field?.description : ''}
            activeTheme={activeTheme}
            // onChange={handleChange}
            onChange={(e: any) => {
              handleChange(e);
              handleOptionChange(e);
            }}
            containerStyles={{
              width: {
                xs:
                  tabletLayout === 'doubleColumn' &&
                  deviceLayoutMode !== 'mobile'
                    ? '48%'
                    : '100%'
                // sm: `calc(100% / 2 - ${theme.spacing(1.2)})`,
              },
              color: activeTheme?.theme?.textColor || '#000000',
              marginRight:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '2%'
                  : '0px',
              padding: '8px 0px'
            }}
          />
        );
      } else if (field.input_type === 'file') {
        component = (
          <FormFileInput
            name={fieldName}
            type="file"
            label={label}
            description={field?.description_status ? field?.description : ''}
            activeTheme={activeTheme}
            // handleInputChange={handleInputChange}
            containerStyles={{
              // margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
              width: {
                xs:
                  tabletLayout === 'doubleColumn' &&
                  deviceLayoutMode !== 'mobile'
                    ? '48%'
                    : '100%'
                // sm: `calc(100% / 2 - ${theme.spacing(1.2)})`,
              },
              color: activeTheme?.theme?.textColor || '#000000',
              marginRight:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '2%'
                  : '0px',
              padding: '8px 0px'
            }}
          />
        );
      } else if (field.input_type === 'date') {
        component = (
          <FormInput
            name={fieldName}
            type={field?.input_type}
            description={field?.description_status ? field?.description : ''}
            label={label}
            activeTheme={activeTheme}
            handleInputChange={handleInputChange}
            containerStyles={{
              width: {
                xs:
                  tabletLayout === 'doubleColumn' &&
                  deviceLayoutMode !== 'mobile'
                    ? '48%'
                    : '100%'
              },
              color: activeTheme?.theme?.textColor || '#000000',
              marginRight:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '2%'
                  : '0px'
            }}
          />
        );
      } else if (field.input_type === 'time') {
        component = (
          <FormInput
            name={fieldName}
            type={field?.input_type}
            description={field?.description_status ? field?.description : ''}
            label={label}
            activeTheme={activeTheme}
            handleInputChange={handleInputChange}
            containerStyles={{
              width: {
                xs:
                  tabletLayout === 'doubleColumn' &&
                  deviceLayoutMode !== 'mobile'
                    ? '48%'
                    : '100%'
              },
              color: activeTheme?.theme?.textColor || '#000000',
              marginRight:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '2%'
                  : '0px'
            }}
          />
        );
      } else if (field.input_type === 'datetime-local') {
        component = (
          <FormInput
            name={fieldName}
            type={field?.input_type}
            description={field?.description_status ? field?.description : ''}
            label={label}
            activeTheme={activeTheme}
            handleInputChange={handleInputChange}
            containerStyles={{
              width: {
                xs:
                  tabletLayout === 'doubleColumn' &&
                  deviceLayoutMode !== 'mobile'
                    ? '48%'
                    : '100%'
              },
              color: activeTheme?.theme?.textColor || '#000000',
              marginRight:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '2%'
                  : '0px'
            }}
          />
        );
      } else if (field.input_type === 'phone') {
        component = (
          <FormPhoneInput
            name={fieldName}
            label={label}
            description={field?.description_status ? field?.description : ''}
            activeTheme={activeTheme}
            handleMobileInputChange={handleMobileInputChange}
            containerStyles={{
              // margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
              width: {
                xs:
                  tabletLayout === 'doubleColumn' &&
                  deviceLayoutMode !== 'mobile'
                    ? '48%'
                    : '100%'
                // sm: `calc(100% / 2 - ${theme.spacing(1.2)})`,
              },
              color: activeTheme?.theme?.textColor || '#000000',
              marginRight:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '2%'
                  : '0px',
              padding: '8px 0px'
            }}
          />
        );
      } else if (field.input_type === 'fax') {
        component = (
          <FormPhoneInput
            name={fieldName}
            label={label}
            description={field?.description_status ? field?.description : ''}
            activeTheme={activeTheme}
            handleMobileInputChange={handleMobileInputChange}
            containerStyles={{
              // margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
              width: {
                xs:
                  tabletLayout === 'doubleColumn' &&
                  deviceLayoutMode !== 'mobile'
                    ? '48%'
                    : '100%'
                // sm: `calc(100% / 2 - ${theme.spacing(1.2)})`,
              },
              color: activeTheme?.theme?.textColor || '#000000',
              marginRight:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '2%'
                  : '0px',
              padding: '8px 0px'
            }}
          />
        );
      } else if (field.input_type === 'url') {
        component = (
          <Box
            sx={{
              width: '100%',
              marginTop: '10px',
              marginBottom: '10px',
              color: activeTheme?.theme?.textColor || '#000000'
            }}
          >
            {label}
            {field?.description_status && (
              <Typography
                component="div"
                sx={{ color: activeTheme?.theme?.textColor || '#000000' }}
                dangerouslySetInnerHTML={{ __html: field?.description || '' }}
              />
            )}
            {field?.value && (
              <Box>
                <a href={field?.value} target="_blank" rel="noreferrer">
                  {field?.value}
                </a>
              </Box>
            )}
          </Box>
        );
      } else {
        component = (
          <FormInput
            name={fieldName}
            type="text"
            label={label}
            description={field?.description_status ? field?.description : ''}
            activeTheme={activeTheme}
            handleInputChange={handleInputChange}
            containerStyles={{
              dispaly: 'flex',
              flexDirection: 'column',
              justifyContent: 'end',
              // margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,

              width: {
                xs:
                  tabletLayout === 'doubleColumn' &&
                  deviceLayoutMode !== 'mobile'
                    ? '48%'
                    : '100%'
                // sm: `calc(100% / 2 - ${theme.spacing(1.2)})`,
              },
              color: activeTheme?.theme?.textColor || '#000000',
              marginRight:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '2%'
                  : '0px',
              padding: '8px 0px'
            }}
          />
        );
      }
      break;
    case 'toggle':
      component = (
        <FormToggle
          name={fieldName}
          label={label}
          description={field?.description_status ? field?.description : ''}
          activeTheme={activeTheme}
          data={field?.options}
          onChange={handleChange}
          handleOptionChange={handleOptionChange}
          containerStyles={{
            width:
              tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                ? '48%'
                : '100%',
            // margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
            color: activeTheme?.theme?.textColor || '#000000',
            marginRight:
              tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                ? '2%'
                : '0px',
            padding: '8px 0px'
          }}
        />
      );
      break;
    case 'download':
      component = (
        <Box
          sx={{
            width: '100%',
            marginTop: '10px',
            marginBottom: '10px',
            color: activeTheme?.theme?.textColor || '#000000'
          }}
        >
          {label}
          {field?.description_status && (
            <Typography
              component="div"
              sx={{ color: activeTheme?.theme?.textColor || '#000000' }}
              dangerouslySetInnerHTML={{ __html: field?.description || '' }}
            />
          )}
          {!field?.value && (
            <Box>
              <p>Document not Uploaded</p>
            </Box>
          )}
          {field?.value && (
            <Box>
              <p>Document uploaded successfully</p>
            </Box>
          )}
        </Box>
      );
      break;
    case 'textarea':
      component = (
        <FormInput
          name={fieldName}
          type="text"
          label={label}
          description={field?.description_status ? field?.description : ''}
          activeTheme={activeTheme}
          multiline
          containerStyles={{
            width:
              tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                ? '48%'
                : '100%',
            // margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
            color: activeTheme?.theme?.textColor || '#000000',
            marginRight:
              tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                ? '2%'
                : '0px',
            padding: '8px 0px'
          }}
        />
      );
      break;
    case 'select':
      component = (
        <FormSelect
          name={fieldName}
          label={label}
          description={field?.description_status ? field?.description : ''}
          activeTheme={activeTheme}
          data={field?.options}
          onChange={handleChange}
          containerStyles={{
            width:
              tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                ? '48%'
                : '100%',
            // margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
            color: activeTheme?.theme?.textColor || '#000000',
            marginRight:
              tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                ? '2%'
                : '0px',
            padding: '8px 0px'
          }}
          labelStyles={{
            color: activeTheme?.theme?.textColor || '#000000'
          }}
        />
      );
      break;
    case 'clients_list':
      const options: { label: string; value: any }[] = [];
      assessmentClients.forEach((element: any) => {
        const opt = {
          label: element.name + ' | ' + element.mobile_number,
          value: element.client_id
        };
        options.push(opt);
      });
      component = (
        <FormSelect
          name={fieldName}
          label={label}
          description={field?.description_status ? field?.description : ''}
          activeTheme={activeTheme}
          data={options}
          onChange={handleChange}
          containerStyles={{
            width:
              tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                ? '48%'
                : '100%',
            // margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
            color: activeTheme?.theme?.textColor || '#000000',
            marginRight:
              tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                ? '2%'
                : '0px',
            padding: '8px 0px'
          }}
          labelStyles={{
            color: activeTheme?.theme?.textColor || '#000000'
          }}
        />
      );
      break;
    case 'signature':
      component = (
        <FormSignature
          name={fieldName}
          label={label}
          description={field?.description_status ? field?.description : ''}
          activeTheme={activeTheme}
          containerStyles={{
            width: '100%',
            // margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
            color: activeTheme?.theme?.textColor || '#000000'
          }}
          canvasProps={{
            width: getCanvasWidth(),
            height: 180
          }}
          imageProps={{
            width: getCanvasWidth(),
            height: 180
          }}
          dateName={generateSignatureNames('date')}
          agreeName={generateSignatureNames('agree')}
        />
      );
      break;
    case 'image':
      component = (
        <FormFileInput
          name={fieldName}
          type="file"
          label={label}
          description={field?.description_status ? field?.description : ''}
          activeTheme={activeTheme}
          containerStyles={{
            // margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
            width:
              tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                ? '48%'
                : '100%',
            marginRight:
              tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                ? '2%'
                : '0px',
            padding: '8px 0px',
            color: activeTheme?.theme?.textColor || '#000000'
          }}
        />
      );
      break;
    case 'paragraph':
      component = (
        <>
          {field?.description && (
            <FormParagraph
              label={label}
              activeTheme={activeTheme}
              description={field?.description}
              containerStyles={{
                width: '100%',
                // margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
                color: activeTheme?.theme?.textColor || '#000000'
              }}
            />
          )}
        </>
      );
      break;
    case 'editor':
      component = (
        <Box
          sx={{
            width: {
              xs:
                tabletLayout === 'singleColumn' && deviceLayoutMode !== 'mobile'
                  ? '100%'
                  : '100%'
            },
            color: activeTheme?.theme?.textColor || '#000000',
            marginRight:
              tabletLayout === 'singleColumn' && deviceLayoutMode !== 'mobile'
                ? '0px'
                : '0px'
          }}
        >
          {label && (
            <Typography
              sx={{
                color: activeTheme?.theme?.textColor || '#000000'
              }}
            >
              {label}
            </Typography>
          )}
          {field?.description_status && (
            <Typography
              component="div"
              sx={{ color: activeTheme?.theme?.textColor || '#000000' }}
              dangerouslySetInnerHTML={{ __html: field?.description || '' }}
            />
          )}
          <ReactQuill
            theme="snow"
            value={getIn(values, fieldName)}
            onChange={(editorValue: string) =>
              setFieldValue(fieldName, editorValue)
            }
          />
        </Box>
      );
      break;
    case 'scribble':
      component = (
        <Box
          sx={{
            width: {
              xs:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '48%'
                  : '100%'
            },
            color: activeTheme?.theme?.textColor || '#000000',
            marginRight:
              tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                ? '2%'
                : '0px',
            marginBottom: '20px',
            marginTop: '20px'
          }}
        >
          {field?.description_status && (
            <Typography
              component="div"
              sx={{ color: activeTheme?.theme?.textColor || '#000000' }}
              dangerouslySetInnerHTML={{ __html: field?.description || '' }}
            />
          )}
          <Button variant="contained">{label}</Button>
        </Box>
      );
      break;
    case 'address':
      if (field.input_type === 'country') {
        component = (
          <FormSelect
            name={fieldName}
            label={label}
            placeholder="Select Country"
            description={field?.description_status ? field?.description : ''}
            data={countriesData}
            activeTheme={activeTheme}
            containerStyles={{
              width:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '48%'
                  : '100%',
              color: activeTheme?.theme?.textColor || '#000000',
              marginRight:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '2%'
                  : '0px',
              padding: '8px 0px'
            }}
            labelStyles={{
              color: activeTheme?.theme?.textColor || '#000000'
            }}
            onChange={handleCountrySelect}
          />
        );
      } else if (field.input_type === 'state') {
        component = (
          <FormSelect
            name={fieldName}
            label={label}
            placeholder="Select State"
            description={field?.description_status ? field?.description : ''}
            data={statesData}
            activeTheme={activeTheme}
            containerStyles={{
              width:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '48%'
                  : '100%',
              color: activeTheme?.theme?.textColor || '#000000',
              marginRight:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '2%'
                  : '0px',
              padding: '8px 0px'
            }}
            labelStyles={{
              color: activeTheme?.theme?.textColor || '#000000'
            }}
            onChange={handleStateSelect}
          />
        );
      } else if (field.input_type === 'city') {
        component = (
          <FormSelect
            name={fieldName}
            label={label}
            placeholder="Select City"
            description={field?.description_status ? field?.description : ''}
            data={citiesData}
            activeTheme={activeTheme}
            containerStyles={{
              width:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '48%'
                  : '100%',
              color: activeTheme?.theme?.textColor || '#000000',
              marginRight:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '2%'
                  : '0px',
              padding: '8px 0px'
            }}
            labelStyles={{
              color: activeTheme?.theme?.textColor || '#000000'
            }}
            onChange={handleCitySelect}
          />
        );
      } else if (field.input_type === 'zipcode') {
        component = (
          <FormInput
            name={fieldName}
            type="text"
            label={label}
            description={field?.description_status ? field?.description : ''}
            activeTheme={activeTheme}
            containerStyles={{
              // margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
              width: {
                xs:
                  tabletLayout === 'doubleColumn' &&
                  deviceLayoutMode !== 'mobile'
                    ? '48%'
                    : '100%'
                // sm: `calc(100% / 2 - ${theme.spacing(1.2)})`,
              },
              color: activeTheme?.theme?.textColor || '#000000',
              marginRight:
                tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                  ? '2%'
                  : '0px',
              padding: '8px 0px'
            }}
          />
        );
      }
      break;
    default:
      <FormInput
        name={fieldName}
        type="text"
        label={label}
        description={field?.description_status ? field?.description : ''}
        activeTheme={activeTheme}
        containerStyles={{
          // margin: `${theme.spacing(1.5)} ${theme.spacing(0.6)}`,
          width: {
            xs:
              tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
                ? '48%'
                : '100%'
            // sm: `calc(100% / 2 - ${theme.spacing(1.2)})`,
          },
          color: activeTheme?.theme?.textColor || '#000000',
          marginRight:
            tabletLayout === 'doubleColumn' && deviceLayoutMode !== 'mobile'
              ? '2%'
              : '0px'
        }}
      />;
  }

  return <>{component}</>;
};

export const AddSeparator: React.FC<{
  onPress: () => void;
  lineStyle?: any;
}> = ({ onPress, lineStyle }) => {
  return (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <Divider style={{ flexShrink: 1, ...lineStyle }} />
      <Tooltip title="Add Section Repeat Fields" arrow>
        <IconButton onClick={onPress}>
          <Add sx={{ color: 'primary.main' }} />
        </IconButton>
      </Tooltip>
    </Box>
  );
};

export const RemoveSeparator: React.FC<{
  onPress: () => void;
  lineStyle?: any;
}> = ({ onPress, lineStyle }) => {
  return (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <Divider style={{ flexShrink: 1, ...lineStyle }} />
      <Tooltip title="Remove Section Repeat Fields" arrow>
        <IconButton onClick={onPress}>
          <RemoveCircleOutline sx={{ color: 'primary.main' }} />
          {/* <Remove sx={{color: 'primary.main'}} /> */}
        </IconButton>
      </Tooltip>
    </Box>
  );
};

export const FormFieldArray: React.FC<{
  group?: GROUP;
  name: string;
  minLength: number;
  emptyField?: any;
  from?: 'SECTION' | 'FIELD';
  maxLength: number | null | undefined;
  children: (index: number) => React.ReactNode;
  activeTheme?: any;
}> = ({ group, name, children, maxLength, emptyField, activeTheme }) => {
  const getValue = (values: any) => {
    return getIn(values, name);
  };
  return (
    <FieldArray
      name={name}
      render={(arrayHelpers) => {
        const fieldValues = getValue(arrayHelpers.form.values);
        return (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              width: '100%'
            }}
          >
            {fieldValues?.map((_i: any, index: number) => {
              return (
                <Box
                  key={index + 11}
                  // display="flex"
                  // flexDirection="column"
                  sx={{
                    width: '100%'
                  }}
                >
                  {group?.is_iterative_or_not ? (
                    <Box sx={{ mb: 2.5 }}>
                      <Typography
                        variant="h5"
                        sx={{
                          mb: 1.2,
                          textTransform: 'capitalize',
                          color: activeTheme?.theme?.textColor || '#000000'
                        }}
                      >
                        {group?.group_title} #{index + 1}
                      </Typography>
                      {group?.group_description && (
                        <Typography
                          variant="subtitle2"
                          sx={{
                            color: activeTheme?.theme?.textColor || '#000000'
                          }}
                        >
                          {group?.group_description}
                        </Typography>
                      )}
                    </Box>
                  ) : (
                    ''
                  )}
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      width: '100%'
                    }}
                  >
                    {index <= fieldValues.length && fieldValues.length >= 2 && (
                      <RemoveSeparator
                        onPress={() => arrayHelpers.remove(index)}
                      />
                    )}
                    {isNumber(maxLength) ? (
                      fieldValues?.length < maxLength && (
                        <AddSeparator
                          onPress={() => arrayHelpers.push(emptyField)}
                        />
                      )
                    ) : (
                      <AddSeparator
                        onPress={() => arrayHelpers.push(emptyField)}
                      />
                    )}
                  </Box>
                  {children(index)}
                </Box>
              );
            })}
          </Box>
        );
      }}
    />
    // <FieldArray
    //   name={name}
    //   render={(arrayHelpers) => {
    //     const fieldValues = getValue(arrayHelpers.form.values);
    //     return (
    //       <Box display="flex" flexDirection="column">
    //         {fieldValues?.map((_i: any, index: number) => {
    //           return (
    //             <Box key={index + 11} display="flex" flexDirection="row">
    //               {index <= fieldValues.length && fieldValues.length >= 2 && (
    //                 <Box>
    //                   <RemoveSeparator
    //                     onPress={() => arrayHelpers.remove(index)}
    //                   />
    //                 </Box>
    //               )}
    //               {children(index)}
    //             </Box>
    //           );
    //         })}
    //         {isNumber(maxLength) ? (
    //           fieldValues?.length < maxLength && (
    //             <AddSeparator onPress={() => arrayHelpers.push(emptyField)} />
    //           )
    //         ) : (
    //           <AddSeparator onPress={() => arrayHelpers.push(emptyField)} />
    //         )}
    //       </Box>
    //     );
    //   }}
    // />
  );
};

export const RenderGroupsBasedOnIteration: React.FC<{
  group: GROUP;
  arrayIndex: number;
  fieldStyles: any;
  isCreateForm?: boolean;
  activeTheme?: any;
}> = ({ group, arrayIndex, fieldStyles, isCreateForm, activeTheme }) => {
  return group.fields.map((field: FIELD, index: number) => {
    if (field?.is_iterative_or_not) {
      return (
        <FormFieldArray
          name={`${group?.group_key}.${arrayIndex}.${field?.name}`}
          children={(findex) =>
            isCreateForm ? (
              <RenderField
                field={field}
                key={index + 1}
                groupKey={group.group_key}
                style={fieldStyles}
                index={arrayIndex}
                findex={findex}
                isCreateForm={isCreateForm}
              />
            ) : (
              <AppField
                field={field}
                key={index + 1}
                groupKey={group.group_key}
                style={fieldStyles}
                index={arrayIndex}
                findex={findex}
                isCreateForm={isCreateForm}
                activeTheme={activeTheme}
              />
            )
          }
          minLength={field.iteration_min_length || 1}
          maxLength={field.iteration_max_length}
          emptyField={field.input_type === 'checkbox' ? [] : ''}
          from="FIELD"
        />
      );
    }
    return (
      <AppField
        field={field}
        key={index + 1}
        groupKey={group.group_key}
        style={fieldStyles}
        index={arrayIndex}
        isCreateForm={isCreateForm}
        activeTheme={activeTheme}
      />
    );
  });
};

export const RenderFieldsBasedOnIteration: React.FC<{
  field: FIELD;
  fieldStyles: any;
  group_key: string;
  isCreateForm?: boolean;
  secIndex?: number;
  colIndex?: number;
  id?: any;
  key?: any;
  activeTheme?: any;
}> = ({
  id,
  key,
  field,
  fieldStyles,
  group_key,
  isCreateForm,
  secIndex,
  colIndex,
  activeTheme
}) => {
  const { values } = useFormikContext<any>();
  const { formValues } = useSelector((state: RootState) => state.preview);
  const { form }: any = useSelector((state: AppState) => state.form);
  const [isDisplay, setIsDisplay] = useState<boolean>(true);
  const [conditionalInput, setConditionalInput] = useState<any>([]);

  if (field?.is_iterative_or_not && !isCreateForm) {
    return (
      <FormFieldArray
        key={key}
        name={`${group_key}.${field.name}`}
        children={
          (aindex) => (
            <AppField
              field={field}
              groupKey={group_key}
              style={fieldStyles}
              findex={aindex}
              key={`${group_key}_${aindex}`}
              isCreateForm={isCreateForm}
              secIndex={secIndex}
              colIndex={colIndex}
            />
          )
          // )
        }
        minLength={field.iteration_min_length || 1}
        maxLength={field.iteration_max_length}
        emptyField={field.input_type === 'checkbox' ? [] : ''}
        from="FIELD"
      />
    );
  }

  if (isCreateForm) {
    return (
      <RenderField
        id={id}
        field={field}
        groupKey={group_key}
        style={fieldStyles}
        isCreateForm={isCreateForm}
        secIndex={secIndex}
        colIndex={colIndex}
        activeTheme={activeTheme}
      />
    );
  }
  const previewFormResponse: any = formValues;

  const renderingField: any = field;

  let isDisplayTheField: boolean = true;

  // if (
  //   renderingField?.conditions?.length &&
  //   renderingField?.conditions[0]?.field_value !==
  //     previewFormResponse?.[group_key]?.[
  //       renderingField?.conditions[0]?.field_name
  //     ]
  // ) {
  //   isDisplayTheField = false;
  // }

  // const selectedField = form.groups
  //   ?.flatMap((group: any) => group.fields || [])
  //   .find(
  //     (fd: any) =>
  //       fd?.field_id ===
  //       renderingField?.validation_schema?.conditional_validation
  //         ?.selectedInputId
  //   );
  // console.log('Selected Field: ', selectedField);

  // const conditionalValue =
  //   values[
  //     renderingField?.validation_schema?.conditional_validation
  //       ?.selectedGroupKey
  //   ]?.[selectedField?.name] || '';
  // console.log('Conditional Value: ', conditionalValue);
  // // eslint-disable-next-line react-hooks/rules-of-hooks
  // useEffect(() => {
  //   const isConditionalInput =
  //     field?.validation_schema?.conditional_validation?.conditions[0] ===
  //       'isDisplay' ||
  //     field?.validation_schema?.conditional_validation?.conditions[1] ===
  //       'isDisplay';
  //   console.log('Is Conditional Input: ', isConditionalInput);

  //   if (isConditionalInput) {
  //     setConditionalInput([...conditionalInput, field]);
  //     setIsDisplay(false);
  //   }
  //   if (selectedField) {
  //     const value = selectedField?.dependencies?.map((fd: any) => {
  //       const val = form.groups?.map((group: any) =>
  //         group?.fields?.find(
  //           (fdd: any) => fdd?.field_id === fd?.selectedInputId
  //         )
  //       );
  //       return val?.find(Boolean);
  //     });
  //   }
  // }, [selectedField]);

  // // eslint-disable-next-line react-hooks/rules-of-hooks
  // useEffect(() => {
  //   if (conditionalValue) {
  //     const fid = form.groups
  //       ?.flatMap((group: any) => group.fields || [])
  //       .find(
  //         (fd: any) =>
  //           fd?.field_id === selectedField?.dependencies[0]?.selectedInputId
  //       );

  //     console.log('fid: ', fid);
  //     if (selectedField?.input_type === 'checkbox') {
  //       if (
  //         conditionalValue[0] ===
  //         fid?.validation_schema?.conditional_validation?.selectedOption[0]
  //       ) {
  //         setIsDisplay(true);
  //       } else {
  //         setIsDisplay(false);
  //       }
  //     } else if (
  //       selectedField?.input_type === 'radio' ||
  //       selectedField?.input_type === 'toggle' ||
  //       selectedField?.input_type === 'select'
  //     ) {
  //       if (
  //         conditionalValue ===
  //         fid?.validation_schema?.conditional_validation?.selectedOption
  //       ) {
  //         setIsDisplay(true);
  //       } else {
  //         setIsDisplay(false);
  //       }
  //     } else if (
  //       selectedField?.input_type === 'date' ||
  //       selectedField?.input_type === 'time' ||
  //       selectedField?.input_type === 'datetime-local'
  //     ) {
  //       if (
  //         fid?.validation_schema?.conditional_validation?.dateComparison ===
  //           'lessthan' ||
  //         fid?.validation_schema?.conditional_validation?.timeComparison ===
  //           'lessthan' ||
  //         fid?.validation_schema?.conditional_validation
  //           ?.datetimeComparision === 'lessthan'
  //       ) {
  //         if (
  //           conditionalValue <
  //           fid?.validation_schema?.conditional_validation?.selectedDate
  //         ) {
  //           setIsDisplay(true);
  //         } else {
  //           setIsDisplay(false);
  //         }
  //       }
  //       if (
  //         fid?.validation_schema?.conditional_validation?.dateComparison ===
  //           'greaterthan' ||
  //         fid?.validation_schema?.conditional_validation?.timeComparison ===
  //           'greaterthan' ||
  //         fid?.validation_schema?.conditional_validation
  //           ?.datetimeComparision === 'greaterthan'
  //       ) {
  //         if (
  //           conditionalValue >
  //           fid?.validation_schema?.conditional_validation?.selectedDate
  //         ) {
  //           setIsDisplay(true);
  //         } else {
  //           setIsDisplay(false);
  //         }
  //       }
  //       if (
  //         fid?.validation_schema?.conditional_validation?.dateComparison ===
  //           'equalto' ||
  //         fid?.validation_schema?.conditional_validation?.timeComparison ===
  //           'equalto' ||
  //         fid?.validation_schema?.conditional_validation
  //           ?.datetimeComparision === 'equalto'
  //       ) {
  //         if (
  //           conditionalValue ===
  //           fid?.validation_schema?.conditional_validation?.selectedDate
  //         ) {
  //           setIsDisplay(true);
  //         } else {
  //           setIsDisplay(false);
  //         }
  //       }
  //       if (
  //         fid?.validation_schema?.conditional_validation?.dateComparison ===
  //           'lessthanorequalto' ||
  //         fid?.validation_schema?.conditional_validation?.timeComparison ===
  //           'lessthanorequalto' ||
  //         fid?.validation_schema?.conditional_validation
  //           ?.datetimeComparision === 'lessthanorequalto'
  //       ) {
  //         if (
  //           conditionalValue <=
  //           fid?.validation_schema?.conditional_validation?.selectedDate
  //         ) {
  //           setIsDisplay(true);
  //         } else {
  //           setIsDisplay(false);
  //         }
  //       }
  //       if (
  //         fid?.validation_schema?.conditional_validation?.dateComparison ===
  //           'greaterthanorequalto' ||
  //         fid?.validation_schema?.conditional_validation?.timeComparison ===
  //           'greaterthanorequalto' ||
  //         fid?.validation_schema?.conditional_validation
  //           ?.datetimeComparision === 'greaterthanorequalto'
  //       ) {
  //         if (
  //           conditionalValue >=
  //           fid?.validation_schema?.conditional_validation?.selectedDate
  //         ) {
  //           setIsDisplay(true);
  //         } else {
  //           setIsDisplay(false);
  //         }
  //       }
  //       if (
  //         fid?.validation_schema?.conditional_validation?.dateComparison ===
  //           'inbetween' ||
  //         fid?.validation_schema?.conditional_validation?.timeComparison ===
  //           'inbetween' ||
  //         fid?.validation_schema?.conditional_validation
  //           ?.datetimeComparision === 'inbetween'
  //       ) {
  //         if (
  //           conditionalValue <
  //           fid?.validation_schema?.conditional_validation?.selectedDate
  //         ) {
  //           setIsDisplay(true);
  //         } else {
  //           setIsDisplay(false);
  //         }
  //       }
  //     } else if (
  //       conditionalValue ===
  //       fid?.validation_schema?.conditional_validation?.selectedOption
  //     ) {
  //       setIsDisplay(true);
  //     } else {
  //       setIsDisplay(false);
  //     }
  //   }
  // }, [conditionalValue]);

  // if (dependencyField?.field_id) {
  //   console.log('dependency Field Check: ', dependencyField);
  // }

  const checkConditionMatch = (
    fieldValue: any,
    condition: any,
    type: string
  ) => {
    if (!condition || fieldValue === undefined || fieldValue === null)
      return false;

    switch (type) {
      case 'checkbox':
        return fieldValue.includes(condition.selectedOption?.[0]);

      case 'radio':
      case 'select':
      case 'toggle':
        return fieldValue === condition.selectedOption;

      case 'date':
      case 'time':
      case 'datetime-local': {
        const selectedDate = new Date(condition.selectedDate);
        const fieldDate = new Date(fieldValue);
        switch (
          condition.dateComparison ||
          condition.timeComparison ||
          condition.datetimeComparision
        ) {
          case 'lessthan':
            return fieldDate < selectedDate;
          case 'greaterthan':
            return fieldDate > selectedDate;
          case 'equalto':
            return fieldDate.getTime() === selectedDate.getTime();
          case 'lessthanorequalto':
            return fieldDate <= selectedDate;
          case 'greaterthanorequalto':
            return fieldDate >= selectedDate;
          case 'inbetween': {
            const from = new Date(condition.fromDate);
            const to = new Date(condition.toDate);
            return fieldDate >= from && fieldDate <= to;
          }
          default:
            return false;
        }
      }

      default:
        return fieldValue === condition.selectedOption;
    }
  };

  const conditionalValidation =
    field?.validation_schema?.conditional_validation;
  const selectedField = useMemo(() => {
    return form.groups
      ?.flatMap((group: any) => group.fields || [])
      .find(
        (fd: any) => fd?.field_id === conditionalValidation?.selectedInputId
      );
  }, [form, field]);

  const dependentValue =
    values?.[conditionalValidation?.selectedGroupKey]?.[selectedField?.name];

  useEffect(() => {
    if (!selectedField || !conditionalValidation) return;

    const shouldShow = checkConditionMatch(
      dependentValue,
      conditionalValidation,
      selectedField?.input_type === null
        ? selectedField?.type
        : selectedField?.input_type
    );

    setIsDisplay(shouldShow);
  }, [dependentValue, selectedField, conditionalValidation]);

  return isDisplay ? (
    // return isDisplayTheField ? (
    <AppField
      id={id}
      field={field}
      groupKey={group_key}
      style={fieldStyles}
      isCreateForm={isCreateForm}
      secIndex={secIndex}
      colIndex={colIndex}
      activeTheme={activeTheme}
    />
  ) : (
    ''
  );
};

export const Group: React.FC<{
  group: GROUP;
  groupsCount: number;
  type: string;
  activeTheme: any;
}> = ({ group, type, activeTheme }) => {
  const getValue = (values: any) => {
    return getIn(values, group.group_key);
  };
  const renderFields = (arrayIndex: number) => {
    return (
      <RenderGroupsBasedOnIteration
        arrayIndex={arrayIndex}
        fieldStyles={{}}
        group={group}
        key={`${group.group_key}_${arrayIndex}`}
        activeTheme={activeTheme}
      />
    );
  };
  return type === 'accordion' ? (
    <>
      {!group?.is_iterative_or_not ? (
        <Accordion
          sx={{
            marginBottom: 1,
            border: '1px solid black',
            '&:last-child': {
              marginBottom: 0
            }
          }}
        >
          <AccordionSummary
            expandIcon={<Icon name="ExpandMore" sx={{ marginRight: '20px' }} />}
            aria-controls="panel1a-content"
            id="panel1a-header"
            sx={{
              minHeight: 80
            }}
          >
            <Box
              sx={{
                mb: 0
              }}
            >
              <Typography
                variant="h5"
                sx={{
                  mb: 1.2,
                  textTransform: 'capitalize',
                  color: activeTheme?.theme?.textColor || '#000000'
                }}
              >
                {group?.group_title}
              </Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            {group?.group_description && (
              <Typography
                variant="subtitle2"
                sx={{
                  color: activeTheme?.theme?.textColor || '#000000'
                }}
              >
                {group?.group_description}
              </Typography>
            )}
            <Box
              sx={[
                {
                  marginVertical: 20,
                  display: 'flex',
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  justifyContent: 'space-between'
                }
              ]}
            >
              {group.is_iterative_or_not ? (
                <FormFieldArray
                  group={group}
                  name={group.group_key}
                  children={(index) => renderFields(index)}
                  minLength={group.iteration_min_length || 1}
                  maxLength={group.iteration_max_length}
                  emptyField={generateEmptyObjectForArraySections(group)}
                  activeTheme={activeTheme}
                />
              ) : (
                group.fields.map((field: any, index: number) => {
                  return (
                    <RenderFieldsBasedOnIteration
                      field={field}
                      group_key={group.group_key}
                      fieldStyles={{}}
                      key={`${group.group_key}_${index}`}
                      activeTheme={activeTheme}
                    />
                  );
                })
              )}
            </Box>
          </AccordionDetails>
        </Accordion>
      ) : (
        <FieldArray
          name={group.group_key}
          render={(arrayHelpers) => {
            const fieldValues = getValue(arrayHelpers.form.values);
            return (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  width: '100%'
                }}
              >
                {fieldValues?.map((_i: any, index: number) => {
                  return (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        width: '100%'
                      }}
                    >
                      <Accordion
                        sx={{
                          marginBottom: 1,
                          border: '1px solid black',
                          '&:last-child': {
                            marginBottom: 0
                          },
                          width: '100%'
                        }}
                      >
                        <AccordionSummary
                          expandIcon={
                            <Icon
                              name="ExpandMore"
                              sx={{ marginRight: '20px' }}
                            />
                          }
                          aria-controls="panel1a-content"
                          id="panel1a-header"
                          sx={{
                            minHeight: 80
                          }}
                        >
                          <Box
                            sx={{
                              mb: 0
                            }}
                          >
                            <Typography
                              variant="h5"
                              sx={{
                                mb: 1.2,
                                textTransform: 'capitalize',
                                color:
                                  activeTheme?.theme?.textColor || '#000000'
                              }}
                            >
                              {group?.group_title} #{index + 1}
                            </Typography>
                          </Box>
                        </AccordionSummary>
                        <AccordionDetails>
                          {group?.group_description && (
                            <Typography
                              variant="subtitle2"
                              sx={{
                                color:
                                  activeTheme?.theme?.textColor || '#000000'
                              }}
                            >
                              {group?.group_description}
                            </Typography>
                          )}
                          <Box
                            key={index + 11}
                            // display="flex"
                            // flexDirection="column"
                            sx={{
                              width: '100%'
                            }}
                          >
                            {renderFields(index)}
                          </Box>
                        </AccordionDetails>
                      </Accordion>
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'flex-end'
                        }}
                      >
                        {index <= fieldValues.length &&
                          fieldValues.length >= 2 && (
                            <RemoveSeparator
                              onPress={() => arrayHelpers.remove(index)}
                            />
                          )}
                        {isNumber(group.iteration_max_length) ? (
                          fieldValues?.length < group.iteration_max_length && (
                            <AddSeparator
                              onPress={() =>
                                arrayHelpers.push(
                                  generateEmptyObjectForArraySections(group)
                                )
                              }
                            />
                          )
                        ) : (
                          <AddSeparator
                            onPress={() =>
                              arrayHelpers.push(
                                generateEmptyObjectForArraySections(group)
                              )
                            }
                          />
                        )}
                      </Box>
                    </Box>
                  );
                })}
              </Box>
            );
          }}
        />
      )}
    </>
  ) : (
    type === 'plain' && (
      <>
        {!group?.is_iterative_or_not ? (
          <Box sx={{ mb: 2.5 }}>
            <Typography
              variant="h5"
              sx={{
                mb: 1.2,
                textTransform: 'capitalize',
                color: activeTheme?.theme?.textColor || '#000000'
              }}
            >
              {group?.group_title}
            </Typography>
            {group?.group_description && (
              <Typography
                variant="subtitle2"
                sx={{
                  color: activeTheme?.theme?.textColor || '#000000'
                }}
              >
                {group?.group_description}
              </Typography>
            )}
          </Box>
        ) : (
          ''
        )}
        <Box
          sx={[
            {
              marginVertical: 20,
              display: 'flex',
              // flexDirection: "column",
              flexWrap: 'wrap',
              justifyContent: 'space-between',
              width: '100%'
            }
          ]}
        >
          {group?.is_iterative_or_not ? (
            <FormFieldArray
              group={group}
              name={group.group_key}
              children={(index) => renderFields(index)}
              minLength={group.iteration_min_length || 1}
              maxLength={group.iteration_max_length}
              emptyField={generateEmptyObjectForArraySections(group)}
              activeTheme={activeTheme}
            />
          ) : (
            group?.fields.map((field: any, index: number) => {
              return (
                <RenderFieldsBasedOnIteration
                  field={field}
                  group_key={group.group_key}
                  fieldStyles={{}}
                  key={`${group.group_key}_${index + 1}`}
                  activeTheme={activeTheme}
                />
              );
            })
          )}
        </Box>
      </>
    )
  );
};

export const FormSectionsAsPlain: React.FC<{
  form: FORM;
  type: string;
  activeTheme: any;
  submitForm: any;
  layout?: any;
}> = ({ form, type, activeTheme }) => {
  // const steps = [
  //   "Select campaign settings",
  //   "Create an ad group",
  //   "Create an ad",
  // ];
  const [steps, setSteps] = useState<any>(['']);
  const [activeStep, setActiveStep] = React.useState(0);
  const [skipped, setSkipped] = React.useState(new Set<number>());

  useEffect(() => {
    const arrayData = form.groups.map((group: any) => group?.group_title);
    setSteps(arrayData);
  }, []);
  const isStepOptional = (step: number) => {
    return step === 1;
  };

  const isStepSkipped = (step: number) => {
    return skipped.has(step);
  };

  const handleNext = () => {
    let newSkipped = skipped;
    if (isStepSkipped(activeStep)) {
      newSkipped = new Set(newSkipped.values());
      newSkipped.delete(activeStep);
    }

    setActiveStep((prevActiveStep) => prevActiveStep + 1);
    setSkipped(newSkipped);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleReset = () => {
    setActiveStep(0);
  };

  const { resetForm } = useFormikContext<any>();

  const renderFields = (arrayIndex: number) => {
    return (
      <RenderGroupsBasedOnIteration
        arrayIndex={arrayIndex}
        fieldStyles={{}}
        group={form.groups[activeStep]}
        key={`${form.groups[activeStep].group_key}_${arrayIndex}`}
      />
    );
  };

  return type === 'stepper' ? (
    <Box sx={{ width: '100%' }}>
      <Stepper activeStep={activeStep}>
        {form?.groups.map((_label, index) => {
          const stepProps: { completed?: boolean } = {};
          const labelProps: {
            optional?: React.ReactNode;
          } = {};
          if (isStepOptional(index)) {
            labelProps.optional = <Typography variant="caption" />;
          }
          if (isStepSkipped(index)) {
            stepProps.completed = false;
          }
          return <StepLabel key={`${index + 1}`} {...labelProps} />;
        })}
      </Stepper>
      {activeStep === steps.length ? (
        <>
          <Typography sx={{ mt: 2, mb: 1 }}>
            All steps completed - you&apos;re finished
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
            <Box sx={{ flex: '1 1 auto' }} />
            <Button
              onClick={handleReset}
              sx={{
                backgroundColor: '#0483BA',
                height: '50px',
                width: '500px',
                color: '#ffffff',
                '&:hover': {
                  backgroundColor: '#046F99'
                }
              }}
            >
              Reset
            </Button>
          </Box>
        </>
      ) : (
        <>
          <Box>
            {!form.groups[activeStep].is_iterative_or_not ? (
              <Box sx={{ mb: 2.5 }}>
                <Typography
                  variant="h5"
                  sx={{
                    mb: 1.2,
                    textTransform: 'capitalize',
                    color: activeTheme?.theme?.textColor || '#000000'
                  }}
                >
                  {form.groups[activeStep].group_title}
                </Typography>
                {form.groups[activeStep].group_description && (
                  <Typography
                    variant="subtitle2"
                    sx={{
                      color: activeTheme?.theme?.textColor || '#000000'
                    }}
                  >
                    {form.groups[activeStep].group_description}
                  </Typography>
                )}
              </Box>
            ) : (
              ''
            )}

            <Box
              sx={[
                {
                  marginVertical: 20,
                  display: 'flex',
                  flexWrap: 'wrap',
                  flexDirection: 'row',
                  width: '100%',
                  justifyContent: 'space-between'
                }
              ]}
            >
              {form.groups[activeStep].is_iterative_or_not ? (
                <FormFieldArray
                  group={form.groups[activeStep]}
                  name={form.groups[activeStep].group_key}
                  children={(index) => renderFields(index)}
                  minLength={form.groups[activeStep].iteration_min_length || 1}
                  maxLength={form.groups[activeStep].iteration_max_length}
                  emptyField={generateEmptyObjectForArraySections(
                    form.groups[activeStep]
                  )}
                  activeTheme={activeTheme}
                />
              ) : (
                form.groups[activeStep].fields.map(
                  (field: any, index: number) => {
                    return (
                      <RenderFieldsBasedOnIteration
                        field={field}
                        group_key={form.groups[activeStep].group_key}
                        fieldStyles={{}}
                        key={`${form.groups[activeStep].group_key}_${index}`}
                        activeTheme={activeTheme}
                      />
                    );
                  }
                )
              )}
            </Box>
          </Box>
          <Grid container spacing={2} sx={{ marginTop: '5px' }}>
            <Grid item xs={6}>
              <Button
                color="inherit"
                disabled={activeStep === 0}
                onClick={handleBack}
                sx={{
                  mr: 1,
                  backgroundColor: activeTheme?.theme?.buttonColor || '#0483BA',
                  color: activeTheme?.theme?.buttonTextColor || '#ffffff',
                  borderRadius:
                    activeTheme?.theme?.buttonStyle === 'rounded'
                      ? '30px'
                      : '0px',
                  width: '100%',
                  height: '50px',
                  marginLeft: '5px',
                  '&:hover': {
                    backgroundColor: activeStep === 0 ? '#B0BEC5' : '#046F99' // Slightly darker hover color
                  }
                }}
              >
                Back
              </Button>
            </Grid>
            <Grid item xs={6}>
              <Button
                onClick={handleNext}
                sx={{
                  backgroundColor: activeTheme?.theme?.buttonColor || '#0483BA',
                  color: activeTheme?.theme?.buttonTextColor || '#ffffff',
                  borderRadius:
                    activeTheme?.theme?.buttonStyle === 'rounded'
                      ? '30px'
                      : '0px',
                  width: '100%',
                  height: '50px',
                  '&:hover': {
                    backgroundColor: '#046F99' // Slightly darker hover color
                  }
                }}
              >
                {activeStep === steps.length - 1 ? 'Submit' : 'Next'}
              </Button>
            </Grid>
          </Grid>

          <Box sx={{ display: 'flex', justifyContent: 'center', pt: 2 }} />
        </>
      )}
    </Box>
  ) : (
    <Box>
      {form &&
        form?.groups
          ?.slice()
          ?.sort(
            (prev: GROUP, next: GROUP) =>
              parseInt(prev.group_index, 10) - parseInt(next.group_index, 10)
          )
          ?.map((group: any, index: number) => (
            <Group
              group={group}
              groupsCount={form?.groups?.length}
              key={`${index + 1}`}
              type={type}
              activeTheme={activeTheme}
            />
          ))}
      <Grid container spacing={2} sx={{ marginTop: '5px' }}>
        <Grid item xs={6}>
          <Button
            variant="contained"
            fullWidth
            sx={{
              height: '50px',
              width: '100%',
              color: activeTheme?.theme?.buttonTextColor,
              backgroundColor: activeTheme?.theme?.buttonColor,
              borderRadius:
                activeTheme?.theme?.buttonStyle === 'rounded' ? '30px' : '0px',
              '&:hover': {
                backgroundColor: activeTheme?.theme?.hoverColor || '#046F99' // Custom hover color or fallback
              }
            }}
            onClick={() => resetForm()}
          >
            Clear
          </Button>
        </Grid>
        <Grid item xs={6}>
          <SubmitButton
            title="Submit"
            sx={{
              height: '50px',
              width: '100%',
              color: activeTheme?.theme?.buttonTextColor,
              backgroundColor: activeTheme?.theme?.buttonColor,
              borderRadius:
                activeTheme?.theme?.buttonStyle == 'rounded' ? '30px' : '0px',
              '&:hover': {
                backgroundColor: activeTheme?.theme?.hoverColor || '#046F99' // Custom hover color or fallback
              }
            }}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export const AddGroup: React.FC<{
  group: GROUP;
  groupsCount: number;
  groupTitle: string;
  groupDescription: string;
  handleInputChange: any;
  secIndex: number;
}> = ({ group, groupTitle, groupDescription, handleInputChange, secIndex }) => {
  const { sectionIndex } = useSelector((state: RootState) => state.form);
  const [fieldsList, setFieldsList] = useState<any>([]);

  const renderFields = (arrayIndex: number) => {
    return (
      <RenderGroupsBasedOnIteration
        arrayIndex={arrayIndex}
        fieldStyles={{}}
        group={group}
        key={`${group.group_key}_${arrayIndex}`}
        isCreateForm
      />
    );
  };

  useEffect(() => {
    const items = group.fields.map((field: any) => {
      return {
        id: field.field_id,
        field
      };
    });
    setFieldsList(items);
  }, [group]);

  const getItemPosition = (id: any) =>
    fieldsList.findIndex((field: any) => field.id === id);

  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (active.id === over.id) return;

    setFieldsList((fieldsList: any) => {
      const originalPosition = getItemPosition(active.id);
      const newPosition = getItemPosition(over.id);

      return arrayMove(fieldsList, originalPosition, newPosition);
    });
  };

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(TouchSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  );

  return (
    <DndContext
      sensors={sensors}
      onDragEnd={handleDragEnd}
      collisionDetection={closestCorners}
    >
      <Box>
        <Box
          className="section-container"
          sx={{
            width: '100%'
          }}
        >
          <Box
            id="form-section-card"
            className={`form-section-card ${
              secIndex == sectionIndex && 'activeSection'
            }`}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-end',
                position: 'absolute',
                right: '20px',
                top: '20px'
              }}
            >
              <IconButton>
                <Icon
                  name="MoreVert"
                  sx={{
                    cursor: 'pointer'
                  }}
                />
              </IconButton>
            </Box>
            <FormInput
              name={groupTitle}
              className="section-title-field"
              label=""
              autoComplete="off"
              placeholder="Section Title"
              isCreateForm
              handleInputChange={handleInputChange}
              containerStyles={{
                marginBottom: '0px'
              }}
            />
            <FormInput
              name={groupDescription}
              label=""
              placeholder="Description"
              autoComplete="off"
              className="section-description"
              isCreateForm
              handleInputChange={handleInputChange}
              containerStyles={{
                marginBottom: '0px'
              }}
            />
          </Box>
        </Box>

        <Box sx={[{ marginVertical: 20 }]}>
          <SortableContext
            strategy={verticalListSortingStrategy}
            items={fieldsList}
          >
            {group.is_iterative_or_not ? (
              <FormFieldArray
                name={group.group_key}
                children={(index) => renderFields(index)}
                minLength={group.iteration_min_length || 1}
                maxLength={group.iteration_max_length}
                emptyField={generateEmptyObjectForArraySections(group)}
              />
            ) : (
              fieldsList.map((item: any, index: number) => {
                return (
                  <RenderFieldsBasedOnIteration
                    id={item.id}
                    field={item.field}
                    key={item.id}
                    group_key={group.group_key}
                    fieldStyles={{}}
                    isCreateForm
                    colIndex={index}
                    secIndex={secIndex}
                  />
                );
              })
            )}
          </SortableContext>
        </Box>
      </Box>
    </DndContext>
  );
};
