/* eslint-disable no-nested-ternary */
import * as React from 'react';
import Box from '@mui/material/Box';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon from '@mui/material/ListItemIcon';
import Divider from '@mui/material/Divider';
import CropPortraitIcon from '@mui/icons-material/CropPortrait';
import CropLandscapeIcon from '@mui/icons-material/CropLandscape';
import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';
import LibraryBooksOutlinedIcon from '@mui/icons-material/LibraryBooksOutlined';
import UploadIcon from '@mui/icons-material/Upload';
// import StartIcon from '@mui/icons-material/Start';
// import VerticalAlignCenterIcon from '@mui/icons-material/VerticalAlignCenter';
import AspectRatioIcon from '@mui/icons-material/AspectRatio';
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined';
import InsertPageBreakOutlinedIcon from '@mui/icons-material/InsertPageBreakOutlined';
import MarginOutlinedIcon from '@mui/icons-material/MarginOutlined';
import UploadFileOutlinedIcon from '@mui/icons-material/UploadFileOutlined';
import HeightOutlinedIcon from '@mui/icons-material/HeightOutlined';
import FontDownloadOutlinedIcon from '@mui/icons-material/FontDownloadOutlined';
import TextIncreaseIcon from '@mui/icons-material/TextIncrease';
// import VerticalAlignCenterOutlinedIcon from '@mui/icons-material/VerticalAlignCenterOutlined';
import BrandingWatermarkOutlinedIcon from '@mui/icons-material/BrandingWatermarkOutlined';
import TextFieldsOutlinedIcon from '@mui/icons-material/TextFieldsOutlined';
import {
  Button,
  Card,
  CardHeader,
  IconButton,
  InputAdornment,
  ListItem,
  Menu,
  TextField,
  Tooltip,
  Typography
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import {
  FormatAlignCenter,
  //   FormatAlignJustify,
  FormatAlignLeft,
  FormatAlignRight
  // LastPage
} from '@mui/icons-material';
import { useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ColorPickerButton from './ColorPicker';
import PdfFormResponse from './PdfFormResponseComponent';
import logo from '../../assets/klezalogo.png';
import { getorganizationdetails } from '../../redux/reducers/org.reducer';
import { ORGANIZATIONAPPLISTDETAILS } from '../../types';
import { AppDispatch, AppState } from '../../redux/app.store';
import { RootState } from '../../redux/reducers';
import { getpdfconfig } from '../../redux/reducers/addressconfig.reducer';

export default function ToolbarComponent() {
  //   const [value, setValue] = React.useState(0);
  const [selectedIndex, setSelectedIndex] = React.useState<number>(0);
  const [sizeValue, setSizeValue] = React.useState<string>('A4');
  const [breakValue, setBreakValue] = React.useState<string>('PageWrap');
  const [orientationValue, setOrientationValue] = React.useState('Portrait');
  const [marginValue, setMarginValue] = React.useState<string>('Normal');
  //   const [pageNumberAlignment, setPageNumberAlignment] = React.useState('Left');
  // const [logoAlignment, setLogoAlignment] = React.useState('center');
  const [insertLogo, setInsertLogo] = useState<string | undefined>(undefined);
  const [tempInsertLogo, setTempInsertLogo] = useState<string | undefined>(
    undefined
  );
  const [currentColor, setCurrentColor] = React.useState(''); // Default to white
  // const [colorScheme, setColorScheme] = React.useState('White'); // Example if using predefined schemes
  const [fontFamily, setFontFamily] = React.useState('Helvetica');
  const [fontSize, setFontSize] = React.useState('14');
  const [watermarkText, setWatermarkText] = React.useState<string>('');
  const [tempWatermarkText, setTempWatermarkText] = useState<string>(''); // temporary input
  const [textColor, setTextColor] = React.useState('#000000');
  //   const [alignText, setAlignText] = React.useState('center');
  //   const [watermarkImage, setWatermarkImage] = React.useState(null);
  const [headerHeight, setHeaderHeight] = React.useState('');
  const [footerHeight, setFooterHeight] = React.useState('');
  const [tempHeaderHeight, setTempHeaderHeight] = useState('');
  const [tempFooterHeight, setTempFooterHeight] = useState('');
  const [footerText, setFooterText] = React.useState('');
  const [tempFooterText, setTempFooterText] = useState<string>('');
  const [alignPageNumberText, setAlignPageNumberText] =
    React.useState('center');
  const [openOrientation, setOpenOrientation] =
    React.useState<null | HTMLElement>(null);
  const isOpenOrientation = Boolean(openOrientation);
  const handleClickOrientation = (event: React.MouseEvent<HTMLElement>) => {
    setOpenOrientation(event.currentTarget);
  };
  const { user } = useSelector((state: RootState) => state.user);
  const [orgData, setOrgData] = useState<ORGANIZATIONAPPLISTDETAILS>();
  const [logoError, setLogoError] = useState('');
  const dispatch = useDispatch<AppDispatch>();
  // const [styles, setStyles] = useState();
  const location = useLocation();
  const { pdfConfig }: any = useSelector(
    (state: AppState) => state.addressconfig
  );

  useEffect(() => {
    if (pdfConfig?.details) {
      const parsedStyles = pdfConfig.details;
      // setStyles(parsedStyles);
      setOrientationValue(parsedStyles?.orientation || 'Portrait');
      setSizeValue(parsedStyles?.size || 'A4');
      setBreakValue(parsedStyles?.pageBreaks || 'PageWrap');
      setMarginValue(parsedStyles?.margin || 'Normal');
      setCurrentColor(parsedStyles?.backgroundColor || '#ffffff');
      setTextColor(parsedStyles?.textColor || '#000000');
      setFontFamily(parsedStyles?.fontFamily || 'Helvetica');
      setFontSize(parsedStyles?.fontSize || '14');
      setHeaderHeight(parsedStyles?.headerHeight || '');
      setWatermarkText(parsedStyles?.watermarkText || '');
      setFooterText(parsedStyles?.footerText || '');
      setInsertLogo(parsedStyles?.uploadLogo || '');
    }
  }, [pdfConfig?.details]);
  // const alignText = 'center';
  useMemo(() => {
    try {
      (async () => {
        const orgaData = await dispatch(
          getorganizationdetails(user.organization)
        );
        if (orgaData.payload.status) {
          setOrgData(orgaData?.payload?.data);
        } else {
          toast.error(
            orgaData?.payload?.message ||
              'Something Went Wrong, Please Try Again Later.'
          );
        }
      })();
    } catch (error) {
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
    if (location.pathname?.includes('/view-pdf')) {
      try {
        (async () => {
          const orgaData = await dispatch(getpdfconfig(null));
          if (orgaData.payload.status) {
            // setStyles(orgaData?.payload?.data?.details);
            setOrientationValue(orgaData?.payload?.data?.details?.orientation);
            setSizeValue(orgaData?.payload?.data?.details?.size);
            setBreakValue(orgaData?.payload?.data?.details?.pageBreaks);
            setMarginValue(orgaData?.payload?.data?.details?.margin);
            setCurrentColor(orgaData?.payload?.data?.details?.backgroundColor);
            setTextColor(orgaData?.payload?.data?.details?.textColor);
            setFontFamily(orgaData?.payload?.data?.details?.fontFamily);
            setFontSize(orgaData?.payload?.data?.details?.fontSize);
            setInsertLogo(orgaData?.payload?.data?.details?.uploadLogo);
            setHeaderHeight(orgaData?.payload?.data?.details?.headerHeight);
            // setFooterHeight(orgaData?.payload?.data?.details?.footerHeight);
            setWatermarkText(orgaData?.payload?.data?.details?.watermarkText);
            setFooterText(orgaData?.payload?.data?.details?.footerText);
          } else {
            toast.error(
              orgaData?.payload?.message ||
                'Something Went Wrong, Please Try Again Later.'
            );
          }
        })();
      } catch (error) {
        toast.error('Something Went Wrong, Please Try Again Later.');
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  const watermarkImage = null;

  const handleCloseOrientation = () => {
    setOpenOrientation(null);
  };

  const [openPageSize, setOpenPageSize] = React.useState<null | HTMLElement>(
    null
  );
  const isOpenPageSize = Boolean(openPageSize);
  const handleClickPageSize = (event: React.MouseEvent<HTMLElement>) => {
    setOpenPageSize(event.currentTarget);
  };

  const handleClosePageSize = () => {
    setOpenPageSize(null);
  };

  const [openPageBreaks, setOpenPageBreaks] =
    React.useState<null | HTMLElement>(null);
  const isOpenPageBreaks = Boolean(openPageBreaks);
  const handleClickPageBreaks = (event: React.MouseEvent<HTMLElement>) => {
    setOpenPageBreaks(event.currentTarget);
  };

  const handleClosePageBreaks = () => {
    setOpenPageBreaks(null);
  };

  const [openPageAlignment, setOpenPageAlignment] =
    React.useState<null | HTMLElement>(null);
  const isOpenPageAlignment = Boolean(openPageAlignment);
  // const handleClickPageAlignment = (event: React.MouseEvent<HTMLElement>) => {
  //   setOpenPageAlignment(event.currentTarget);
  // };

  const handleClosePageAlignment = () => {
    setOpenPageAlignment(null);
  };

  const [openMarginValue, setOpenMarginValue] =
    React.useState<null | HTMLElement>(null);
  const isOpenMarginValue = Boolean(openMarginValue);
  const handleClickMargin = (event: React.MouseEvent<HTMLElement>) => {
    setOpenMarginValue(event.currentTarget);
  };

  const handleCloseMarginValue = () => {
    setOpenMarginValue(null);
  };

  const [openUploadLogo, setOpenUploadLogo] =
    React.useState<null | HTMLElement>(null);
  const isOpenUploadLogo = Boolean(openUploadLogo);

  function handleClickUploadLogo(
    event: React.MouseEvent<HTMLButtonElement>
  ): void {
    setOpenUploadLogo(event.currentTarget);
  }

  const handleCloseUploadLogo = () => {
    setInsertLogo(tempInsertLogo);
    setOpenUploadLogo(null);
  };

  const [openHeaderFooterHeight, setOpenHeaderFooterHeight] =
    React.useState<null | HTMLElement>(null);
  const isOpenHeaderFooterHeight = Boolean(openHeaderFooterHeight);
  const handleClickHeaderFooter = (event: React.MouseEvent<HTMLElement>) => {
    setOpenHeaderFooterHeight(event.currentTarget);
  };

  const handleCloseHeaderFooter = () => {
    setOpenHeaderFooterHeight(null);
  };

  const [openFontFamily, setOpenFontFamily] =
    React.useState<null | HTMLElement>(null);
  const isOpenFontFamily = Boolean(openFontFamily);
  const handleClickFontFamily = (event: React.MouseEvent<HTMLElement>) => {
    setOpenFontFamily(event.currentTarget);
  };

  const handleCloseFontFamily = () => {
    setOpenFontFamily(null);
  };

  const [openFontSize, setOpenFontSize] = React.useState<null | HTMLElement>(
    null
  );
  const isOpenFontSize = Boolean(openFontSize);
  const handleClickFontSize = (event: React.MouseEvent<HTMLElement>) => {
    setOpenFontSize(event.currentTarget);
  };

  const handleCloseFontSize = () => {
    setOpenFontSize(null);
  };

  // const [openLogoAlignment, setOpenLogoAlignment] =
  //   React.useState<null | HTMLElement>(null);
  // const isOpenLogoAlignment = Boolean(openLogoAlignment);
  // const handleClickLogoAlignment = (event: React.MouseEvent<HTMLElement>) => {
  //   setOpenLogoAlignment(event.currentTarget);
  // };

  // const handleCloseLogoAlignment = () => {
  //   setOpenLogoAlignment(null);
  // };

  const [openWatermark, setOpenWatermark] = React.useState<null | HTMLElement>(
    null
  );
  const isOpenWatermark = Boolean(openWatermark);
  const handleClickWatermark = (event: React.MouseEvent<HTMLElement>) => {
    setOpenWatermark(event.currentTarget);
  };

  const handleCloseWatermark = () => {
    setOpenWatermark(null);
    setWatermarkText(tempWatermarkText);
  };

  const [openFooterText, setOpenFooterText] =
    React.useState<null | HTMLElement>(null);

  const isOpenFooterText = Boolean(openFooterText);
  const handleClickFooterText = (event: React.MouseEvent<HTMLElement>) => {
    setOpenFooterText(event.currentTarget);
  };

  const handleCloseFooterText = () => {
    setOpenFooterText(null);
    setFooterText(tempFooterText);
  };

  useEffect(() => {
    if (isOpenWatermark) {
      setTempWatermarkText(watermarkText);
    }
    if (isOpenFooterText) {
      setTempFooterText(footerText);
    }
    if (isOpenHeaderFooterHeight) {
      setTempHeaderHeight(headerHeight?.toString() ?? '');
      setTempFooterHeight(footerHeight?.toString() ?? '');
    }
  }, [isOpenWatermark, isOpenFooterText, isOpenHeaderFooterHeight]);

  //   const handleChange = (event: React.SyntheticEvent, newValue: number) => {
  //     setValue(newValue);
  //   };

  const handleListItemClick = (index: number, listItemValue: string) => {
    setSelectedIndex(index);
    setOrientationValue(listItemValue);
  };

  const handleSizeChange = (size: string) => {
    setSizeValue(size);
  };

  const handleBreakChange = (breakType: string) => {
    setBreakValue(breakType);
  };

  // const handleColorChange = (color: string) => {
  //   setCurrentColor(color);
  //   setColorScheme('Custom');
  // };

  const handleTextColorChange = (color: string) => {
    setTextColor(color); // Update the textColor state
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    // File size in bytes (100KB = 102400 bytes)
    if (file.size > 102400) {
      setLogoError('Logo size must be less than 100KB.');
      setTempInsertLogo('');
      return;
    }

    setLogoError('');

    const reader = new FileReader();
    reader.onloadend = () => {
      const base64Image = reader.result as string;
      setTempInsertLogo(base64Image);
    };
    reader.readAsDataURL(file);
  };

  const handleDeleteLogo = () => {
    setInsertLogo('');
    setTempInsertLogo('');
    setLogoError('');
    setOpenUploadLogo(null);
  };

  //   const handleWatermarkImageUpload = (
  //     event: React.ChangeEvent<HTMLInputElement>
  //   ) => {
  //     const file = event.target.files?.[0];
  //     if (file) {
  //       const imageUrl: string = URL.createObjectURL(file);
  //       setWatermarkImage(imageUrl); // ✅ FIXED
  //     }
  //   };
  const handlePageNumberAlignmentChange = (alignment: any) => {
    setAlignPageNumberText(alignment);
  };

  const marginIcons: { [key: string]: JSX.Element } = {
    Normal: <InsertDriveFileOutlinedIcon />,
    Narrow: <LibraryBooksOutlinedIcon />,
    Wide: <CropLandscapeIcon />
  };

  return (
    <Box sx={{ width: '100%' }}>
      {location.pathname?.includes('/pdf-configuration') && (
        <Box
          sx={{
            backgroundColor: '#f6f6f6'
          }}
        >
          <Card
            sx={{
              width: '100%',
              boxShadow: '0px 6px 12px rgba(0, 0, 0, 0.1)', // Enhanced shadow for depth
              borderRadius: '12px', // Smooth rounded corners
              overflow: 'hidden', // Ensures consistent border radius application
              backgroundColor: '#fff' // Ensures a clean, bright card
            }}
          >
            <CardHeader
              // sx={{
              //   backgroundColor: '#f6f6f6',
              //   boxShadow: '2px 3px 4px rgba(0, 0, 0, 0.05)',
              //   borderBottom: '1px solid #D7D6D6'
              // }}
              sx={{
                backgroundColor: '#fff',
                boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.1)', // Slight lift effect
                borderBottom: '1px solid #D7D6D6',
                padding: '12px 16px' // Improved spacing
              }}
              title={
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    width: '100%'
                  }}
                >
                  <Tooltip title="Orientation">
                    <IconButton
                      onClick={handleClickOrientation}
                      size="small"
                      sx={{ ml: 2 }}
                      aria-controls={
                        isOpenOrientation ? 'account-menu' : undefined
                      }
                      aria-haspopup="true"
                      aria-expanded={isOpenOrientation ? 'true' : undefined}
                    >
                      <AspectRatioIcon />
                    </IconButton>
                  </Tooltip>
                  <Divider orientation="vertical" flexItem />
                  <Tooltip title="Page Size">
                    <IconButton
                      onClick={handleClickPageSize}
                      size="small"
                      sx={{ ml: 2 }}
                      aria-controls={
                        isOpenPageSize ? 'page-size-menu' : undefined
                      }
                      aria-haspopup="true"
                      aria-expanded={isOpenPageSize ? 'true' : undefined}
                    >
                      <DescriptionOutlinedIcon />
                    </IconButton>
                  </Tooltip>
                  <Divider orientation="vertical" flexItem />

                  <Tooltip title="Page Breaks">
                    <IconButton
                      onClick={handleClickPageBreaks}
                      size="small"
                      sx={{ ml: 2 }}
                      aria-controls={
                        isOpenPageBreaks ? 'page-breaks-menu' : undefined
                      }
                      aria-haspopup="true"
                      aria-expanded={isOpenPageBreaks ? 'true' : undefined}
                    >
                      <InsertPageBreakOutlinedIcon />
                    </IconButton>
                  </Tooltip>

                  <Divider orientation="vertical" flexItem />

                  <Tooltip title="Margin">
                    <IconButton
                      onClick={handleClickMargin}
                      size="small"
                      sx={{ ml: 2 }}
                      aria-controls={
                        isOpenMarginValue ? 'page-margin-menu' : undefined
                      }
                      aria-haspopup="true"
                      aria-expanded={isOpenMarginValue ? 'true' : undefined}
                    >
                      <MarginOutlinedIcon />
                    </IconButton>
                  </Tooltip>

                  <Divider orientation="vertical" flexItem />

                  {/* New Logo Upload Option */}
                  <Tooltip title="Upload Logo">
                    <IconButton
                      onClick={(event) => handleClickUploadLogo(event)} // Add this function in your component
                      size="small"
                      sx={{ ml: 2 }}
                      aria-controls={
                        isOpenUploadLogo ? 'upload-logo-menu' : undefined
                      }
                      aria-haspopup="true"
                      aria-expanded={isOpenUploadLogo ? 'true' : undefined}
                    >
                      <UploadFileOutlinedIcon /> {/* Use an appropriate icon */}
                    </IconButton>
                  </Tooltip>

                  {/* <Divider orientation="vertical" flexItem /> */}

                  {/* Background Color */}
                  {/* <Tooltip title="Background Color">
                    <Box sx={{ backgroundColor: 'transparent' }}>
                      <ColorPickerButton
                        currentColor={currentColor}
                        onColorChange={handleColorChange}
                      />
                    </Box>
                  </Tooltip> */}

                  <Divider orientation="vertical" flexItem />

                  {/* Text Color */}
                  <Tooltip title="Text Color">
                    <Box>
                      <ColorPickerButton
                        currentColor={textColor}
                        onColorChange={handleTextColorChange}
                        isFontColor
                      />
                    </Box>
                  </Tooltip>

                  <Divider orientation="vertical" flexItem />
                  <Tooltip title="Header & Footer Height">
                    <IconButton
                      onClick={handleClickHeaderFooter}
                      size="small"
                      sx={{ ml: 2 }}
                      aria-controls={
                        isOpenHeaderFooterHeight ? 'margins-menu' : undefined
                      }
                      aria-haspopup="true"
                      aria-expanded={
                        isOpenHeaderFooterHeight ? 'true' : undefined
                      }
                    >
                      <HeightOutlinedIcon />
                    </IconButton>
                  </Tooltip>

                  <Divider orientation="vertical" flexItem />

                  {/* Font Family Option */}
                  <Tooltip title="Font Family">
                    <IconButton
                      onClick={handleClickFontFamily}
                      size="small"
                      sx={{ ml: 2 }}
                      aria-controls={
                        isOpenFontFamily ? 'font-family-menu' : undefined
                      }
                      aria-haspopup="true"
                      aria-expanded={isOpenFontFamily ? 'true' : undefined}
                    >
                      <FontDownloadOutlinedIcon />
                    </IconButton>
                  </Tooltip>

                  <Divider orientation="vertical" flexItem />

                  {/* Font Size Option */}
                  <Tooltip title="Font Size">
                    <IconButton
                      onClick={handleClickFontSize}
                      size="small"
                      sx={{ ml: 2 }}
                      aria-controls={
                        isOpenFontSize ? 'font-size-menu' : undefined
                      }
                      aria-haspopup="true"
                      aria-expanded={isOpenFontSize ? 'true' : undefined}
                    >
                      <TextIncreaseIcon />
                    </IconButton>
                  </Tooltip>

                  {/* <Divider orientation="vertical" flexItem /> */}
                  {/* Logo alignment Option */}
                  {/* <Tooltip title="Logo Alignment">
                    <IconButton
                      onClick={handleClickLogoAlignment}
                      size="small"
                      sx={{ ml: 2 }}
                      aria-controls={
                        isOpenLogoAlignment ? 'logo-alignment-menu' : undefined
                      }
                      aria-haspopup="true"
                      aria-expanded={isOpenLogoAlignment ? 'true' : undefined}
                    >
                      <VerticalAlignCenterOutlinedIcon />
                    </IconButton>
                  </Tooltip> */}

                  <Divider orientation="vertical" flexItem />
                  <Tooltip title="Watermark">
                    <IconButton
                      onClick={handleClickWatermark}
                      size="small"
                      sx={{ ml: 2 }}
                      aria-controls={
                        isOpenWatermark ? 'watermark-menu' : undefined
                      }
                      aria-haspopup="true"
                      aria-expanded={isOpenWatermark ? 'true' : undefined}
                    >
                      <BrandingWatermarkOutlinedIcon />
                    </IconButton>
                  </Tooltip>

                  <Divider orientation="vertical" flexItem />
                  <Tooltip title="Footer Text">
                    <IconButton
                      onClick={handleClickFooterText}
                      size="small"
                      sx={{ ml: 2 }}
                      aria-controls={
                        isOpenFooterText ? 'footer-text-menu' : undefined
                      }
                      aria-haspopup="true"
                      aria-expanded={isOpenFooterText ? 'true' : undefined}
                    >
                      <TextFieldsOutlinedIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              }
            />
          </Card>
        </Box>
      )}

      {/* orientation  */}
      <Menu
        anchorEl={openOrientation}
        id="account-menu"
        open={isOpenOrientation}
        onClose={handleCloseOrientation}
        onClick={handleCloseOrientation}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1
              },
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0
              }
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <ListItemButton
          selected={selectedIndex === 0}
          onClick={() => handleListItemClick(0, 'Portrait')}
        >
          <ListItemIcon>
            <CropPortraitIcon />
          </ListItemIcon>
          <ListItemText primary="Portrait" sx={{ fontSize: '14px' }} />
        </ListItemButton>
        <ListItemButton
          selected={selectedIndex === 1}
          onClick={() => handleListItemClick(1, 'Landscape')}
        >
          <ListItemIcon>
            <CropLandscapeIcon />
          </ListItemIcon>
          <ListItemText primary="Landscape" />
        </ListItemButton>
      </Menu>

      {/* pages size */}
      <Menu
        anchorEl={openPageSize}
        id="page-size-menu"
        open={isOpenPageSize}
        onClose={handleClosePageSize}
        onClick={handleClosePageSize}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1
              },
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0
              }
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {[
          ['A1', 'A2'],
          ['A3', 'A10'],
          ['A6', 'A4']
        ].map((pair, index) => (
          <ListItem
            key={`${index + 1}`}
            sx={{
              width: '100%',
              padding: '2px 4px',
              display: 'flex',
              alignItems: 'center'
            }}
          >
            {pair.map((size) => (
              <ListItemButton
                key={size}
                selected={sizeValue === size}
                onClick={() => handleSizeChange(size)}
                sx={{
                  justifyContent: 'flex-start',
                  padding: '4px 8px',
                  minHeight: '32px'
                }}
              >
                <ListItemText primary={size} />
              </ListItemButton>
            ))}
          </ListItem>
        ))}
      </Menu>

      {/* Breaks */}
      <Menu
        anchorEl={openPageBreaks}
        id="page-breaks-menu"
        open={isOpenPageBreaks}
        onClose={handleClosePageBreaks}
        onClick={handleClosePageBreaks}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1
              },
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0
              }
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <ListItemButton
          selected={breakValue === 'PageWrap'}
          onClick={() => handleBreakChange('PageWrap')}
        >
          <ListItemIcon>
            <InsertDriveFileOutlinedIcon />
          </ListItemIcon>
          <ListItemText primary="Single Page" />
        </ListItemButton>
        <ListItemButton
          selected={breakValue === 'TextWrap'}
          onClick={() => handleBreakChange('TextWrap')}
        >
          <ListItemIcon>
            <LibraryBooksOutlinedIcon />
          </ListItemIcon>
          <ListItemText primary="Multiple Page" />
        </ListItemButton>
      </Menu>

      {/* page numberalignments */}
      <Menu
        anchorEl={openPageAlignment}
        id="page-alignment-menu"
        open={isOpenPageAlignment}
        onClose={handleClosePageAlignment}
        onClick={handleClosePageAlignment}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1
              },
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0
              }
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {['left', 'center', 'right'].map((alignment) => (
          <ListItemButton
            key={alignment}
            selected={alignPageNumberText === alignment}
            onClick={() => handlePageNumberAlignmentChange(alignment)}
          >
            <ListItemIcon>
              {alignment === 'Left' ? (
                <FormatAlignLeft />
              ) : alignment === 'Center' ? (
                <FormatAlignCenter />
              ) : (
                <FormatAlignRight />
              )}
            </ListItemIcon>
            <ListItemText primary={alignment} />
          </ListItemButton>
        ))}
      </Menu>

      {/* page margin menu */}
      <Menu
        anchorEl={openMarginValue}
        id="page-margin-menu"
        open={isOpenMarginValue}
        onClose={handleCloseMarginValue}
        onClick={handleCloseMarginValue}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1
              },
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0
              }
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {['Normal', 'Narrow', 'Wide'].map((margin) => (
          <ListItemButton
            key={margin}
            selected={marginValue === margin}
            onClick={() => setMarginValue(margin)}
          >
            <ListItemIcon>{marginIcons[margin]}</ListItemIcon>
            <ListItemText primary={margin} />
          </ListItemButton>
        ))}
      </Menu>

      {/* upload logo menu */}
      <Menu
        anchorEl={openUploadLogo}
        id="upload-logo-menu"
        open={isOpenUploadLogo}
        onClose={handleCloseUploadLogo}
        // onClick={handleLogoUpload}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1
              },
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0
              }
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box sx={{ textAlign: 'center', padding: '8px' }}>
          <Typography>Upload Logo</Typography>
          <Button
            component="label"
            variant="outlined"
            startIcon={<UploadIcon sx={{ color: '#007BFF' }} />} // Upload icon
            sx={{
              textTransform: 'none',
              color: 'gray',
              borderColor: 'lightgray',
              borderRadius: '8px',
              padding: '10px 20px',
              width: '200px'
            }}
          >
            Image Or Logo
            <input
              type="file"
              accept="image/*,.svg"
              hidden
              onChange={handleLogoUpload}
            />
          </Button>

          {logoError && (
            <Typography color="error" variant="body2" sx={{ mt: 1 }}>
              {logoError}
            </Typography>
          )}
          {logo && (
            <Box>
              <Typography variant="body2" color="textSecondary">
                Selected Logo:
              </Typography>
              <img src={tempInsertLogo} alt="Logo Preview" width={50} />
              {tempInsertLogo && (
                <Tooltip title="Remove Logo">
                  <IconButton
                    size="small"
                    onClick={handleDeleteLogo}
                    sx={{
                      p: 0.5,
                      color: 'error.main'
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          )}
          <Button
            onClick={handleCloseUploadLogo}
            variant="contained"
            sx={{ textAlign: 'center', mt: 2 }}
          >
            Apply
          </Button>
        </Box>
      </Menu>

      {/* header and footer menu */}
      <Menu
        anchorEl={openHeaderFooterHeight}
        id="header-footer-height-menu"
        open={isOpenHeaderFooterHeight}
        onClose={handleCloseHeaderFooter}
        //   onClick={handleCloseHeaderFooter}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1
              },
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0
              }
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box sx={{ textAlign: 'center' }}>
          <Typography
            variant="h6"
            sx={{
              marginRight: '152px',
              fontSize: '14px',
              marginTop: '5px'
            }}
          >
            Header
          </Typography>
          <ListItem>
            <TextField
              label="Header Height"
              variant="outlined"
              size="small"
              type="number"
              value={tempHeaderHeight}
              defaultValue={1}
              onChange={(e) => setTempHeaderHeight(e.target.value)}
              sx={{ minWidth: '200px', marginBottom: '10px' }}
              inputProps={{ min: 1, max: 100 }}
              // Ensures the height can't be set to a value lower than 1
              // eslint-disable-next-line react/jsx-no-duplicate-props
              InputProps={{
                endAdornment: <InputAdornment position="end">px</InputAdornment>
              }}
            />
          </ListItem>
          <Typography
            variant="h6"
            sx={{
              marginRight: '152px',
              fontSize: '14px',
              marginTop: '5px'
            }}
          >
            Footer
          </Typography>
          <ListItem>
            <TextField
              label="Footer Height"
              variant="outlined"
              size="small"
              type="number"
              value={tempFooterHeight}
              onChange={(e) => setTempFooterHeight(e.target.value)}
              sx={{ minWidth: '200px' }}
              inputProps={{ min: 1, max: 100 }} // Ensures the width can't be set to a value lower than 1
              // eslint-disable-next-line react/jsx-no-duplicate-props
              InputProps={{
                endAdornment: <InputAdornment position="end">px</InputAdornment>
              }}
            />
          </ListItem>
          <Button
            onClick={() => {
              setHeaderHeight(tempHeaderHeight);
              setFooterHeight(tempFooterHeight);
              handleCloseHeaderFooter();
            }}
            variant="contained"
            sx={{ textAlign: 'center' }}
          >
            Apply
          </Button>
        </Box>
      </Menu>

      {/* Font Family Menu */}
      <Menu
        anchorEl={openFontFamily}
        id="font-family-menu"
        open={isOpenFontFamily}
        onClose={handleCloseFontFamily}
        onClick={handleCloseFontFamily}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1
              },
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0
              }
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {['Times-Roman', 'Helvetica', 'Courier'].map((font) => (
          <ListItemButton
            key={font}
            selected={fontFamily === font}
            onClick={() => setFontFamily(font)}
          >
            <ListItemText primary={font} sx={{ fontFamily: font }} />
          </ListItemButton>
        ))}
      </Menu>

      {/* Font Size Menu */}
      <Menu
        anchorEl={openFontSize}
        id="font-size-menu"
        open={isOpenFontSize}
        onClose={handleCloseFontSize}
        onClick={handleCloseFontSize}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1
              },
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0
              }
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {['12', '14', '15', '16', '18', '20', '22'].map((fontsize) => (
          <ListItemButton
            key={fontsize}
            selected={fontSize === fontsize}
            onClick={() => setFontSize(fontsize)}
          >
            <ListItemText primary={fontsize} sx={{ fontSize: fontsize }} />
          </ListItemButton>
        ))}
      </Menu>

      {/* Logo Alignment Menu */}
      {/* <Menu
        anchorEl={openLogoAlignment}
        id="logo-alignment-menu"
        open={isOpenLogoAlignment}
        onClose={handleCloseLogoAlignment}
        onClick={handleCloseLogoAlignment}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1
              },
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0
              }
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {[
          { value: 'start', label: 'Start', icon: <StartIcon /> },
          {
            value: 'center',
            label: 'Center',
            icon: <VerticalAlignCenterIcon />
          },
          { value: 'end', label: 'End', icon: <LastPage /> }
        ].map((item) => (
          <ListItemButton
            key={item.value}
            selected={logoAlignment === item.value}
            onClick={() => setLogoAlignment(item.value)}
          >
            <ListItemIcon>{item.icon}</ListItemIcon>
            <ListItemText primary={item.label} />
          </ListItemButton>
        ))}
      </Menu> */}

      {/* Watermark Menu */}
      <Menu
        anchorEl={openWatermark}
        id="watermark-menu"
        open={isOpenWatermark}
        onClose={handleCloseWatermark}
        // onClick={handleCloseWatermark}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1
              },
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0
              }
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box
          sx={{
            textAlign: 'center',
            padding: '8px',
            maxWidth: '250px',
            // margin: 'auto'
            marginTop: '18px'
          }}
        >
          <div className="d-flex">
            <TextField
              label="Enter Watermark Text"
              type="text"
              value={tempWatermarkText}
              onChange={(e) => setTempWatermarkText(e.target.value)}
              variant="outlined"
              size="small"
              sx={{ flex: 1 }}
            />
            {watermarkText && (
              <Tooltip title="Remove WaterMark">
                <IconButton
                  size="small"
                  onClick={() => {
                    setTempWatermarkText('');
                    setWatermarkText('');
                    setOpenWatermark(null);
                  }}
                  sx={{
                    p: 0.5,
                    color: 'error.main'
                  }}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </div>
          <Button
            onClick={handleCloseWatermark}
            variant="contained"
            sx={{ textAlign: 'center', mt: 2 }}
          >
            Apply
          </Button>
        </Box>
      </Menu>

      {/* FooterText Menu */}
      <Menu
        anchorEl={openFooterText}
        id="footer-text-menu"
        open={isOpenFooterText}
        onClose={handleCloseFooterText}
        // onClick={handleCloseFooterText}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1
              },
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0
              }
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box sx={{ padding: '10px', textAlign: 'center' }}>
          <ListItem>
            <TextField
              label="Footer Text"
              variant="outlined"
              size="small"
              type="text"
              value={tempFooterText}
              onChange={(e) => {
                if (e.target.value.length <= 50) {
                  setTempFooterText(e.target.value);
                }
              }}
              inputProps={{ maxLength: 50 }}
              error={tempFooterText.length >= 50}
              helperText={
                tempFooterText.length >= 50
                  ? 'Maximum 50 characters allowed'
                  : ''
              }
              sx={{ minWidth: '120px' }}
            />
          </ListItem>
          <Button
            onClick={() => {
              setFooterText(tempFooterText); // apply value
              handleCloseFooterText(); // close menu
            }}
            variant="contained"
          >
            Apply
          </Button>
        </Box>
      </Menu>

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          // height: '100vh', // Full viewport height
          padding: '40px' // Prevents content from touching edges
        }}
      >
        <Box
          sx={{
            marginTop: '30px',
            marginBottom: '70px',
            width: '60%', // Slightly wider for better visibility
            backgroundColor: '#fff', // Clean background for the content
            boxShadow: '0px 6px 12px rgba(0, 0, 0, 0.1)', // Soft shadow for elevation
            borderRadius: '12px', // Smooth rounded corners
            overflow: 'hidden', // Ensures content stays within the border radius
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '10px' // Proper padding inside the box
          }}
        >
          <PdfFormResponse
            orientationValue={orientationValue}
            sizeValue={sizeValue}
            breakValue={breakValue}
            marginValue={marginValue}
            // logoAlignment={logoAlignment}
            currentColor={currentColor}
            // colorScheme={colorScheme}
            textColor={textColor}
            fontFamily={fontFamily}
            fontSize={fontSize}
            // alignText={alignText}
            insertLogo={insertLogo || orgData?.logo}
            headerTitle={orgData?.name}
            watermarkText={watermarkText}
            watermarkImage={watermarkImage}
            alignPageNumberText={alignPageNumberText}
            headerHeight={headerHeight}
            // headerWidth={undefined}
            footerHeight={footerHeight}
            footerText={footerText}
          />
        </Box>
      </Box>
    </Box>
  );
}
