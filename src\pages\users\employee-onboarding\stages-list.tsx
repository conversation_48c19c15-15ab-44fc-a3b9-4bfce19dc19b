/* eslint-disable react-hooks/exhaustive-deps */
// Package Imports
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
import Shell from '../../../components/layout/Shell';
import { SubMenu } from '../../../components/form.elements';
import { UserNavBarModules } from '../../../components/users/UsersList';
import Sidebar from '../../../components/reusable/Sidebar';
import LoaderUI from '../../../components/reusable/loaderUI';
import StagesList from '../../../components/users/employee-onboarding/StagesList';
import { AppDispatch, AppState } from '../../../redux/app.store';
import { getstages } from '../../../redux/reducers/stages.reducer';

const StagesListPage = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [stagesListData, setStagesListData] = React.useState([]);
  const { stagesList, isLoading } = useSelector(
    (state: AppState) => state.stages
  );

  const getSubMenu = () => {
    return <SubMenu backNavigation />;
  };

  const menuItems = UserNavBarModules();

  const getDrawer = () => {
    return <Sidebar menuItems={menuItems} userType={undefined} />;
  };

  const getStagesListData = async () => {
    const res = await dispatch(getstages(null));
    if (res.meta.requestStatus === 'rejected') {
      toast.error(res.payload?.message);
    } else {
      setStagesListData(res.payload.stages);
    }
  };

  useEffect(() => {
    // if (stagesList?.length === 0) {
    //   getStagesListData();
    // } else {
    //   setStagesListData(stagesList);
    // }
    getStagesListData();
  }, []);

  return (
    <Shell subMenu={getSubMenu()} showDrawer drawerData={getDrawer()}>
      {isLoading ? (
        <LoaderUI />
      ) : (
        <StagesList stagesListData={stagesListData || []} />
      )}
    </Shell>
  );
};

export default StagesListPage;
