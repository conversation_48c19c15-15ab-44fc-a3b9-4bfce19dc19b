/* eslint-disable no-await-in-loop */
/* eslint-disable no-restricted-syntax */
// Global imports
import {
  Box,
  IconButton,
  Typography,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Checkbox,
  Button,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormGroup
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
// import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';
import UploadIcon from '@mui/icons-material/Upload';
import ChevronRightOutlinedIcon from '@mui/icons-material/ChevronRightOutlined';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import TagsInput from 'react-tagsinput';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

// Local imports
import { AppDispatch, AppState } from '../../../redux/app.store';
import {
  checkfolder,
  getassessmentclients,
  getclientforms,
  sendpdf
} from '../../../redux/reducers/clients.reducer';
import { getapps } from '../../../redux/reducers/apps.reducer';
import 'react-tagsinput/react-tagsinput.css';
import { Icon, SubMenu } from '../../../components/form.elements';
import Sidebar from '../../../components/reusable/Sidebar';
import Shell from '../../../components/layout/Shell';
import { UserNavBarModules } from '../../../components/users/UsersList';
import LoaderUI from '../../../components/reusable/loaderUI';
import '../../../css/client-assessment-styles.scss';
import { RootState } from '../../../redux/reducers';
import { getpdfconfig } from '../../../redux/reducers/addressconfig.reducer';

const ClientAssessmentListPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { assessmentClients, isLoading }: any = useSelector(
    (state: AppState) => state.clients
  );
  const navigate = useNavigate();
  const [appId, setAppId] = useState();
  const [path, setPath]: any = useState();
  const [clientId, setClientId] = useState();
  const [type, setType]: any = useState();
  const [tags, setTags] = useState<any>([]);
  const [open, setOpen] = useState(false);
  const [checked, setChecked] = useState(false);
  const [input, setInput]: any = useState();
  const [pdfType, setPdfType] = useState('');
  const [pdfData, setPdfData]: any = useState();
  const [selectAll, setSelectAll] = useState(false);
  const [selectedForms, setSelectedForms] = useState<string[]>([]);
  const { clientForms } = useSelector((state: RootState) => state.clients);
  const formNames = clientForms?.map((data: any) => data.form?.name);
  const formIds = clientForms?.map((data: any) => data?.form?.form_id);
  const formResponseData: any = clientForms;

  // Generate the selected PDF data URLs based on selected forms
  const selectedPdfFormIds = selectedForms.map((formName) => {
    const index = formNames.indexOf(formName);
    return index !== -1 ? formIds[index] : null;
  });

  useEffect(() => {
    const getClientsList = async () => {
      const response = await dispatch(getapps(null));
      if (response.payload.status) {
        response.payload.data.forEach(async (app: any) => {
          if (app?.industry_app_process?.process_code === 'HC_CLIASS') {
            setAppId(app?.app_id);
            await dispatch(getassessmentclients(app?.app_id));
          }
        });
      }
    };
    getClientsList();
  }, [dispatch]);

  const getData = async () => {
    try {
      const response = await dispatch(
        getclientforms({ id: clientId, type: 'client-assessment' })
      );
      if (response.payload.error) {
        toast.error(response.payload?.error);
      } else if (response.payload.status) {
        // toast.success(response.payload?.message);
      }
    } catch (error: any) {
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  const getPdfData = async () => {
    const response = await dispatch(getpdfconfig(null));
    if (response.payload.status) {
      setPdfData(response.payload.data);
    }
  };
  console.log(pdfData);
  useEffect(() => {
    if (clientId) {
      getData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [clientId]);

  useEffect(() => {
    // generatePDFs();
    getPdfData();
  }, [formResponseData, pdfType, type, clientId]);

  const handleSendMailOpen = (param: any) => {
    setClientId(param);
    setType('share');
    setOpen(true);
  };

  const handleClickOpen = async (param: any) => {
    setClientId(param);
    setType('upload');

    let currentFormIds: string[] = [];
    try {
      const response = await dispatch(
        getclientforms({ id: param, type: 'client-assessment' })
      );
      if (response.payload.error) {
        toast.error(response.payload?.error);
      } else if (response.payload.status) {
        const clientFormsData = response.payload?.data;

        // Extract formIds from fresh API response
        currentFormIds = clientFormsData?.map(
          (data: any) => data?.form?.form_id
        );
      }
    } catch (error: any) {
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }

    const uploadPayload = {
      request_type: 'upload',
      app_id: appId,
      pdf_type: 'multiple',
      forms: currentFormIds
    };

    console.log(uploadPayload, 'uploadPayload');

    try {
      const response = await dispatch(
        checkfolder({
          clientId: param
        })
      );
      if (response.payload.error) {
        // setSnackbarOpen({
        //   status: true,
        //   message: response.payload?.error,
        //   type: 'error'
        // });
        toast.error(response.payload?.error);
      } else if (response.payload.status) {
        toast.success(response.payload?.message);
        if (response.payload.isExisted) {
          setOpen(true);
        } else {
          try {
            const res = await dispatch(
              sendpdf({
                clientId: param,
                data: {
                  request_type: 'upload',
                  app_id: appId,
                  pdf_type: 'multiple',
                  forms: currentFormIds
                }
              })
            );
            if (res.payload.error) {
              // setSnackbarOpen({
              //   status: true,
              //   message: res.payload?.error,
              //   type: 'error'
              // });
              toast.error(res.payload?.error);
            } else if (res.payload.status) {
              // setSnackbarOpen({
              //   status: true,
              //   message: res.payload?.message,
              //   type: 'success'
              // });
              // toast.success(res.payload?.message);
            }
          } catch (error: any) {
            toast.error(
              error?.message || 'Something Went Wrong Please try again later'
            );
          }
        }
      }
    } catch (error: any) {
      toast.error(
        error?.message || 'Something Went Wrong Please try again later'
      );
    }
  };

  // const handleClickOpen = async (param: any, type2: string) => {
  //   setClientId(param);
  //   // console.log(param, 'param');
  //   setType(type2);
  //   const individualUrls = await generateIndividualPDFUrls();
  //   if (type2 === 'share') {
  //     setOpen(true);
  //   } else {
  //     const uploadPayload = {
  //       request_type: 'upload',
  //       app_id: appId,
  //       pdf_type: 'multiple',
  //       list_of_forms: formNames,
  //       pdf_data_url: individualUrls
  //     };
  //     console.log(uploadPayload, 'uploadPayload');
  //     const response = await dispatch(checkfolder({ clientId: param }));
  //     if (response.payload.status) {
  //       if (response.payload.isExisted) {
  //         setOpen(true);
  //       } else {
  //         await dispatch(
  //           sendpdf({
  //             clientId: param,
  //             data: {
  //               request_type: 'upload',
  //               app_id: appId,
  //               pdf_type: 'multiple',
  //               list_of_forms: formNames,
  //               pdf_data_url: individualUrls
  //             }
  //           })
  //         );
  //       }
  //     }
  //   }
  // };

  const handleClose = () => {
    setOpen(false);
    setSelectAll(false);
    setSelectedForms([]);
    setPdfType('');
  };

  const handleChange = (event: any) => {
    setChecked(event.target.checked);
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInput(event.target.value);
  };

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  };

  const handleTagsChange = (tagss: any) => {
    if (isValidEmail(tagss[tagss.length - 1])) {
      setTags(tagss);
    }
  };

  const columns = [
    { field: 'name', headerName: 'Name', flex: 1 },
    { field: 'mobile_number', headerName: 'Phone number', flex: 1 },
    {
      field: 'action',
      headerName: 'Action',
      flex: 1,
      renderCell: (params: any) => (
        <Box>
          <IconButton onClick={() => handleClickOpen(params.id)}>
            <UploadIcon color="primary" />
          </IconButton>
          <IconButton onClick={() => handleSendMailOpen(params.id)}>
            <Icon name="Mail" fontSize="small" color="primary" />
          </IconButton>
          <IconButton
            onClick={() =>
              navigate(
                `/users/client-assessment/form-values/${params.id}/${appId}`
              )
            }
          >
            <ChevronRightOutlinedIcon color="primary" />
          </IconButton>
        </Box>
      )
    }
  ];

  const menuItems = UserNavBarModules();

  const getSubMenu = () => <SubMenu backNavigation />;
  const getDrawer = () => (
    <Sidebar menuItems={menuItems} userType={undefined} />
  );

  async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();

    const formData = new FormData(event.currentTarget);
    const formJson = Object.fromEntries((formData as any).entries());
    // Filter out any null/undefined
    if (type === 'share') {
      const email = formJson?.email;
      const sendMailPayload = {
        request_type: 'share',
        app_id: appId,
        email,
        cc_emails: tags,
        pdf_type: pdfType,
        forms: selectedPdfFormIds
      };
      console.log(sendMailPayload, 'sendMailPayload');
      if (email) {
        const response = await dispatch(
          sendpdf({
            clientId,
            data: {
              request_type: 'share',
              app_id: appId,
              email,
              cc_emails: tags,
              pdf_type: pdfType,
              forms: selectedPdfFormIds
            }
          })
        );
        if (response.payload.status) {
          toast.success(response.payload?.message);
          handleClose();
        }
      }
    } else {
      const folderName = formJson?.folderName;
      const checkbox = formJson?.checkbox;

      const uploadPayload = {
        request_type: 'upload',
        app_id: appId,
        path: folderName,
        pdf_type: 'multiple',
        forms: formIds
      };
      console.log(uploadPayload, 'uploadPayload');
      if (folderName && !checkbox) {
        const response = await dispatch(checkfolder({ clientId, folderName }));
        if (response.payload.status) {
          if (response.payload.isExisted) {
            setPath(folderName);
            setInput('');
          } else {
            const resp = await dispatch(
              sendpdf({
                clientId,
                data: {
                  request_type: 'upload',
                  app_id: appId,
                  path: folderName,
                  pdf_type: 'multiple',
                  forms: formIds
                }
              })
            );
            if (resp.payload.status) {
              setChecked(false);
              setPath('');
              setInput('');
              handleClose();
            }
          }
        }
      } else if (checkbox) {
        const response = await dispatch(
          sendpdf({
            clientId,
            data: {
              request_type: 'upload',
              app_id: appId,
              pdf_type: 'multiple',
              forms: formIds
            }
          })
        );
        if (response.payload.status) {
          handleClose();
          setChecked(false);
          setPath('');
          setInput('');
        }
      }
    }
  }

  return (
    <Shell showDrawer drawerData={getDrawer()} subMenu={getSubMenu()}>
      <Box
        sx={{ display: 'flex', overflow: 'hidden', flexDirection: 'column' }}
      >
        {isLoading ? (
          <LoaderUI />
        ) : (
          <Box
            sx={{
              backgroundColor: '#FFFFFF',
              padding: '10px',
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              flexGrow: 1,
              minHeight: 'calc(100vh - 100px)'
            }}
            className="main-container"
          >
            <Box sx={{ width: '100%', overflowX: 'auto' }}>
              <Box sx={{ backgroundColor: '#FAF9F8' }}>
                <div>
                  <Typography
                    sx={{
                      fontSize: '22px',
                      fontWeight: '600',
                      // marginTop: '50px',
                      marginBottom: '10px',
                      padding: '2px',
                      marginLeft: '20px'
                    }}
                  >
                    Clients
                  </Typography>
                </div>
              </Box>

              <Box className="content">
                <Box className="div-styles">
                  <Box sx={{ width: '100%', flexGrow: 1, minHeight: '300px' }}>
                    {assessmentClients?.length > 0 ? (
                      <DataGrid
                        rows={assessmentClients}
                        getRowId={(row) => row?.client_id}
                        columns={columns}
                        initialState={{
                          pagination: {
                            paginationModel: { page: 0, pageSize: 5 }
                          }
                        }}
                        pageSizeOptions={[5, 10]}
                        // checkboxSelection
                        getRowClassName={() => `striped-row`}
                        sx={{
                          '& .MuiDataGrid-columnHeader': {
                            '& .MuiDataGrid-menuIcon': {
                              visibility: 'visible',
                              width: 'auto'
                            }
                          }
                        }}
                      />
                    ) : (
                      <Box className="no-applicants">
                        <Typography
                          sx={{ fontSize: '17px', fontWeight: '500' }}
                        >
                          No records found.
                        </Typography>
                      </Box>
                    )}
                  </Box>
                  <Box sx={{ padding: '20px' }}>
                    <Dialog
                      open={open}
                      onClose={handleClose}
                      maxWidth="sm"
                      fullWidth
                      PaperProps={{ component: 'form', onSubmit: handleSubmit }}
                    >
                      <DialogContent sx={{ paddingLeft: 7, paddingRight: 7 }}>
                        {type === 'share' ? (
                          <>
                            <TextField
                              autoFocus
                              required
                              margin="dense"
                              id="email"
                              name="email"
                              label="Please enter email"
                              placeholder="Enter Email"
                              type="email"
                              fullWidth
                              variant="standard"
                              InputLabelProps={{
                                shrink: true,
                                style: { color: 'black', fontSize: '25px' }
                              }}
                              InputProps={{ style: { marginTop: '40px' } }}
                            />
                            <TagsInput
                              value={tags}
                              onChange={handleTagsChange}
                            />
                            <FormControl component="fieldset" sx={{ mt: 4 }}>
                              <Typography sx={{ fontWeight: 500, mb: 1 }}>
                                PDF Format:
                              </Typography>
                              <RadioGroup
                                row
                                aria-label="pdfType"
                                name="pdfType"
                                value={pdfType}
                                onChange={(e) => {
                                  const selectedType = e.target.value;
                                  setPdfType(selectedType);

                                  if (selectedType === 'single') {
                                    setSelectAll(true);
                                    setSelectedForms(formNames);
                                  } else {
                                    setSelectAll(false);
                                    setSelectedForms([]);
                                  }
                                }}
                              >
                                <FormControlLabel
                                  value="single"
                                  control={<Radio />}
                                  label="Single PDF"
                                />
                                <FormControlLabel
                                  value="multiple"
                                  control={<Radio />}
                                  label="Multiple PDF"
                                />
                              </RadioGroup>

                              <Box sx={{ mt: 4 }}>
                                <Typography sx={{ fontWeight: 500, mb: 1 }}>
                                  Choose Forms:
                                </Typography>
                                <FormGroup>
                                  <FormControlLabel
                                    control={
                                      <Checkbox
                                        checked={selectAll}
                                        onChange={({
                                          target: { checked: isChecked }
                                        }) => {
                                          setSelectAll(isChecked);
                                          setSelectedForms(
                                            isChecked ? formNames : []
                                          );
                                        }}
                                      />
                                    }
                                    label="Select All Forms"
                                  />
                                  {formNames.map((form) => (
                                    <FormControlLabel
                                      key={form}
                                      control={
                                        <Checkbox
                                          checked={selectedForms.includes(form)}
                                          onChange={({
                                            target: { checked: isChecked }
                                          }) => {
                                            const updated = isChecked
                                              ? [...selectedForms, form]
                                              : selectedForms.filter(
                                                  (f) => f !== form
                                                );
                                            setSelectedForms(updated);
                                            setSelectAll(
                                              updated.length ===
                                                formNames.length
                                            );
                                          }}
                                        />
                                      }
                                      label={form}
                                    />
                                  ))}
                                </FormGroup>
                              </Box>
                            </FormControl>
                          </>
                        ) : (
                          <>
                            {path && (
                              <DialogContentText
                                sx={{
                                  fontSize: '20px',
                                  fontWeight: '500',
                                  color: 'black'
                                }}
                              >
                                <Typography
                                  sx={{
                                    fontSize: '24px',
                                    fontWeight: '600',
                                    paddingTop: 4
                                  }}
                                >
                                  Folder name already exists !
                                </Typography>
                                <Checkbox
                                  disabled={input !== ''}
                                  checked={checked}
                                  onChange={handleChange}
                                  inputProps={{ 'aria-label': 'controlled' }}
                                  sx={{ paddingLeft: 0 }}
                                  id="checkbox"
                                  name="checkbox"
                                />
                                Use same folder
                              </DialogContentText>
                            )}
                            {path && (
                              <Typography
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }}
                              >
                                (or)
                              </Typography>
                            )}
                            <TextField
                              autoFocus
                              disabled={!!checked}
                              onChange={handleInputChange}
                              margin="dense"
                              id="name"
                              name="folderName"
                              label="Please enter new folder"
                              placeholder="Enter Folder Name"
                              type="text"
                              fullWidth
                              variant="standard"
                              InputLabelProps={{
                                shrink: true,
                                style: { color: 'black', fontSize: '25px' }
                              }}
                              InputProps={{ style: { marginTop: '40px' } }}
                            />
                          </>
                        )}
                      </DialogContent>
                      <DialogActions
                        sx={{
                          paddingBottom: 5,
                          paddingLeft: 6,
                          paddingRight: 5
                        }}
                      >
                        <Button onClick={handleClose}>Cancel</Button>
                        <Button type="submit">Ok</Button>
                      </DialogActions>
                    </Dialog>
                  </Box>
                </Box>
              </Box>
            </Box>
          </Box>
        )}
      </Box>
    </Shell>
  );
};

export default ClientAssessmentListPage;
