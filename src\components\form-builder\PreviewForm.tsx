// Package Imports
import Card from '@mui/material/Card';
import * as Yup from 'yup';
import { useEffect, useState } from 'react';
import { Box } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import _ from 'lodash';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { AppForm, FormSectionsAsPlain } from '../form.elements';
import '../../css/index.scss';

// Local Imports
import '../../css/preview-form.scss';
import image from '../../../public/previewImg.png';
import { FIELD, GROUP } from '../../types';
import {
  formatText,
  removeHtmlTags,
  generateEmptyObjectForArraySections
} from '../../utils/functions';
import { AppDispatch, AppState } from '../../redux/app.store';
import {
  updateFormLayout,
  updateSectionViewType,
  validatepreviewform
} from '../../redux/reducers/form.reducer';
import FormHeader from './features/Header';
import Shell from '../layout/Shell';
// import { SnackbarElement } from '../reusable/SnackbarElement';

const PreviewForm: React.FC<{ form: any }> = ({ form }) => {
  const { activeTheme, tabletLayout, sectionViewType, deviceLayoutMode }: any =
    useSelector((state: AppState) => state.form);
  const dispatch = useDispatch<AppDispatch>();
  // Check the desktopView key whether it is computer or desktopView
  const [previewType, setPreviewType] = useState(
    deviceLayoutMode || 'computer'
  );
  // const [snackMessage, setSnackMessage] = useState("");
  // const [openSnackbar, setOpenSnackbar] = useState(false);
  // const [snackbarMessage, setSnackbarMessage] = useState('');
  // const [snackbarSeverity, setSnackbarSeverity] = useState<
  //   'success' | 'error' | 'warning' | 'info'
  // >('success');
  const { id } = useParams<string>();

  useEffect(() => {
    if (form) {
      dispatch(updateFormLayout(form?.tabletLayout || 'singleColumn'));
      dispatch(updateSectionViewType(form?.sectionViewType || 'plain'));
    }
  }, [form, dispatch]);

  const clickPreviewType = (type: string) => {
    setPreviewType(type);
  };
  // const handleClose = (
  //   _event: React.SyntheticEvent | Event,
  //   reason?: string
  // ) => {
  //   if (reason === "clickaway") {
  //     return;
  //   }
  //   setOpenSnackbar(false);
  // };
  // const action = (
  //   <>
  //     <IconButton
  //       size="small"
  //       aria-label="close"
  //       color="inherit"
  //       onClick={handleClose}
  //     >
  //       <Close fontSize="small" />
  //     </IconButton>
  //   </>
  // );
  const getSignatureValues = (
    type: 'agree' | 'date',
    name: string,
    group_key: string
  ) => {
    const values = form?.values || {};
    let rtn;
    if (type === 'date') {
      if (values[group_key]) {
        if (values[group_key][`${name}_${type}`]) {
          rtn = values[group_key][`${name}_${type}`];
        } else {
          rtn = '';
        }
      } else {
        rtn = '';
      }
    } else if (values[group_key]) {
      if (values[group_key][`${name}_${type}`]) {
        rtn = values[group_key][`${name}_${type}`];
      } else {
        rtn = false;
      }
    } else {
      rtn = false;
    }
    return rtn;
  };
  const fieldValidation = (field: FIELD) => {
    const {
      type,
      input_type: inputType,
      validation_schema: validationSchema,
      label,
      is_iterative_or_not: isIterativeOrNot
    } = field;
    const error = formatText(removeHtmlTags(label), 'plain');
    let validation: any = Yup.string();
    switch (type) {
      case 'input':
        if (
          inputType === 'text' ||
          inputType === 'date' ||
          inputType === 'time' ||
          inputType === 'radio' ||
          inputType === 'email' ||
          inputType.includes('datetime')
        )
          validation = Yup.string();
        if (inputType === 'number') validation = Yup.number();
        if (inputType === 'checkbox') validation = Yup.array();
        if (inputType === 'email')
          validation = Yup.string().matches(
            /^[\w\-\\.!#$%&'*+\-=?^_`{|}~]{2,}@([\w-]+\.)+[\w-]{2,}$/,
            `${error} must be a valid email`
          );
        if (inputType === 'file')
          validation = Yup.object().shape({
            name: Yup.string(),
            file: Yup.string(),
            mimetype: Yup.string()
          });
        break;
      case 'toggle':
      case 'download':
      case 'select':
      case 'textarea':
      case 'signature':
        if (inputType === 'checkbox') {
          validation = Yup.array();
        } else {
          validation = Yup.string();
        }
        break;
      case 'image':
        if (inputType === 'file') validation = Yup.string();
        break;
      case 'address':
        if (
          inputType === 'country' ||
          inputType === 'state' ||
          inputType === 'city'
        )
          validation = Yup.string();
        if (inputType === 'zipcode')
          validation = Yup.string().matches(
            /^[\w\\.\s]{2,10}$/,
            `${error} must be a valid zipcode/postal code`
          );
        break;
      default:
        validation = Yup.string();
    }
    if (validationSchema.required && type?.toLowerCase() !== 'paragraph') {
      if (inputType === 'file' && type?.toLowerCase() === 'input') {
        validation = Yup.object().shape({
          name: Yup.string().required(`${error} is a required field`),
          file: Yup.string(),
          mimetype: Yup.string()
        });
      } else if (inputType === 'checkbox') {
        let min = 1;
        if (field.is_quiz_field) {
          switch (field.checkbox_answer_type?.toLowerCase()) {
            case 'equal to':
              min = field.checkbox_answer_limit || min;
              break;
            case 'at most':
            default:
              min = 1;
              break;
          }
        }
        let minError = `${error} is a required field`;
        if (min > 1) {
          minError = `Please check at least ${min} options`;
        }
        validation = Yup.array()
          ?.of(Yup.string().required(`${error} is a required field`))
          .min(min, minError);
      } else {
        validation = validation?.required(`${error} is a required field`);
      }
    } else {
      validation = validation?.nullable();
    }
    if (isIterativeOrNot) {
      return Yup.array()?.of(validation);
    }
    // if (
    //   validationSchema?.conditional_validation?.conditions[0] ===
    //     'isRequired' ||
    //   validationSchema?.conditional_validation?.conditions[1] === 'isRequired'
    // ) {
    //   // window.alert(Yup.string());
    //   validation = Yup.string();
    // }
    if (
      validationSchema?.conditional_validation?.conditions?.includes(
        'isRequired'
      )
    ) {
      validation = Yup.string().required(`${error} is a required field`);
    }

    return validation;
  };

  const fieldInitialValues = (field: FIELD, group_key: string) => {
    const {
      input_type: inputType,
      name,
      is_iterative_or_not: isIterativeOrNot
    } = field;
    const values = form?.values || {};
    if (inputType === 'checkbox') {
      return values[group_key] ? values[group_key][name] || [] : [];
    }
    if (isIterativeOrNot) {
      return values[group_key] ? values[group_key][name] || [''] : [''];
    }
    return values[group_key] ? values[group_key][name] || '' : '';
  };

  const initializeForms = () => {
    const init: any = {};
    const validations: any = {};
    form.groups?.forEach((group: GROUP) => {
      init[group.group_key] = {};
      const shape: any = {};
      validations[group.group_key] = {};
      if (group.is_iterative_or_not) {
        const values = form?.values || {};
        init[group.group_key] = values[group.group_key] || [
          generateEmptyObjectForArraySections(group)
        ];
      }
      group.fields?.forEach((field: FIELD) => {
        if (!group.is_iterative_or_not) {
          init[group.group_key][field.name] = fieldInitialValues(
            field,
            group.group_key
          );
          if (field.type === 'signature') {
            init[group.group_key][`${field.name}_agree`] = getSignatureValues(
              'agree',
              field.name,
              group.group_key
            );
            init[group.group_key][`${field.name}_date`] = getSignatureValues(
              'date',
              field.name,
              group.group_key
            );
          }
        }
        shape[field.name] = fieldValidation(field);
      });
      if (group.is_iterative_or_not) {
        validations[group.group_key] = Yup.array().of(
          Yup.object().shape(shape)
        );
      } else {
        validations[group.group_key] = Yup.object().shape(shape);
      }
    });
    return { init, validationsSchema: Yup.object(validations) };
  };
  const submitForm = async (values: any) => {
    const keys = Object.keys(values);
    let allObj: any = {};
    keys.forEach((key) => {
      const vobj = _.get(values, key);
      allObj = {
        ...allObj,
        ...vobj
      };
    });
    let checkedAllObj: any = {};
    _.map(allObj, (result, value) => {
      if (_.isObject(result) && !_.isArray(result)) {
        checkedAllObj = {
          ...checkedAllObj,
          ...result
        };
      } else {
        checkedAllObj = {
          ...checkedAllObj,
          [value]: result
        };
      }
    });
    const allValues = _.map(checkedAllObj, (result) => {
      return result ? result.length : 0;
    }).filter((value) => value !== 0);
    if (allValues.length) {
      if (id) {
        const data = await dispatch(
          validatepreviewform({
            id,
            data: {
              ...values
            }
          })
        );
        // setOpenSnackbar(true);
        if (data?.payload?.statusCode) {
          // setSnackbarMessage(data.payload.message);
          // setSnackbarSeverity('success');
          toast.error(data.payload.message);
        } else {
          // setSnackbarMessage(data.payload.message);
          // setSnackbarSeverity('success');
          // toast.success(data.payload.message);
        }
      }
    } else {
      // setOpenSnackbar(true);
      // setSnackbarMessage('Nothing to Save');
      // setSnackbarSeverity('info');
      toast.info('Nothing to Save');
    }
  };

  const getClassName = () => {
    if (previewType === 'mobile') return 'mobileView';
    if (previewType === 'tab') return 'tabView';
    return 'desktopView';
  };

  return (
    <Shell
      subMenu={
        <FormHeader
          type="preview-form"
          clickPreviewType={clickPreviewType}
          displayStylePanel
        />
      }
    >
      <Box
        className={getClassName()}
        sx={{
          display: 'block',
          margin: '20px auto',
          paddingTop: '50px',
          width: '100%'
        }}
      >
        <Box
          className="previewOuter"
          sx={{
            padding: previewType === 'computer' ? '0px 150px' : '0px 20px'
          }}
        >
          <Box>
            {activeTheme?.theme?.headerImage ? (
              <Card>
                <img src={image} className="img-container" alt="img" />
              </Card>
            ) : (
              <Card
                sx={{
                  backgroundColor:
                    activeTheme?.theme?.navigationColor || '#d4d4d4',
                  color: activeTheme?.theme?.navigationTextColor || '#000000',
                  padding: '20px'
                }}
              >
                <Box
                  className="textTransform-capitalize font-size-24 overflow-hidden"
                  sx={{
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis'
                  }}
                >
                  {form?.name}
                </Box>
                <Box className="font-size-14">{form?.description}</Box>
              </Card>
            )}

            <Card
              className="textFields"
              sx={{
                backgroundColor:
                  activeTheme?.theme?.backgroundColor || '#ffffff',
                // marginTop: "5px",
                padding: (() => {
                  if (previewType === 'mobile') return '25px';
                  if (previewType === 'tab') return '30px';
                  return '60px';
                })(),
                height: previewType === 'computer' ? 'auto' : '500px',
                overflowY: previewType === 'computer' ? 'hidden' : 'auto'
              }}
            >
              <div className="formik-container">
                {Object.keys(form).length > 0 && (
                  <AppForm
                    onSubmit={submitForm}
                    initialValues={initializeForms().init}
                    validationSchema={initializeForms().validationsSchema}
                  >
                    <FormSectionsAsPlain
                      form={form}
                      type={sectionViewType}
                      layout={tabletLayout}
                      activeTheme={activeTheme}
                      submitForm={submitForm}
                    />
                  </AppForm>
                )}
                {/* <SnackbarElement
                  message={snackbarMessage}
                  statusType={snackbarSeverity}
                  snackbarOpen={openSnackbar}
                  setSnackbarOpen={setOpenSnackbar}
                /> */}
              </div>
            </Card>
          </Box>
        </Box>
      </Box>
    </Shell>
  );
};
export default PreviewForm;
