import api, { handleError, apiRoutes, authHeaders } from './config';

export const getApps = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.apps}/me`, { headers });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
export const getApp = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.form}?app_id=${id}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getIndustryAppProcess = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(apiRoutes.industryAppProcess, { headers });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
export const createApp = async (
  payload: { id?: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const userType = localStorage.getItem('user_type');

    if (userType === 'organization') {
      const response = await api.post(
        `${apiRoutes.orgAppConfiguration}/${payload.id}`,
        payload.data,
        {
          headers
        }
      );
      return fulfillWithValue(response.data);
    }
    const response = await api.post(apiRoutes.apps, payload.data, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const updateApp = async (
  payload: { id: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const userType = localStorage.getItem('user_type');
    if (userType === 'organization') {
      const response = await api.patch(
        `${apiRoutes.orgAppConfiguration}/${payload.id}`,
        payload.data,
        {
          headers
        }
      );
      return fulfillWithValue(response.data);
    }
    const response = await api.put(
      `${apiRoutes.apps}/${payload.id}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const autoCreate = async (
  data: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post(`${apiRoutes.form}/auto-create`, data, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
export const autoCreateWithPdf = async (
  data: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    delete headers['Content-Type'];
    const response = await api.post(`${apiRoutes.form}/create-form-pdf`, data, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
export const getSubForms = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.form}/${id}/sub-forms`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
export const getFormData = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.form}/${id}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getAppData = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.apps}/${id}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getAppForms = async (
  payload: { id: any; organizationId?: string },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.apps}/forms/${payload.id}`, {
      params: { organization: payload.organizationId },
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const ChangeFormStatus = async (
  // id: any,
  payload: { formId: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.form}/${payload.formId}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getDashboardConfig = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(
      `${apiRoutes.orgAppDashboardConfiguration}/${id}`,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getPrimaryForm = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.primaryForm}?app_id=${id}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getAppsById = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.apps}/${id}`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const createDashboardConfig = async (
  payload: { id?: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post(
      `${apiRoutes.orgAppDashboardConfiguration}/${payload.id}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const updateDashboardConfig = async (
  payload: { id: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.orgAppDashboardConfiguration}/${payload.id}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
export const getDashboardConfigPreviewData = async (
  id: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(
      `${apiRoutes.orgAppDashboardConfiguration}/preview/${id}`,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
