import {
  InputBaseProps,
  CheckboxProps,
  RadioProps,
  SvgIconProps,
  SxProps
} from '@mui/material';
import * as Icons from '@mui/icons-material';
import { ReactSignatureCanvasProps } from 'react-signature-canvas';

type ObjectData = {
  label: string;
  value: string;
};

interface FormInputBaseType {
  label?: string | any;
  containerStyles?: any;
}

export type Menu = {
  name: string;
  to: string;
  icon: JSX.Element;
};

export interface Route {
  index?: boolean;
  path?: string;
  page: string;
  children?: Route[];
}

export interface IconProps {
  // name: any;
  name: keyof typeof Icons;
  color?: SvgIconProps['color'];
  fontSize?: SvgIconProps['fontSize'];
  style?: React.CSSProperties;
  sx?: React.CSSProperties;
  onClick?: any;
}

export interface FormTextareaProps {
  name: string;
  label: string;
  placeholder?: string;
  containerStyles?: SxProps;
  [x: string]: any; // To accept additional props
}

export interface FormInputType extends InputBaseProps, FormInputBaseType {
  name: string;
  isCreateForm?: boolean;
  handleInputChange?: any;
  activeTheme?: any;
  description?: any;
  required?: boolean;
}

export interface FormParagraphType {
  label: string | any;
  description: string;
  containerStyles?: any;
  activeTheme?: any;
}

export interface FormCheckboxType extends CheckboxProps, FormInputBaseType {
  name: string;
  data: ObjectData[];
  activeTheme?: any;
  description?: any;
  handleOptionChange?: any;
}

export interface FormSelectProps {
  name: any;
  data: { label: string; value: string | number }[];
  label?: any;
  description?: any;
  placeholder?: string;
  containerStyles?: object;
  labelStyles?: object;
  iconStyles?: object;
  menuStyles?: object;
  required?: boolean;
  [x: string]: any; // To accept additional props
}

export interface FormRadioType extends RadioProps, FormInputBaseType {
  name: string;
  data: ObjectData[];
  activeTheme?: any;
  description?: any;
  handleRadioChange?: any;
}

export interface FormToggleType extends FormInputBaseType {
  name: string;
  description?: any;
  data?: ObjectData[];
  isCreateForm?: any;
  activeTheme?: any;
  onChange?: any;
  handleOptionChange?: any;
}

export interface SignatureType extends ReactSignatureCanvasProps {
  onClear: () => void;
  onDone: (data: string | undefined, isEmpty: boolean | undefined) => void;
  agreement?: boolean;
}

export interface FormPhoneInputType extends FormInputBaseType {
  name: string;
  description?: any;
  placeholder?: string;
  activeTheme?: any;
  required?: boolean;
  handleMobileInputChange?: any;
}

export interface FormSignatureType
  extends ReactSignatureCanvasProps,
    FormInputBaseType {
  name: string;
  description?: any;
  imageProps?: {
    width: string | number;
    height: string | number;
  };
  dateName: string;
  agreeName: string;
  activeTheme?: any;
  onsubmit?: any;
}

export type FORMITEM = {
  name: string;
  form_id: string;
  completed: boolean;
};

export interface ATTRIBUTES {
  attr_type: string;
  attr_value: string;
}

export interface FIELD {
  dependencies: any;
  name: string;
  label: string;
  type: string;
  input_type: any;
  description?: string;
  description_status?: boolean;
  options: ObjectData[];
  validation_schema: any;
  original_field_id?: string;
  default_state?: string;
  attributes: ATTRIBUTES[];
  default_country?: string;
  is_iterative_or_not?: boolean;
  iteration_min_length?: number;
  iteration_max_length?: number;
  is_quiz_field: boolean;
  checkbox_answer_type: string;
  checkbox_answer_limit: number;
  value?: string;
  field_id?: any;
  label_url_type?: any;
  label_url?: any;
  conditions?: FieldCondition[];
  auto_fill_dependencies?: any[];
  signature_action_by?: string;
}
export interface FieldCondition {
  field_id: string;
  field_name?: string;
  field_value: any;
}

export interface APPFIELD {
  field: FIELD;
  index?: number;
  groupKey: string;
  style?: any;
  findex?: number;
  isCreateForm?: boolean;
  secIndex?: number;
  colIndex?: number;
  id?: any;
  activeTheme?: any;
}

export interface GROUP {
  fields: FIELD[];
  group_key: string;
  group_id?: string;
  group_index: string;
  group_title: string;
  group_description: string;
  is_iterative_or_not?: boolean;
  iteration_min_length?: number;
  iteration_max_length?: number;
}

export enum SECTIONVIEWTYPE {
  Plain = 'plain',
  Stepper = 'stepper',
  Accordion = 'accordion'
}

export enum LAYOUT {
  SingleColumn = 'singleColumn',
  DoubleColumn = 'doubleColumn'
}

export interface FORM {
  values: any;
  name: string;
  app_id: string;
  groups: GROUP[];
  form_id: string;
  status: boolean;
  completed: boolean;
  is_sub_form: boolean;
  has_sub_forms: boolean;
}

export enum BUTTONSTYLE {
  // solid = 'solid',
  // outline = 'outline',
  Rounded = 'rounded',
  Standard = 'standard'
}

export interface THEME {
  textColor: string;
  bodyFont: string;
  titleFont: string;
  linkColor: string;
  buttonColor: string;
  tabletLayout: LAYOUT;
  mobileLayout: LAYOUT;
  backgroundColor: string;
  buttonTextColor: string;
  navigationColor: string;
  buttonStyle: BUTTONSTYLE;
  navigationTextColor: string;
  sectionViewType: SECTIONVIEWTYPE;
}

export interface APPADDRESSSTATE {
  states: any[];
  cities: any[];
  countries: any[];
  currentCityCode: string;
  currentStateCode: string;
  currentCountryCode: string;
}

export interface APPREDUCERINITSTATE {
  errors: any;
  // currentTheme: THEME;
  isLoading: boolean;
  currentLocation: any;
  currentThemeMode: 'dark' | 'light';
  addressState: APPADDRESSSTATE;
  appDetails: {
    app_logo: string;
    app_code: string;
    app_name: string;
    app_process_code: string;
  };
  forms: FORMITEM[];
  form: FORM;
  isFormSubmiting: boolean;
}

export interface INDUSTRYAPPPROCESS {
  industry_app_process_id?: string;
  process_name?: string;
  process_code?: string;
  description?: string;
  status?: boolean;
  created_at?: Date;
  id?: number;
}

export interface INDUSTRYTYPE {
  industry_type_id?: string;
  industry_type?: string;
  description: string;
  status: boolean;
  created_at: Date;
  apps?: APPS[];
  industry_app_process?: INDUSTRYAPPPROCESS[];
  organizations?: ORGANIZATIONAPPLISTDETAILS[];
  length?: number | null;
  message?: string | null;
  type?: string | null;
}

export interface APPS {
  id?: number;
  app_id?: string;
  app_code?: string;
  name?: string;
  description?: string;
  status?: boolean;
  created_at?: Date;
  email?: string;
  industry_app_process?: INDUSTRYAPPPROCESS;
}

export interface ORGANIZATIONAPPLISTDETAILS {
  id?: number;
  organization_id?: string;
  name?: string;
  email?: string;
  mobile_number?: string;
  password?: string;
  status?: boolean;
  apps?: APPS[];
  industry_type?: INDUSTRYAPPPROCESS[];
  list?: object;
  length?: number;
  logo?: any;
}

export interface USERTYPE {
  userType: 'organization' | 'super_admin' | string | null;
}

export interface ORGDATA {
  apps?: APPS[];
  email?: string;
  industry_type?: INDUSTRYTYPE[];
  industry_type_id?: string;
  mobile_number?: string;
  name?: string;
  organization_id?: string;
  status?: boolean;
  message?: string;
  type?: string;
  logo?: string;
}

export interface OrganizationRegistrationFormProps {
  getappsData?: APPS[];
  selectedAppsList: APPS[];
  industryTypes: { label: string; value: string | number }[];
  formData: {
    name: string;
    logo: string;
    custom_name: string;
    email: string;
    password: string;
    industry_type_id: string;
    mobile_number: string;
  };
}

export type DashboardPanelComponentState = {
  id: number;
  type: string;
  width: string;
};

export type ParamsState = {
  appId: string;
  formId: string;
};
export type MetricsListComponentState = {
  id: number;
  type: string;
  width: string;
};

export type OrgAppListState = {
  name: string;
  icon: string | any;
  values: number;
};

export type SnabackBarState = {
  status: boolean;
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info';
};

export type SnackBarStoreState = {
  snackbarMessage: string;
  snackbarSeverity: 'success' | 'error' | 'warning' | 'info';
  snackbarOpen: boolean;
  setSnackbarOpen: any;
};

export type IndustryTypes = { label: string; value: string | number };

export type OrganizationsListProps = {
  orgData: ORGANIZATIONAPPLISTDETAILS[];
};

export type LoginAction = {
  status: boolean;
  userType: USERTYPE['userType'];
  loading: boolean;
};

export type HandleVideoAddTypes = {
  value1: boolean;
  value2: string;
};

export type ThemeBuilderTypes = {
  type: any;
  setActiveSelection?: any;
};

export type CreateFormTypes = {
  appId: any;
  appData: any;
};

export type CheckboxControlTypes = {
  value: string;
  checked: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  label: string;
};

export type RadioControlTypes = {
  value: string;
  checked: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  label: string;
};

export type ValidationInputTypes = {
  type: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur: () => void;
  error: string;
};

export type EmployeeOnboardingStepperModalProps = {
  currentStatus: string;
  handleClose: () => void;
  emp: any;
  stepperIntialValues: { interview_date: string; interview_time: string };
  setLocalEmployeeList: any;
};

export type EmployeeList = {
  bg_verification_document: string;
  email: string;
  form_filling_status: string;
  is_rejected: boolean;
  mobile_number: string;
  name: string;
  onboarding_employee_id: string;
  orientationResultString: string;
  orientation_status: string;
  scheduled_interviews: [
    { scheduled_interview_date: string; scheduled_interview_time: string }
  ];
  status: string;
};

export type EmployeeRowType = {
  id: string;
  name: string;
  mobile_number: number;
  email: string;
  form_filling_status: string;
  action: boolean;
  status: string;
  is_rejected: string;
};

export type RenderGroupTypes = {
  formData: any;
  group: GROUP;
  groupKey: string;
  groupsCount: number;
  groupTitle: string;
  groupDescription: string;
  groupIteration: string;
  groupMaxIterationLength: string;
  handleInputChange: any;
  secIndex: number;
};
export type EmployeeRowProps = {
  row: EmployeeRowType;
  steps: any;
  interviewCompleted: (data: any) => boolean;
  handlesetCurrentStatus?: (value: string) => void;
  handleRowClick: (_event: React.MouseEvent, id: string) => void;
  handleViewClick: (_event: React.MouseEvent, id: string) => void;
  handleEditClick: (_event: React.MouseEvent, id: string) => void;
  handleOnboardClick: (_event: React.MouseEvent, id: string) => void;
  handleReject: (_event: React.MouseEvent, status: string, id: string) => void;
};

export type FormContainerTypes = {
  showTemplate: any;
  setShowTemplate: any;
  selectedTemplate: any;
};

// EmploymentType is now:
type EmploymentType =
  | 'Full-time'
  | 'Part-time'
  | 'Contract'
  | 'Temporary'
  | 'Internship';

// JobStatus is now:
type JobStatus = 'Active' | 'Inactive' | 'Draft';

// export interface JobPostTypes {
//   id: number;
//   job_id: string;
//   title: string;
//   department?: string;
//   description?: string;
//   requirements?: any[];
//   requirements?: string;
//   salary_range?: string;
//   employment_type: EmploymentType;
//   location?: string;
//   status: JobStatus;
//   version: number;
//   application_start_date?: Date | string;
//   application_end_date?: Date | string;
//   organization: any;
//   onboarding_employees?: any[];
//   organization: Organization;
//   onboarding_employees?: OnboardingEmployee[];
//   created_at: Date | string;
//   updated_at: Date | string;
//   deleted_at?: Date | string;
// }
export interface JobPostTypes {
  title: string;
  department?: string;
  description?: string;
  requirements?: string[];
  salary_range?: string;
  employment_type: EmploymentType;
  location?: string;
  status: JobStatus;
  application_start_date?: Date | string;
  application_end_date?: Date | string;
  stages: any[];
}

export interface CreateStageInitForm {
  title: string;
  description: string;
  appId: string;
  formIds: string[];
}
